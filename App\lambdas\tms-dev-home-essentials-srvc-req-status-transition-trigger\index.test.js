const AWS = require("aws-sdk");

AWS.config.update({
  accessKeyId: "",
  secretAccessKey: "",
  region: "ap-south-1",
});

const lambda = new AWS.Lambda();

describe("Lambda Function Actual Call", () => {
  it("should invoke the actual Lambda function", async () => {
    const params = {
      FunctionName:
        "tms-dev-home-essentials-srvc-req-status-transition-trigger",
      InvocationType: "RequestResponse",
      Payload: "{}",
    };
    const response = await lambda.invoke(params).promise();
    expect(response.StatusCode).toBe(200); // Check HTTP status code
    const payload = JSON.parse(response.Payload);
    expect(payload).toBeDefined(); // Verify response exists
  });
});
