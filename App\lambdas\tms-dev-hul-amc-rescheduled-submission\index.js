const axios = require("axios");
const oauth = require("oauth-1.0a");
const crypto = require("crypto");

// Configuration for OAuth 1.0
const oauthConfig = {
  consumerKey:
    "3d579d4b66c8bcf4c86e4fb06f258adf2c67e9631a7fc0c17b676a3491081c54",
  consumerSecret:
    "677175d5d35ed51458c4518c709d44cdd10d16971e72c1887da5e9cc93614cac",
  accessToken:
    "856926f942b20a07d30e6fb02c06887532fe9c8f118425156d65676fe0b47066",
  accessTokenSecret:
    "8045e4be2ae953731ac0679b9db2d000ced82c7f1870d9b6aac1fb6800fc1ad9",
  realm: "3667364_SB1",
};

const filesPrefixUrl = "https://tms-hul-media-demo.wify.co.in/";

const logAxiosError = (error) => {
  if (error.response) {
    // The request was made and the server responded with a status code
    // that falls out of the range of 2xx
    console.log(error.response.data);
    console.log(error.response.status);
    console.log(error.response.headers);
  } else if (error.request) {
    // The request was made but no response was received
    // `error.request` is an instance of XMLHttpRequest in the browser and an instance of
    // http.ClientRequest in node.js
    console.log(error.request);
  } else {
    // Something happened in setting up the request that triggered an Error
    console.log("Error", error.message);
  }
  // console.log(error.config);
};

// console.log('request_data["headers"] ', request_data);
const makeRequest = async (data) => {
  // Create an OAuth 1.0 instance
  const oauthInstance = oauth({
    consumer: {
      key: oauthConfig.consumerKey,
      secret: oauthConfig.consumerSecret,
      realm: oauthConfig.realm,
    },
    signature_method: "HMAC-SHA256",
    hash_function(baseString, key) {
      return crypto
        .createHmac("SHA256", key)
        .update(baseString)
        .digest("base64");
    },
    realm: oauthConfig.realm,
  });

  const token = {
    key: oauthConfig.accessToken,
    secret: oauthConfig.accessTokenSecret,
  };
  const request_data = {
    url: "https://3667364-sb1.restlets.api.netsuite.com/app/site/hosting/restlet.nl?script=2499&deploy=1",
    method: "POST",
    maxBodyLength: Infinity,
    body: JSON.stringify(data),
  };
  // Generate OAuth 1.0 headers
  const oauthHeaders = oauthInstance.toHeader(
    oauthInstance.authorize(request_data, token)
  );
  const headers = {
    "Content-Type": "application/json",
    ...oauthHeaders,
  };
  return await axios.post(request_data.url, JSON.stringify(data), {
    headers,
  });
};

const handler = async (event) => {
  let responseStatus = false;
  let responseMessage = "Error please contact admin";

  console.log("event form_data", event.form_data);
  console.log("event request_data", event.request_data);

  const fieldVsKey = {
    netsuiteOrderId: "79a88c7b-c64f-46c4-a277-bc80efa1c154",
    date: "006b680a-2108-463d-8ecc-e43d3f84fdb1",
    start_time: "620569d8-9535-4a84-8446-9e04f25f5687",
    end_time: "406338ff-46e9-40ac-b5ce-4e4c8583feda",
  };
  const netsuiteOrderId = event.request_data[fieldVsKey.netsuiteOrderId];
  // console.log('netsuiteOrderId',netsuiteOrderId)

  const date = event.form_data[fieldVsKey.date];
  const start_time = event.form_data[fieldVsKey.start_time];
  const end_time = event.form_data[fieldVsKey.end_time];

  const attachments = event.form_data.attachments?.general || [];
  // const finalAttachments = attachments.map(url => filesPrefixUrl + url);

  let data = {
    request_type: "AMC_RESCHEDULE",
    date: date.split("T")[0],
    start_time: start_time,
    end_time: end_time,
    internal_id: netsuiteOrderId.split("/")[1],
    reason_for_reschedule: event.form_data.remarks,
  };

  console.log("Netsuite api call data", data);
  try {
    const response = await makeRequest(data);
    console.log("Netsuite api response", response.data); // Handle the API response data

    const netSuiteData = response?.data[0] || response.data;

    if (netSuiteData && netSuiteData.message) {
      let { message, responseCode, status } = netSuiteData;
      responseMessage = message;
      console.log(
        "message,responseCode,status : ",
        message,
        responseCode,
        status
      );
      if (status == "Success") {
        responseStatus = true; //
      } else {
        responseMessage = message;
      }
    } else {
      // this is a fatal error from netsuite
      // the response is not as per documentation rxd
      responseMessage = JSON.stringify(response.data);
    }
  } catch (error) {
    logAxiosError(error);
    // handle error
  }

  const response = {
    status: responseStatus,
    message: responseMessage,
  };
  return response;
};

// handler({});

exports.handler = handler;
