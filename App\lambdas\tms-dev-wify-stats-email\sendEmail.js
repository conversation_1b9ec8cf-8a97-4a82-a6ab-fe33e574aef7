const nodemailer = require("nodemailer");

const getEmailCredentials = () => {
  return {
    EMAIL_SMTP_HOST: process.env.EMAIL_SMTP_HOST || "email-smtp.ap-south-1.amazonaws.com",
    EMAIL_SMTP_PORT: process.env.EMAIL_SMTP_PORT || 465,
    EMAIL_AUTH_USER: process.env.EMAIL_AUTH_USER || "AKIAS2NDWJLSOTXFOSTZ",
    EMAIL_AUTH_PASS: process.env.EMAIL_AUTH_PASS || "BOqxSK7A6xNnri0puZ+FF60ArOeviv8WAUsZUTgxl7Lw",
    EMAIL_FROM: process.env.EMAIL_FROM || "<EMAIL>",
  };
};

exports.performSendEMAIL = async (data) => {
  let to = data?.to;
  let subject = `[TMS] ${data?.subject}`;
  let messageHTML = data?.message;
  //optional param for save email_log
  let cc = data?.cc || "";
  // let bcc = data?.bcc || '<EMAIL>';
  // let attachments = data?.attachments || '';
  let email_credential = getEmailCredentials();
  var smtpTransport = nodemailer.createTransport({
    host: email_credential.EMAIL_SMTP_HOST,
    port: email_credential.EMAIL_SMTP_PORT,
    auth: {
      user: email_credential.EMAIL_AUTH_USER,
      pass: email_credential.EMAIL_AUTH_PASS,
    },
  });

  const notifyData = {
    from: `WIFY - Do Not Reply <${email_credential.EMAIL_FROM}>`,
    to: to,
    cc: cc,
    // bcc: bcc,
    subject: subject,
    html: messageHTML,
    //attachments: attachments,
  };

  try {
    // send email
    let sendEmailResp = await smtpTransport.sendMail(notifyData);
    console.log("Email sent successfully");
    console.log("To Email ids", to);
    return {
      response: sendEmailResp,
      status: "Success",
    };
  } catch (error) {
    console.log("Email send error resp ==>", error);
    return {
      response: error,
      status: "Failed",
    };
  }
};
