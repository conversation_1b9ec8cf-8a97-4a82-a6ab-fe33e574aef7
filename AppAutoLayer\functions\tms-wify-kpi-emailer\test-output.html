
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Full Width Manager vs Category Table Test</title>
        <style>
            body {
                font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                line-height: 1.6;
                color: #333;
                max-width: 100%;
                margin: 0;
                padding: 10px;
                background-color: #f5f5f5;
            }
            .container {
                background-color: white;
                border-radius: 8px;
                box-shadow: 0 2px 10px rgba(0,0,0,0.1);
                overflow: hidden;
            }
            .header {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                padding: 30px;
                text-align: center;
            }
            .header h1 {
                margin: 0 0 10px 0;
                font-size: 28px;
                font-weight: 300;
            }
            .header p {
                margin: 0;
                opacity: 0.9;
                font-size: 16px;
            }
            .content {
                padding: 15px;
            }
            .summary-section {
                margin-bottom: 30px;
                padding: 20px;
                background-color: #f8f9fa;
                border-radius: 6px;
                border-left: 4px solid #667eea;
            }
            .summary-grid {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
                gap: 15px;
                margin-top: 15px;
            }
            .summary-item {
                background: white;
                padding: 15px;
                border-radius: 4px;
                border: 1px solid #e9ecef;
            }
            .data-table {
                width: 100%;
                border-collapse: collapse;
                margin: 20px 0;
                font-size: 14px;
            }
            .data-table th {
                background-color: #007bff;
                color: black;
                font-weight: bold;
                text-transform: uppercase;
                font-size: 12px;
                letter-spacing: 0.5px;
            }
            .data-table tr:nth-child(even) {
                background-color: #f8f9fa;
            }
            .data-table tr:hover {
                background-color: #e9ecef;
            }
            .table-container {
                overflow-x: auto;
                margin-top: 20px;
                border-radius: 6px;
                box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            }
            .footer {
                margin-top: 30px;
                padding-top: 20px;
                border-top: 1px solid #dee2e6;
                text-align: center;
                color: #6c757d;
                font-size: 12px;
            }
            .stats {
                background-color: #e7f3ff;
                padding: 15px;
                border-radius: 5px;
                margin: 20px 0;
                text-align: center;
            }
            .timestamp {
                font-weight: 600;
                color: #495057;
            }
            @media (max-width: 768px) {
                body {
                    padding: 10px;
                }
                .header {
                    padding: 20px;
                }
                .header h1 {
                    font-size: 24px;
                }
                .content {
                    padding: 20px;
                }
                .summary-grid {
                    grid-template-columns: 1fr;
                }
                table {
                    font-size: 12px;
                }
                th, td {
                    padding: 8px 6px;
                }
            }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>Full Width Manager vs Category Table Test</h1>
                <p>Testing the updated table with all columns and full width styling</p>
            </div>
            
            <div class="content">
                <div class="summary-section"><h3>📊 Executive Summary</h3><p style="line-height: 1.6; font-size: 15px; color: #495057;">This is a test of the updated manager vs category table with all KPI columns.</p></div>

                
    <div class="manager-category-section" style="margin: 30px 0; width: 100%;">
      <h3 style="color: #495057; margin-bottom: 15px;">📋 Manager vs Category Performance Overview</h3>
      <p style="color: #6c757d; margin-bottom: 20px; font-size: 14px;">
        Aggregated KPI metrics grouped by category managers and their assigned verticals
      </p>
      <div class="table-container" style="overflow-x: auto; border-radius: 6px; box-shadow: 0 1px 3px rgba(0,0,0,0.1); width: 100%;">
        <table style="width: 100%; border-collapse: collapse; font-size: 12px; min-width: 1200px;">
          <thead>
            <tr><th style="padding: 8px; background-color: #f8f9fa; border: 1px solid #dee2e6; text-align: left; font-weight: bold; font-size: 11px; white-space: nowrap;">Manager</th><th style="padding: 8px; background-color: #f8f9fa; border: 1px solid #dee2e6; text-align: left; font-weight: bold; font-size: 11px; white-space: nowrap;">Categories</th><th style="padding: 8px; background-color: #f8f9fa; border: 1px solid #dee2e6; text-align: left; font-weight: bold; font-size: 11px; white-space: nowrap;">Verticals</th><th style="padding: 8px; background-color: #f8f9fa; border: 1px solid #dee2e6; text-align: left; font-weight: bold; font-size: 11px; white-space: nowrap;">All Time Orders</th><th style="padding: 8px; background-color: #f8f9fa; border: 1px solid #dee2e6; text-align: left; font-weight: bold; font-size: 11px; white-space: nowrap;">Open Orders</th><th style="padding: 8px; background-color: #f8f9fa; border: 1px solid #dee2e6; text-align: left; font-weight: bold; font-size: 11px; white-space: nowrap;">New Orders</th><th style="padding: 8px; background-color: #f8f9fa; border: 1px solid #dee2e6; text-align: left; font-weight: bold; font-size: 11px; white-space: nowrap;">Orders Closed</th><th style="padding: 8px; background-color: #f8f9fa; border: 1px solid #dee2e6; text-align: left; font-weight: bold; font-size: 11px; white-space: nowrap;">Tech Inch.</th><th style="padding: 8px; background-color: #f8f9fa; border: 1px solid #dee2e6; text-align: left; font-weight: bold; font-size: 11px; white-space: nowrap;">Orders Sch.</th><th style="padding: 8px; background-color: #f8f9fa; border: 1px solid #dee2e6; text-align: left; font-weight: bold; font-size: 11px; white-space: nowrap;">Orders Sch. Yest.</th><th style="padding: 8px; background-color: #f8f9fa; border: 1px solid #dee2e6; text-align: left; font-weight: bold; font-size: 11px; white-space: nowrap;">Tasks Created</th><th style="padding: 8px; background-color: #f8f9fa; border: 1px solid #dee2e6; text-align: left; font-weight: bold; font-size: 11px; white-space: nowrap;">Assignment Rate</th><th style="padding: 8px; background-color: #f8f9fa; border: 1px solid #dee2e6; text-align: left; font-weight: bold; font-size: 11px; white-space: nowrap;">Supp. Depl</th><th style="padding: 8px; background-color: #f8f9fa; border: 1px solid #dee2e6; text-align: left; font-weight: bold; font-size: 11px; white-space: nowrap;">Supply All Task Closed</th><th style="padding: 8px; background-color: #f8f9fa; border: 1px solid #dee2e6; text-align: left; font-weight: bold; font-size: 11px; white-space: nowrap;">Supply No Task Update</th><th style="padding: 8px; background-color: #f8f9fa; border: 1px solid #dee2e6; text-align: left; font-weight: bold; font-size: 11px; white-space: nowrap;">Supply Partial Task Update</th><th style="padding: 8px; background-color: #f8f9fa; border: 1px solid #dee2e6; text-align: left; font-weight: bold; font-size: 11px; white-space: nowrap;">Active Supply</th><th style="padding: 8px; background-color: #f8f9fa; border: 1px solid #dee2e6; text-align: left; font-weight: bold; font-size: 11px; white-space: nowrap;">New Supply</th><th style="padding: 8px; background-color: #f8f9fa; border: 1px solid #dee2e6; text-align: left; font-weight: bold; font-size: 11px; white-space: nowrap;">Inactive Supply</th><th style="padding: 8px; background-color: #f8f9fa; border: 1px solid #dee2e6; text-align: left; font-weight: bold; font-size: 11px; white-space: nowrap;">Deployment Rate</th></tr>
          </thead>
          <tbody>
            <tr><td style="padding: 8px; border: 1px solid #dee2e6; font-size: 11px; white-space: nowrap;">Harish</td><td style="padding: 8px; border: 1px solid #dee2e6; font-size: 11px; white-space: nowrap;">MKW Installation, MKW Services, Doors Installation, Civil Interior, Grant Neo, Flooring, Window, Parts, MKW Online Services, MKW-LF</td><td style="padding: 8px; border: 1px solid #dee2e6; font-size: 11px; white-space: nowrap;">2</td><td style="padding: 8px; border: 1px solid #dee2e6; font-size: 11px; white-space: nowrap;">2,140</td><td style="padding: 8px; border: 1px solid #dee2e6; font-size: 11px; white-space: nowrap;">77</td><td style="padding: 8px; border: 1px solid #dee2e6; font-size: 11px; white-space: nowrap;">13</td><td style="padding: 8px; border: 1px solid #dee2e6; font-size: 11px; white-space: nowrap;">8</td><td style="padding: 8px; border: 1px solid #dee2e6; font-size: 11px; white-space: nowrap;">5</td><td style="padding: 8px; border: 1px solid #dee2e6; font-size: 11px; white-space: nowrap;">20</td><td style="padding: 8px; border: 1px solid #dee2e6; font-size: 11px; white-space: nowrap;">17</td><td style="padding: 8px; border: 1px solid #dee2e6; font-size: 11px; white-space: nowrap;">25</td><td style="padding: 8px; border: 1px solid #dee2e6; font-size: 11px; white-space: nowrap;">22.2%</td><td style="padding: 8px; border: 1px solid #dee2e6; font-size: 11px; white-space: nowrap;">33</td><td style="padding: 8px; border: 1px solid #dee2e6; font-size: 11px; white-space: nowrap;">22</td><td style="padding: 8px; border: 1px solid #dee2e6; font-size: 11px; white-space: nowrap;">3</td><td style="padding: 8px; border: 1px solid #dee2e6; font-size: 11px; white-space: nowrap;">8</td><td style="padding: 8px; border: 1px solid #dee2e6; font-size: 11px; white-space: nowrap;">45</td><td style="padding: 8px; border: 1px solid #dee2e6; font-size: 11px; white-space: nowrap;">3</td><td style="padding: 8px; border: 1px solid #dee2e6; font-size: 11px; white-space: nowrap;">8</td><td style="padding: 8px; border: 1px solid #dee2e6; font-size: 11px; white-space: nowrap;">73.3%</td></tr>
          </tbody>
        </table>
      </div>
    </div>
  

                <div class="table-container">
                    <table class="data-table">
                        <thead>
                            <tr><th style="padding: 12px; background-color: #f8f9fa; border: 1px solid #dee2e6; text-align: left; font-weight: bold;">Title</th><th style="padding: 12px; background-color: #f8f9fa; border: 1px solid #dee2e6; text-align: left; font-weight: bold;">All time orders</th><th style="padding: 12px; background-color: #f8f9fa; border: 1px solid #dee2e6; text-align: left; font-weight: bold;">Open orders</th><th style="padding: 12px; background-color: #f8f9fa; border: 1px solid #dee2e6; text-align: left; font-weight: bold;">New orders</th><th style="padding: 12px; background-color: #f8f9fa; border: 1px solid #dee2e6; text-align: left; font-weight: bold;">Orders closed</th><th style="padding: 12px; background-color: #f8f9fa; border: 1px solid #dee2e6; text-align: left; font-weight: bold;">Tech Inch.</th><th style="padding: 12px; background-color: #f8f9fa; border: 1px solid #dee2e6; text-align: left; font-weight: bold;">Orders Sch.</th><th style="padding: 12px; background-color: #f8f9fa; border: 1px solid #dee2e6; text-align: left; font-weight: bold;">Orders Sch. for yest. </th><th style="padding: 12px; background-color: #f8f9fa; border: 1px solid #dee2e6; text-align: left; font-weight: bold;">Tasks created</th><th style="padding: 12px; background-color: #f8f9fa; border: 1px solid #dee2e6; text-align: left; font-weight: bold;">Supp. depl</th><th style="padding: 12px; background-color: #f8f9fa; border: 1px solid #dee2e6; text-align: left; font-weight: bold;">Supply all task closed</th><th style="padding: 12px; background-color: #f8f9fa; border: 1px solid #dee2e6; text-align: left; font-weight: bold;">Supply no task update</th><th style="padding: 12px; background-color: #f8f9fa; border: 1px solid #dee2e6; text-align: left; font-weight: bold;">Supply partial task update</th><th style="padding: 12px; background-color: #f8f9fa; border: 1px solid #dee2e6; text-align: left; font-weight: bold;">Active supply</th><th style="padding: 12px; background-color: #f8f9fa; border: 1px solid #dee2e6; text-align: left; font-weight: bold;">New supply</th><th style="padding: 12px; background-color: #f8f9fa; border: 1px solid #dee2e6; text-align: left; font-weight: bold;">Inactive supply</th></tr>
                        </thead>
                        <tbody>
                            <tr><td style="padding: 12px; border: 1px solid #dee2e6;">MKW Installation</td><td style="padding: 12px; border: 1px solid #dee2e6;">1250</td><td style="padding: 12px; border: 1px solid #dee2e6; color: #dc3545; font-weight: bold;">45</td><td style="padding: 12px; border: 1px solid #dee2e6;">8</td><td style="padding: 12px; border: 1px solid #dee2e6;">5</td><td style="padding: 12px; border: 1px solid #dee2e6;">3</td><td style="padding: 12px; border: 1px solid #dee2e6;">12</td><td style="padding: 12px; border: 1px solid #dee2e6;">10</td><td style="padding: 12px; border: 1px solid #dee2e6;">15</td><td style="padding: 12px; border: 1px solid #dee2e6;">18</td><td style="padding: 12px; border: 1px solid #dee2e6;">12</td><td style="padding: 12px; border: 1px solid #dee2e6;">2</td><td style="padding: 12px; border: 1px solid #dee2e6;">4</td><td style="padding: 12px; border: 1px solid #dee2e6;">25</td><td style="padding: 12px; border: 1px solid #dee2e6;">2</td><td style="padding: 12px; border: 1px solid #dee2e6;">5</td></tr><tr><td style="padding: 12px; border: 1px solid #dee2e6;">MKW Services</td><td style="padding: 12px; border: 1px solid #dee2e6;">890</td><td style="padding: 12px; border: 1px solid #dee2e6; color: #dc3545; font-weight: bold;">32</td><td style="padding: 12px; border: 1px solid #dee2e6;">5</td><td style="padding: 12px; border: 1px solid #dee2e6;">3</td><td style="padding: 12px; border: 1px solid #dee2e6;">2</td><td style="padding: 12px; border: 1px solid #dee2e6;">8</td><td style="padding: 12px; border: 1px solid #dee2e6;">7</td><td style="padding: 12px; border: 1px solid #dee2e6;">10</td><td style="padding: 12px; border: 1px solid #dee2e6;">15</td><td style="padding: 12px; border: 1px solid #dee2e6;">10</td><td style="padding: 12px; border: 1px solid #dee2e6;">1</td><td style="padding: 12px; border: 1px solid #dee2e6;">4</td><td style="padding: 12px; border: 1px solid #dee2e6;">20</td><td style="padding: 12px; border: 1px solid #dee2e6;">1</td><td style="padding: 12px; border: 1px solid #dee2e6;">3</td></tr>
                            <tr style="border-top: 3px solid #007bff;"><td style="padding: 12px; border: 2px solid #007bff; background-color: #e7f3ff; font-weight: bold; text-align: center;">TOTAL</td><td style="padding: 12px; border: 2px solid #007bff; background-color: #e7f3ff; font-weight: bold; text-align: center;">2,140</td><td style="padding: 12px; border: 2px solid #007bff; background-color: #e7f3ff; font-weight: bold; text-align: center;">77</td><td style="padding: 12px; border: 2px solid #007bff; background-color: #e7f3ff; font-weight: bold; text-align: center;">13</td><td style="padding: 12px; border: 2px solid #007bff; background-color: #e7f3ff; font-weight: bold; text-align: center;">8</td><td style="padding: 12px; border: 2px solid #007bff; background-color: #e7f3ff; font-weight: bold; text-align: center;">5</td><td style="padding: 12px; border: 2px solid #007bff; background-color: #e7f3ff; font-weight: bold; text-align: center;">20</td><td style="padding: 12px; border: 2px solid #007bff; background-color: #e7f3ff; font-weight: bold; text-align: center;">17</td><td style="padding: 12px; border: 2px solid #007bff; background-color: #e7f3ff; font-weight: bold; text-align: center;">25</td><td style="padding: 12px; border: 2px solid #007bff; background-color: #e7f3ff; font-weight: bold; text-align: center;">33</td><td style="padding: 12px; border: 2px solid #007bff; background-color: #e7f3ff; font-weight: bold; text-align: center;">22</td><td style="padding: 12px; border: 2px solid #007bff; background-color: #e7f3ff; font-weight: bold; text-align: center;">3</td><td style="padding: 12px; border: 2px solid #007bff; background-color: #e7f3ff; font-weight: bold; text-align: center;">8</td><td style="padding: 12px; border: 2px solid #007bff; background-color: #e7f3ff; font-weight: bold; text-align: center;">45</td><td style="padding: 12px; border: 2px solid #007bff; background-color: #e7f3ff; font-weight: bold; text-align: center;">3</td><td style="padding: 12px; border: 2px solid #007bff; background-color: #e7f3ff; font-weight: bold; text-align: center;">8</td></tr>
                        </tbody>
                    </table>
                </div>
            </div>
            
            <div class="footer">
                <p>Generated on: <span class="timestamp">2025-07-01 17:48:52 IST</span></p>
                <p>Total Records: <strong>2</strong></p>
            </div>
        </div>
    </body>
    </html>
  