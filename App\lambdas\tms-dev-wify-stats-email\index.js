const { Client } = require("pg");
const { performSendEMAIL } = require("./sendEmail");

async function getDbClient() {
  const dbClient = new Client({
    host: process.env.DB_HOST,
    database: process.env.DB_NAME,
    user: process.env.DB_USER,
    password: process.env.DB_PASSWORD,
    port: 5432,
  });

  try {
    await dbClient.connect();
    console.log("Connected to the database!");
    return dbClient;
  } catch (error) {
    console.error("Error connecting to the database:", error);
    throw error;
  }
}

async function getTotalUsers(startDate, endDate) {
  const dbClient = await getDbClient();
  try {
    if (!dbClient._connected) {
      await dbClient.connect();
      console.log("getTotalUsers :: Connected to the database!");
    } else {
      console.log("getTotalUsers :: Already connected to the database!");
    }

    const formatEndDate = new Date(endDate).toISOString();

    const res = await dbClient.query(
      `select count(distinct users.usr_id)
             from cl_tx_users as users
            inner join cl_tx_usr_identities as usr_identities
               on usr_identities.user_id = users.usr_id
            where is_active is true
              and Date(((users.c_meta).time)::timestamp at time zone $$utc$$ at time zone $$Asia/kolkata$$) <= date(($1)::timestamp at time zone $$utc$$ at time zone $$Asia/kolkata$$)`,
      [formatEndDate]
    );

    const resp = res.rows[0].count;

    console.log("getTotalUsers :: resp :: ", resp);
    return resp;
  } catch (error) {
    console.error("getTotalUsers :: error ::", error);
  }
}

async function getNewUsers(startDate, endDate) {
  const dbClient = await getDbClient();
  try {
    if (!dbClient._connected) {
      await dbClient.connect();
      console.log("getNewUsers :: Connected to the database!");
    } else {
      console.log("getNewUsers :: Already connected to the database!");
    }
    const formatStartDate = new Date(startDate).toISOString();
    const formatEndDate = new Date(endDate).toISOString();

    const res = await dbClient.query(
      `select count(distinct users.usr_id)
             from cl_tx_users as users
            inner join cl_tx_usr_identities as usr_identities
               on usr_identities.user_id = users.usr_id
            where is_active is true
              and Date(((users.c_meta).time)::timestamp at time zone $$utc$$ at time zone $$Asia/kolkata$$) >= date(($1)::timestamp at time zone $$utc$$ at time zone $$Asia/kolkata$$) 
              and Date(((users.c_meta).time)::timestamp at time zone $$utc$$ at time zone $$Asia/kolkata$$) <= date(($2)::timestamp at time zone $$utc$$ at time zone $$Asia/kolkata$$)`,
      [formatStartDate, formatEndDate]
    );

    const resp = res.rows[0].count;

    console.log("getNewUsers :: resp :: ", resp);
    return resp;
  } catch (error) {
    console.error("getNewUsers :: error ::", error);
  }
}

async function getTotalOrdersAssignedToWIFY(startDate, endDate) {
  const dbClient = await getDbClient();
  try {
    if (!dbClient._connected) {
      await dbClient.connect();
      console.log("getTotalOrdersAssignedToWIFY :: Connected to the database!");
    } else {
      console.log(
        "getTotalOrdersAssignedToWIFY :: Already connected to the database!"
      );
    }
    const formatStartDate = new Date(startDate).toISOString();
    const formatEndDate = new Date(endDate).toISOString();

    const res = await dbClient.query(
      `  select count(distinct srvc_req.db_id)
           from public.cl_tx_srvc_req srvc_req 
          inner join cl_tx_srvc_req_trnstn_log as trnstn_log
             on trnstn_log.srvc_req_id = srvc_req.db_id
            and trnstn_log.status_key = 'open'
          inner join cl_cf_srvc_statuses as srvc_status
             on srvc_status.status_key = srvc_req.status
            and srvc_status.srvc_id = srvc_req.srvc_type_id
          inner join cl_tx_orgs as org
             on org.org_id = srvc_req.org_id    
          where srvc_req.is_deleted is not true  
            and srvc_req.srvc_prvdr = '2' 
            and DATE((srvc_req.srvc_prvdr_assg_time)::timestamp at time zone $$utc$$ at time zone $$Asia/kolkata$$ ) >= DATE(($1)::timestamp at time zone $$utc$$ at time zone $$Asia/kolkata$$)  
            and DATE((srvc_req.srvc_prvdr_assg_time)::timestamp at time zone $$utc$$ at time zone $$Asia/kolkata$$ ) <= DATE(($2)::timestamp at time zone $$utc$$ at time zone $$Asia/kolkata$$)  
`,
      [formatStartDate, formatEndDate]
    );

    const resp = res.rows[0].count;

    console.log("getTotalOrdersAssignedToWIFY :: resp :: ", resp);
    return resp;
  } catch (error) {
    console.error("getTotalOrdersAssignedToWIFY :: error ::", error);
  }
}

async function getTotalOrdersAssignedToWIFYWithOneSubtask(startDate, endDate) {
  const dbClient = await getDbClient();
  try {
    if (!dbClient._connected) {
      await dbClient.connect();
      console.log(
        "getTotalOrdersAssignedToWIFYWithOneSubtask :: Connected to the database!"
      );
    } else {
      console.log(
        "getTotalOrdersAssignedToWIFYWithOneSubtask :: Already connected to the database!"
      );
    }
    const formatStartDate = new Date(startDate).toISOString();
    const formatEndDate = new Date(endDate).toISOString();
    console.log("formatStartDate", formatStartDate),
      console.log("formatEndDate", formatEndDate);

    const res = await dbClient.query(
      `  
          select count(distinct srvc_req.db_id)
            from public.cl_tx_srvc_req srvc_req 
           inner join cl_tx_srvc_req_trnstn_log as trnstn_log
              on trnstn_log.srvc_req_id = srvc_req.db_id
            and trnstn_log.status_key = 'open'
           inner join cl_cf_srvc_statuses as srvc_status
              on srvc_status.status_key = srvc_req.status
             and srvc_status.srvc_id = srvc_req.srvc_type_id
           inner join cl_tx_orgs as org
              on org.org_id = srvc_req.org_id    
           inner join cl_tx_sbtsk as sbtsk_fr_today
              on sbtsk_fr_today.db_id = (
                    select db_id
                  from cl_tx_sbtsk
                    where srvc_req_id = srvc_req.db_id
                        and date((start_time)::timestamp at time zone $$utc$$ at time zone $$Asia/kolkata$$)  >= DATE(($1)::timestamp at time zone $$utc$$ at time zone $$Asia/kolkata$$)  
                        and date((end_time)::timestamp at time zone $$utc$$ at time zone $$Asia/kolkata$$)  <= DATE(($2)::timestamp at time zone $$utc$$ at time zone $$Asia/kolkata$$)  
                        and is_deleted is not true
                        and org_id = 2
                    limit 1
            )
          where srvc_req.is_deleted is not true  
            and srvc_req.srvc_prvdr = '2' 
            and DATE((srvc_req.srvc_prvdr_assg_time)::timestamp at time zone $$utc$$ at time zone $$Asia/kolkata$$ ) >= DATE(($1)::timestamp at time zone $$utc$$ at time zone $$Asia/kolkata$$)  
            and DATE((srvc_req.srvc_prvdr_assg_time)::timestamp at time zone $$utc$$ at time zone $$Asia/kolkata$$ ) <= DATE(($2)::timestamp at time zone $$utc$$ at time zone $$Asia/kolkata$$)  
`,
      [formatStartDate, formatEndDate]
    );

    const resp = res.rows[0].count;

    console.log("getTotalOrdersAssignedToWIFYWithOneSubtask :: resp :: ", resp);
    return resp;
  } catch (error) {
    console.error(
      "getTotalOrdersAssignedToWIFYWithOneSubtask :: error ::",
      error
    );
  }
}

async function getActiveTechnicianWithOneSbtsk(startDate, endDate) {
  const dbClient = await getDbClient();
  try {
    if (!dbClient._connected) {
      await dbClient.connect();
      console.log(
        "getActiveTechnicianWithOneSbtsk :: Connected to the database!"
      );
    } else {
      console.log(
        "getActiveTechnicianWithOneSbtsk :: Already connected to the database!"
      );
    }
    const formatStartDate = new Date(startDate).toISOString();
    const formatEndDate = new Date(endDate).toISOString();

    const res = await dbClient.query(
      `
        select count (distinct users.usr_id)
          from cl_tx_users as users
         inner join cl_tx_usr_roles as role_
            on role_.user_id = users.usr_id
           and role_.role_id = any(ARRAY[162, 426, 427, 424, 423, 1450, 441, 446, 821, 425, 507, 1355, 1356, 276, 218, 193, 725, 821, 158, 430])--ARRAY[162, 426, 427, 424, 423, 1450, 441, 446, 821, 425, 507, 1355, 1356, 276, 218, 193, 725, 821, 158, 430]
         inner join cl_tx_sbtsk as sbtsk
            on users.usr_id = any(sbtsk.assigned_to)
           and date((start_time)::timestamp at time zone $$utc$$ at time zone $$Asia/kolkata$$) >= DATE(($1)::timestamp at time zone $$utc$$ at time zone $$Asia/kolkata$$)  
           and date((end_time)::timestamp at time zone $$utc$$ at time zone $$Asia/kolkata$$) <= DATE(($2)::timestamp at time zone $$utc$$ at time zone $$Asia/kolkata$$)  
           and sbtsk.is_deleted is not true
         where users.org_id = 2
          and Date(((users.c_meta).time)::timestamp at time zone $$utc$$ at time zone $$Asia/kolkata$$) <= date(($1)::timestamp at time zone $$utc$$ at time zone $$Asia/kolkata$$)          
         `,
      [formatStartDate, formatEndDate]
    );

    const resp = res.rows[0].count;

    console.log("getActiveTechnicianWithOneSbtsk :: resp :: ", resp);
    return resp;
  } catch (error) {
    console.error("getActiveTechnicianWithOneSbtsk :: error ::", error);
  }
}

async function getTotalActiveUserOfWifySP(startDate, endDate) {
  const dbClient = await getDbClient();
  try {
    if (!dbClient._connected) {
      await dbClient.connect();
      console.log("getTotalActiveUserOfWifySP :: Connected to the database!");
    } else {
      console.log(
        "getTotalActiveUserOfWifySP :: Already connected to the database!"
      );
    }
    const formatStartDate = new Date(startDate).toISOString();
    const formatEndDate = new Date(endDate).toISOString();

    const res = await dbClient.query(
      `
        select count(distinct technician.usr_id)
          from cl_tx_users as technician
         inner join cl_tx_usr_roles as role_
            on role_.user_id = technician.usr_id
           and role_.role_id = any(ARRAY[162, 426, 427, 424, 423, 1450, 441, 446, 821, 425, 507, 1355, 1356, 276, 218, 193, 725, 821, 158, 430])--ARRAY[162, 426, 427, 424, 423, 1450, 441, 446, 821, 425, 507, 1355, 1356, 276, 218, 193, 725, 821, 158, 430]
         inner join cl_cf_roles as role_type
            on role_type.role_id = role_.role_id
         where technician.is_active is true
           and technician.org_id = 2
           and Date(((technician.c_meta).time)::timestamp at time zone $$utc$$ at time zone $$Asia/kolkata$$) <= date(($1)::timestamp at time zone $$utc$$ at time zone $$Asia/kolkata$$)          
        
         `,
      [formatEndDate]
    );

    const resp = res.rows[0].count;

    console.log("getTotalActiveUserOfWifySP :: resp :: ", resp);
    return resp;
  } catch (error) {
    console.error("getTotalActiveUserOfWifySP :: error ::", error);
  }
}

async function getNumberOfSbtskWithInRange(startDate, endDate) {
  const dbClient = await getDbClient();
  try {
    if (!dbClient._connected) {
      await dbClient.connect();
      console.log("getNumberOfSbtskWithInRange :: Connected to the database!");
    } else {
      console.log(
        "getNumberOfSbtskWithInRange :: Already connected to the database!"
      );
    }
    const formatStartDate = new Date(startDate).toISOString();
    const formatEndDate = new Date(endDate).toISOString();

    const res = await dbClient.query(
      `
        select count(db_id)
          from cl_tx_sbtsk
         where date((start_time)::timestamp at time zone $$utc$$ at time zone $$Asia/kolkata$$) >= DATE(($1)::timestamp at time zone $$utc$$ at time zone $$Asia/kolkata$$)  
           and date((start_time)::timestamp at time zone $$utc$$ at time zone $$Asia/kolkata$$) <= DATE(($2)::timestamp at time zone $$utc$$ at time zone $$Asia/kolkata$$)  
           and is_deleted is not true
           and org_id = 2
          
         `,
      [formatStartDate, formatEndDate]
    );

    const resp = res.rows[0].count;

    console.log("getNumberOfSbtskWithInRange :: resp :: ", resp);
    return resp;
  } catch (error) {
    console.error("getNumberOfSbtskWithInRange :: error ::", error);
  }
}

async function getNumberOfClosedSbtskWithInRange(startDate, endDate) {
  const dbClient = await getDbClient();
  try {
    if (!dbClient._connected) {
      await dbClient.connect();
      console.log(
        "getNumberOfClosedSbtskWithInRange :: Connected to the database!"
      );
    } else {
      console.log(
        "getNumberOfClosedSbtskWithInRange :: Already connected to the database!"
      );
    }
    const formatStartDate = new Date(startDate).toISOString();
    const formatEndDate = new Date(endDate).toISOString();

    const res = await dbClient.query(
      `
         select count(sbtsk_.db_id)
           from cl_tx_sbtsk as sbtsk_
          inner join cl_cf_sbtsk_statuses as sbtsk_status_ 
             on sbtsk_status_.sbtsk_type_id = sbtsk_.sbtsk_type 
            and sbtsk_status_.status_key = sbtsk_.status
          inner join cl_tx_sbtsk_trnstn_log as closed_sbtsk 
             on closed_sbtsk.sbtsk_id = sbtsk_.db_id 
            and closed_sbtsk.status_key = sbtsk_.status
          where sbtsk_.status = 'closed'
            and date((sbtsk_.start_time)::timestamp at time zone $$utc$$ at time zone $$Asia/kolkata$$) >= DATE(($1)::timestamp at time zone $$utc$$ at time zone $$Asia/kolkata$$)  
            and date((sbtsk_.end_time)::timestamp at time zone $$utc$$ at time zone $$Asia/kolkata$$) <= DATE(($2)::timestamp at time zone $$utc$$ at time zone $$Asia/kolkata$$)  
            and date((closed_sbtsk.trnstn_date)::timestamp at time zone $$utc$$ at time zone $$Asia/kolkata$$) >= DATE(($1)::timestamp at time zone $$utc$$ at time zone $$Asia/kolkata$$)  
            and date((closed_sbtsk.trnstn_date)::timestamp at time zone $$utc$$ at time zone $$Asia/kolkata$$) <= DATE(($2)::timestamp at time zone $$utc$$ at time zone $$Asia/kolkata$$)  
            and sbtsk_.is_deleted is not true  
            and sbtsk_.org_id = 2
          
         `,
      [formatStartDate, formatEndDate]
    );

    const resp = res.rows[0].count;

    console.log("getNumberOfClosedSbtskWithInRange :: resp :: ", resp);
    return resp;
  } catch (error) {
    console.error("getNumberOfClosedSbtskWithInRange :: error ::", error);
  }
}

async function getClosedReqWithRating(startDate, endDate) {
  const dbClient = await getDbClient();
  try {
    if (!dbClient._connected) {
      await dbClient.connect();
      console.log("getClosedReqWithRating :: Connected to the database!");
    } else {
      console.log(
        "getClosedReqWithRating :: Already connected to the database!"
      );
    }
    const formatStartDate = new Date(startDate).toISOString();
    const formatEndDate = new Date(endDate).toISOString();

    const res = await dbClient.query(
      `
          select count(srvc_req.db_id)
            from public.cl_tx_srvc_req srvc_req 
           inner join cl_tx_srvc_req_trnstn_log as trnstn_log
              on trnstn_log.srvc_req_id = srvc_req.db_id
             and trnstn_log.status_key = 'open'
           inner join cl_cf_srvc_statuses as srvc_status
              on srvc_status.status_key = srvc_req.status
             and srvc_status.srvc_id = srvc_req.srvc_type_id
           inner join cl_tx_srvc_req_trnstn_log as txn_closed
              on txn_closed.srvc_req_id = srvc_req.db_id
             and txn_closed.status_key = 'closed'
             and Date((txn_closed.trnstn_date)::timestamp at time zone $$utc$$ at time zone $$Asia/kolkata$$) >= Date(($1)::timestamp at time zone $$utc$$ at time zone $$Asia/kolkata$$)
             and Date((txn_closed.trnstn_date)::timestamp at time zone $$utc$$ at time zone $$Asia/kolkata$$) <= Date(($2)::timestamp at time zone $$utc$$ at time zone $$Asia/kolkata$$)  
           inner join cl_tx_orgs as org
              on org.org_id = srvc_req.org_id    
          
           where srvc_req.is_deleted is not true  
             and srvc_req.srvc_prvdr = '2' 
             and srvc_req.form_data->'feedback_data' is not null   
             and srvc_req.form_data->'feedback_data'->'rating_field_key' is not null   
             and srvc_req.status = $$closed$$           
         `,
      [formatStartDate, formatEndDate]
    );

    const resp = res.rows[0].count;

    console.log("getClosedReqWithRating :: resp :: ", resp);
    return resp;
  } catch (error) {
    console.error("getClosedReqWithRating :: error ::", error);
  }
}

async function getSumOfClosedReqRatings(startDate, endDate) {
  const dbClient = await getDbClient();
  try {
    if (!dbClient._connected) {
      await dbClient.connect();
      console.log("getSumOfClosedReqRatings :: Connected to the database!");
    } else {
      console.log(
        "getSumOfClosedReqRatings :: Already connected to the database!"
      );
    }
    const formatStartDate = new Date(startDate).toISOString();
    const formatEndDate = new Date(endDate).toISOString();

    const res = await dbClient.query(
      `
          select SUM(((srvc_req.form_data->'feedback_data'->>'form_data')::json->>(srvc_req.form_data->'feedback_data'->>'rating_field_key'))::int)
            from public.cl_tx_srvc_req srvc_req 
           inner join cl_tx_srvc_req_trnstn_log as trnstn_log
              on trnstn_log.srvc_req_id = srvc_req.db_id
             and trnstn_log.status_key = 'open'
           inner join cl_cf_srvc_statuses as srvc_status
              on srvc_status.status_key = srvc_req.status
             and srvc_status.srvc_id = srvc_req.srvc_type_id
           inner join cl_tx_orgs as org
              on org.org_id = srvc_req.org_id    
           inner join cl_tx_srvc_req_trnstn_log as txn_closed
              on txn_closed.srvc_req_id = srvc_req.db_id
             and txn_closed.status_key = 'closed'
             and Date((txn_closed.trnstn_date)::timestamp at time zone $$utc$$ at time zone $$Asia/kolkata$$) >= Date(($1)::timestamp at time zone $$utc$$ at time zone $$Asia/kolkata$$)
             and Date((txn_closed.trnstn_date)::timestamp at time zone $$utc$$ at time zone $$Asia/kolkata$$) <= Date(($2)::timestamp at time zone $$utc$$ at time zone $$Asia/kolkata$$)
           where srvc_req.is_deleted is not true 
             and srvc_req.srvc_prvdr = '2'  
             and srvc_req.form_data->'feedback_data' is not null   
             and srvc_req.form_data->'feedback_data'->'rating_field_key' is not null   
             and srvc_req.status = $$closed$$          
         `,
      [formatStartDate, formatEndDate]
    );

    const resp = res.rows[0].sum;

    console.log("getSumOfClosedReqRatings :: resp :: ", resp);
    return resp;
  } catch (error) {
    console.error("getSumOfClosedReqRatings :: error ::", error);
  }
}

exports.handler = async (event) => {
  // console.log("event", event);
  let responseStatus = true;
  let responseMessage = "success";
  let respBody = {};
  try {
    let startDate;
    let endDate;

    // Case 1: If called via API Gateway, the parameters will be in the queryStringParameters attribut
    if (event.queryStringParameters) {
      startDate = event.queryStringParameters.startDate;
      endDate = event.queryStringParameters.endDate;
    }
    // Case 2: If called via Lambda invoke, the parameters will be in the Payload
    else {
      startDate = event.startDate;
      endDate = event.endDate;
    }

    //  Determine startDate and endDate based on the given conditions
    if (startDate && !endDate) {
      // If only startDate is given, set endDate = startDate + 7 days
      startDate = new Date(startDate).getTime();
      endDate = startDate + 6 * 24 * 60 * 60 * 1000;
    } else if (!startDate && endDate) {
      // If only endDate is given, set startDate = endDate - 7 days
      endDate = new Date(endDate).getTime();
      startDate = endDate - 6 * 24 * 60 * 60 * 1000;
    } else if (!startDate && !endDate) {
      const oneDay = 24 * 60 * 60 * 1000
      // If neither is given, set endDate = today, startDate = today - 7 days
      endDate = Date.now() - oneDay;
      startDate = endDate - (6 * oneDay);
    } else {
      // If both are provided, convert to timestamps
      startDate = new Date(startDate).getTime();
      endDate = new Date(endDate).getTime();
    }

    // Optionally log the calculated startDate and endDate
    console.log("startDate:", new Date(startDate));
    console.log("endDate:", new Date(endDate));
    const descriptions = [
      "Total Users in TMS",
      "New Users Created in TMS",
      "Total orders (assigned to WIFY) - Requests assigned to WIFY in a week",
      "Total orders assigned by WIFY - Number of unique service requests with atleast 1 subtask created within the week",
      "Total active technicians - as per roles given below in query",
      "Active technicians with atleast one subtask in the week",
      "Number of subtasks that have subtask start date in or between the date range",
      "Number of subtasks from the above that have been updated as closed within the date range",
      "Total Closed Requests with Rating - Total number of WIFY closed requests rated",
      "Total Rating Received - Sum of ratings on above requests",
    ];

    // Fetch values from corresponding functions
    const values = await Promise.all([
      getTotalUsers(startDate, endDate),
      getNewUsers(startDate, endDate),
      getTotalOrdersAssignedToWIFY(startDate, endDate),
      getTotalOrdersAssignedToWIFYWithOneSubtask(startDate, endDate),
      getTotalActiveUserOfWifySP(startDate, endDate),
      getActiveTechnicianWithOneSbtsk(startDate, endDate),
      getNumberOfSbtskWithInRange(startDate, endDate),
      getNumberOfClosedSbtskWithInRange(startDate, endDate),
      getClosedReqWithRating(startDate, endDate),
      getSumOfClosedReqRatings(startDate, endDate),
    ]);

    // Generate HTML table
    const tableRows = descriptions
      .map(
        (desc, index) => `<tr><td>${desc}</td><td>${values[index]}</td></tr>`
      )
      .join("");

    const emailContent = `
        <h3>Report from ${new Date(startDate).toDateString()} to ${new Date(
      endDate
    ).toDateString()}</h3>
        <table border="1" style="border-collapse: collapse; width: 100%;">
          <tr>
            <th>Description</th>
            <th>Value</th>
          </tr>
          ${tableRows}
        </table>
      `;

    let data = {
      to: "<EMAIL>, <EMAIL>, <EMAIL>", 
      subject: `Wify Stats Report`,
      message: emailContent,
      //   cc:
      //     singleEntry.static_cc_email_ids &&
      //     Array.isArray(singleEntry.static_cc_email_ids)
      //       ? singleEntry.static_cc_email_ids.join(",")
      //       : undefined,
      //   bcc: "<EMAIL>",
    };
    respBody = await performSendEMAIL(data);
  } catch (e) {
    console.error("Error found", e);
    responseStatus = false;
    responseMessage = "something went wrong";
  }
  const response = {
    status: responseStatus,
    message: responseMessage,
    body: respBody,
  };
  return response;
};
