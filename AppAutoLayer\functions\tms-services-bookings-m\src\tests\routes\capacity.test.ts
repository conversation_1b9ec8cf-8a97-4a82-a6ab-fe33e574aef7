// Mock Sequelize models before importing app
const mockCapacity = {
  findOne: jest.fn(),
  findByPk: jest.fn(),
  create: jest.fn(),
  update: jest.fn(),
  findAll: jest.fn(),
  bulkCreate: jest.fn(),
  count: jest.fn(),
  toJSON: jest.fn().mockReturnThis(),
};

// Mock the database connection
jest.mock("../../config/database", () => ({
  __esModule: true,
  default: {
    transaction: jest.fn((callback) =>
      callback({ transaction: "mock-transaction" })
    ),
    query: jest.fn().mockResolvedValue([{ result: 1 }]),
  },
  testConnection: jest.fn().mockResolvedValue(true),
}));

// Mock the sequelize import in models
jest.mock("../../models", () => {
  const mockSequelize = {
    transaction: jest.fn((callback) =>
      callback({ transaction: "mock-transaction" })
    ),
    query: jest.fn().mockResolvedValue([{ result: 1 }]),
  };

  return {
    __esModule: true,
    sequelize: mockSequelize,
    Capacity: mockCapacity,
  };
});

// Now import app after mocking
import { app } from "../../app";

// Mock the models
jest.mock("../../models/Capacity", () => ({
  __esModule: true,
  default: mockCapacity,
}));

jest.mock("../../models/Booking", () => ({
  __esModule: true,
  default: {
    findAll: jest.fn(),
    create: jest.fn(),
  },
}));

// Mock the authentication middleware
jest.mock("../../middleware/auth", () => ({
  authenticateApiKey: (_req: any, _res: any, next: any) => next(),
}));

import request from "supertest";

describe("Capacity Routes", () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe("POST /capacity", () => {
    test("should return 400 if required fields are missing", async () => {
      // Arrange
      const requestBody = { resourceId: "resource-123" }; // Missing other required fields

      // Act
      const response = await request(app).post("/capacity").send(requestBody);

      // Assert
      expect(response.status).toBe(400);
      expect(response.body).toHaveProperty("status", false);
      expect(response.body.message).toContain("Missing required fields");
    });

    test("should update capacity and return 200 if capacity already exists", async () => {
      // Arrange
      const requestBody = {
        resourceId: "resource-123",
        startTime: "2023-07-20T09:00:00.000Z",
        endTime: "2023-07-20T17:00:00.000Z",
        totalCapacity: 15, // New capacity value
      };

      // Mock existing capacity
      const existingCapacity = {
        id: BigInt(1),
        resourceId: "resource-123",
        startTime: new Date("2023-07-20T09:00:00.000Z"),
        endTime: new Date("2023-07-20T17:00:00.000Z"),
        totalCapacity: 10,
        availableCapacity: 10,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      // Mock updated capacity
      const updatedCapacity = {
        ...existingCapacity,
        totalCapacity: 15,
        availableCapacity: 15,
        updatedAt: new Date(),
      };

      // Mock Sequelize findOne to return an existing capacity
      mockCapacity.findOne.mockResolvedValue({
        ...existingCapacity,
        toJSON: () => existingCapacity,
        save: jest.fn().mockResolvedValue(updatedCapacity),
      });

      // Mock Sequelize update to return the updated capacity
      mockCapacity.update.mockResolvedValue({
        ...updatedCapacity,
        toJSON: () => updatedCapacity,
      });

      // Act
      const response = await request(app).post("/capacity").send(requestBody);

      // Assert
      // We're only testing the API response, not the Prisma calls
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty("status", true);
      expect(response.body.message).toContain("Capacity updated successfully");
    });

    test("should create capacity and return 201 if all validations pass", async () => {
      // Arrange
      const requestBody = {
        resourceId: "resource-123",
        startTime: "2023-07-20T09:00:00.000Z",
        endTime: "2023-07-20T17:00:00.000Z",
        totalCapacity: 10,
      };

      // Mock Sequelize findOne to return null (no existing capacity)
      mockCapacity.findOne.mockResolvedValue(null);

      // Mock Sequelize create to return the created capacity
      const createdCapacity = {
        id: BigInt(1),
        resourceId: requestBody.resourceId,
        startTime: new Date(requestBody.startTime),
        endTime: new Date(requestBody.endTime),
        totalCapacity: requestBody.totalCapacity,
        availableCapacity: requestBody.totalCapacity,
        createdAt: new Date(),
        updatedAt: new Date(),
      };
      mockCapacity.create.mockResolvedValue({
        ...createdCapacity,
        toJSON: () => createdCapacity,
      });

      // Act
      const response = await request(app).post("/capacity").send(requestBody);

      // Assert
      // We're only testing the API response, not the Prisma calls

      expect(response.status).toBe(201);
      expect(response.body).toHaveProperty("status", true);
      expect(response.body.message).toContain("Capacity added successfully");
      expect(response.body.data).toEqual(
        expect.objectContaining({
          resourceId: requestBody.resourceId,
          totalCapacity: requestBody.totalCapacity,
          availableCapacity: requestBody.totalCapacity,
        })
      );
    });
  });

  describe("GET /capacity/byId/:id", () => {
    test("should return 404 if capacity is not found", async () => {
      // Arrange
      const capacityId = "999";

      // Mock Sequelize findByPk to return null
      mockCapacity.findByPk.mockResolvedValue(null);

      // Act
      const response = await request(app).get(`/capacity/byId/${capacityId}`);

      // Assert
      // We're only testing the API response, not the Prisma calls
      expect(response.status).toBe(404);
      expect(response.body).toHaveProperty("status", false);
      expect(response.body.message).toContain("Capacity not found");
    });

    test("should return capacity if found", async () => {
      // Arrange
      const capacityId = "1";

      // Mock Sequelize findByPk to return a capacity
      const capacity = {
        id: BigInt(1),
        resourceId: "resource-123",
        startTime: new Date("2023-07-20T09:00:00.000Z"),
        endTime: new Date("2023-07-20T17:00:00.000Z"),
        totalCapacity: 10,
        availableCapacity: 10,
        createdAt: new Date(),
        updatedAt: new Date(),
        toJSON: function () {
          return this;
        },
      };
      mockCapacity.findByPk.mockResolvedValue(capacity);

      // Act
      const response = await request(app).get(`/capacity/byId/${capacityId}`);

      // Assert
      // We're only testing the API response, not the Prisma calls
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty("status", true);
      expect(response.body.data).toEqual(
        expect.objectContaining({
          resourceId: capacity.resourceId,
          totalCapacity: capacity.totalCapacity,
          availableCapacity: capacity.availableCapacity,
        })
      );
    });
  });

  describe("GET /capacity/slot-wise-multiple-days", () => {
    test("should return 400 if required parameters are missing", async () => {
      // Arrange - missing resourceId
      const queryParams = { dates: "2023-07-20,2023-07-21" };

      // Act
      const response = await request(app)
        .get("/capacity/slot-wise-multiple-days")
        .query(queryParams);

      // Assert
      expect(response.status).toBe(400);
      expect(response.body).toHaveProperty("status", false);
      expect(response.body.message).toContain("Missing required parameters");
    });

    test("should return 400 if dates format is invalid", async () => {
      // Arrange - invalid date format
      const queryParams = {
        resourceId: "resource-123",
        dates: "2023-07-20,invalid-date",
      };

      // Act
      const response = await request(app)
        .get("/capacity/slot-wise-multiple-days")
        .query(queryParams);

      // Assert
      expect(response.status).toBe(400);
      expect(response.body).toHaveProperty("status", false);
      expect(response.body.message).toContain("Invalid date format");
    });

    test("should return capacity data for multiple days", async () => {
      // Arrange
      const queryParams = {
        resourceId: "resource-123",
        dates: "2023-07-20,2023-07-21",
      };

      // Mock capacity data for the first date
      const capacitiesDay1 = [
        {
          id: BigInt(1),
          resourceId: "resource-123",
          startTime: new Date("2023-07-20T09:00:00.000Z"),
          endTime: new Date("2023-07-20T10:00:00.000Z"),
          totalCapacity: 10,
          availableCapacity: 8,
          createdAt: new Date(),
          updatedAt: new Date(),
          toJSON: function () {
            return this;
          },
        },
        {
          id: BigInt(2),
          resourceId: "resource-123",
          startTime: new Date("2023-07-20T10:00:00.000Z"),
          endTime: new Date("2023-07-20T11:00:00.000Z"),
          totalCapacity: 10,
          availableCapacity: 5,
          createdAt: new Date(),
          updatedAt: new Date(),
          toJSON: function () {
            return this;
          },
        },
      ];

      // Mock capacity data for the second date
      const capacitiesDay2 = [
        {
          id: BigInt(3),
          resourceId: "resource-123",
          startTime: new Date("2023-07-21T09:00:00.000Z"),
          endTime: new Date("2023-07-21T10:00:00.000Z"),
          totalCapacity: 15,
          availableCapacity: 12,
          createdAt: new Date(),
          updatedAt: new Date(),
          toJSON: function () {
            return this;
          },
        },
      ];

      // Mock Sequelize findAll to return capacities for both days
      mockCapacity.findAll.mockResolvedValue([
        ...capacitiesDay1,
        ...capacitiesDay2,
      ]);

      // Act
      const response = await request(app)
        .get("/capacity/slot-wise-multiple-days")
        .query(queryParams);

      // Assert
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty("status", true);
      expect(response.body.message).toContain(
        "Slot-wise capacity for multiple days retrieved successfully"
      );

      // Check that we have data for both dates
      expect(response.body.data).toHaveLength(2);

      // Check first date data
      const day1Data = response.body.data.find(
        (d: any) => d.date === "2023-07-20"
      );
      expect(day1Data).toBeDefined();
      expect(day1Data.resource_id).toBe("resource-123");
      expect(day1Data.slots).toHaveLength(2);

      // Check second date data
      const day2Data = response.body.data.find(
        (d: any) => d.date === "2023-07-21"
      );
      expect(day2Data).toBeDefined();
      expect(day2Data.resource_id).toBe("resource-123");
      expect(day2Data.slots).toHaveLength(1);
    });

    test("should return empty data if no capacity found", async () => {
      // Arrange
      const queryParams = {
        resourceId: "resource-123",
        dates: "2023-07-20,2023-07-21",
      };

      // Mock Sequelize findAll to return empty array
      mockCapacity.findAll.mockResolvedValue([]);

      // Act
      const response = await request(app)
        .get("/capacity/slot-wise-multiple-days")
        .query(queryParams);

      // Assert
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty("status", true);
      expect(response.body.message).toContain("No capacity data found");
      expect(response.body.data).toEqual([]);
    });
  });

  describe("POST /capacity/batch", () => {
    test("should return 400 if capacityRecords is missing", async () => {
      // Arrange
      const requestBody = {}; // Missing capacityRecords

      // Act
      const response = await request(app)
        .post("/capacity/batch")
        .send(requestBody);

      // Assert
      expect(response.status).toBe(400);
      expect(response.body).toHaveProperty("status", false);
      expect(response.body.message).toContain(
        "capacityRecords must be a non-empty array"
      );
    });

    test("should return 400 if capacityRecords is empty", async () => {
      // Arrange
      const requestBody = { capacityRecords: [] }; // Empty array

      // Act
      const response = await request(app)
        .post("/capacity/batch")
        .send(requestBody);

      // Assert
      expect(response.status).toBe(400);
      expect(response.body).toHaveProperty("status", false);
      expect(response.body.message).toContain(
        "capacityRecords must be a non-empty array"
      );
    });

    test("should process batch successfully with new records and return 200", async () => {
      // Arrange
      const requestBody = {
        capacityRecords: [
          {
            resourceId: "resource-123",
            startTime: "2023-07-20T09:00:00.000Z",
            endTime: "2023-07-20T17:00:00.000Z",
            totalCapacity: 10,
            availableCapacity: 8,
          },
          {
            resourceId: "resource-456",
            startTime: "2023-07-20T09:00:00.000Z",
            endTime: "2023-07-20T17:00:00.000Z",
            totalCapacity: 15,
            availableCapacity: 12,
          },
        ],
        organizationId: "org-123",
      };

      // Mock Sequelize findAll to return empty array (no existing capacities)
      mockCapacity.findAll.mockResolvedValue([]);

      // Mock Sequelize bulkCreate to return the created capacities
      mockCapacity.bulkCreate.mockResolvedValue([
        {
          id: BigInt(1),
          toJSON: () => ({
            id: 1,
            resourceId: "resource-123",
            startTime: new Date("2023-07-20T09:00:00.000Z"),
            endTime: new Date("2023-07-20T17:00:00.000Z"),
            totalCapacity: 10,
            availableCapacity: 8,
          }),
        },
        {
          id: BigInt(2),
          toJSON: () => ({
            id: 2,
            resourceId: "resource-456",
            startTime: new Date("2023-07-20T09:00:00.000Z"),
            endTime: new Date("2023-07-20T17:00:00.000Z"),
            totalCapacity: 15,
            availableCapacity: 12,
          }),
        },
      ]);

      // Act
      const response = await request(app)
        .post("/capacity/batch")
        .send(requestBody);

      // Assert
      // We're only testing the API response, not the Prisma calls
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty("status", true);
      expect(response.body.message).toContain("Successfully processed");
      expect(response.body.data).toHaveProperty("successCount", 2);
      expect(response.body.data).toHaveProperty("failureCount", 0);
    });

    test("should process batch successfully with existing records and return 200", async () => {
      // Arrange
      const requestBody = {
        capacityRecords: [
          {
            resourceId: "resource-123",
            startTime: "2023-07-20T09:00:00.000Z",
            endTime: "2023-07-20T17:00:00.000Z",
            totalCapacity: 20, // Updated capacity
            availableCapacity: 15,
          },
          {
            resourceId: "resource-456",
            startTime: "2023-07-20T09:00:00.000Z",
            endTime: "2023-07-20T17:00:00.000Z",
            totalCapacity: 25, // Updated capacity
            availableCapacity: 20,
          },
        ],
        organizationId: "org-123",
      };

      // Mock existing capacities
      const existingCapacities = [
        {
          id: BigInt(1),
          resourceId: "resource-123",
          startTime: new Date("2023-07-20T09:00:00.000Z"),
          endTime: new Date("2023-07-20T17:00:00.000Z"),
          totalCapacity: 10, // Old capacity
          availableCapacity: 8,
          createdAt: new Date(),
          updatedAt: new Date(),
        },
        {
          id: BigInt(2),
          resourceId: "resource-456",
          startTime: new Date("2023-07-20T09:00:00.000Z"),
          endTime: new Date("2023-07-20T17:00:00.000Z"),
          totalCapacity: 15, // Old capacity
          availableCapacity: 12,
          createdAt: new Date(),
          updatedAt: new Date(),
        },
      ];

      // Mock Sequelize findAll to return existing capacities
      mockCapacity.findAll.mockResolvedValue(
        existingCapacities.map((cap) => ({
          ...cap,
          toJSON: () => cap,
          save: jest.fn().mockResolvedValue({
            ...cap,
            totalCapacity: cap.id === BigInt(1) ? 20 : 25,
            availableCapacity: cap.id === BigInt(1) ? 15 : 20,
            updatedAt: new Date(),
            toJSON: () => ({
              ...cap,
              totalCapacity: cap.id === BigInt(1) ? 20 : 25,
              availableCapacity: cap.id === BigInt(1) ? 15 : 20,
              updatedAt: new Date(),
            }),
          }),
        }))
      );

      // Mock Sequelize update to return updated capacities
      mockCapacity.update.mockImplementation((data, options) => {
        const existingCapacity = existingCapacities.find(
          (c) => c.id === options.where.id
        );
        const updatedCap = {
          ...existingCapacity,
          ...data,
          updatedAt: new Date(),
        };
        return Promise.resolve({
          ...updatedCap,
          toJSON: () => updatedCap,
        });
      });

      // Act
      const response = await request(app)
        .post("/capacity/batch")
        .send(requestBody);

      // Assert
      // We're only testing the API response, not the Prisma calls

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty("status", true);
      expect(response.body.message).toContain("Successfully processed");
      expect(response.body.data).toHaveProperty("successCount", 2);
      expect(response.body.data).toHaveProperty("failureCount", 0);
    });

    test("should return 400 if any record fails validation", async () => {
      // Arrange
      const requestBody = {
        capacityRecords: [
          {
            resourceId: "resource-123",
            startTime: "2023-07-20T09:00:00.000Z",
            endTime: "2023-07-20T17:00:00.000Z",
            totalCapacity: 10,
          },
          {
            resourceId: "resource-456",
            startTime: "invalid-date", // Invalid date format
            endTime: "2023-07-20T17:00:00.000Z",
            totalCapacity: 15,
          },
        ],
        organizationId: "org-123",
      };

      // Act
      const response = await request(app)
        .post("/capacity/batch")
        .send(requestBody);

      // Assert
      expect(response.status).toBe(400);
      expect(response.body).toHaveProperty("status", false);
      expect(response.body.message).toContain("Batch validation failed");
      expect(response.body.data).toHaveProperty("failureCount", 1);
      expect(response.body.data.failedRecords).toHaveLength(1);
      expect(response.body.data.failedRecords[0]).toHaveProperty(
        "resourceId",
        "resource-456"
      );
      // We're only testing the API response, not the Prisma calls
    });
  });
});
