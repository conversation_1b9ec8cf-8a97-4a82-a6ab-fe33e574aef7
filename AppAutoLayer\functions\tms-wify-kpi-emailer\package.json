{"name": "tms-wify-kpi-emailer", "version": "1.0.0", "description": "Lambda function that queries vertical KPI data and sends it via email", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["lambda", "email", "kpi", "tms", "wify"], "author": "WIFY TMS Team", "license": "ISC", "dependencies": {"pg": "^8.11.3", "nodemailer": "^6.9.7", "dotenv": "^16.3.1"}}