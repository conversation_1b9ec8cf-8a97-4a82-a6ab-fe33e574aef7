export interface ProductDetails {
  [key: string]: any;
}

export interface Filters {
  [key: string]: any;
}

export interface TranslatedCapacityKeys {
  [key: string]: any;
}

export interface EventPayload {
  is_api: boolean;
  org_id: string;
  usr_id: string;
  ip_address: string;
  user_agent: string;
  filters: Filters;
  srvc_type_id: number;
  translated_capacity_keys: TranslatedCapacityKeys;
  provider_id: string;
  pincode: string;
  product_details: ProductDetails;
  vertical_id: number;
  hub_id: string;
  [key: string]: any;
}

export interface Response {
  status: boolean;
  message: string;
  data?: any;
  error?: string;
}

export interface APIGatewayResponse {
  statusCode: number;
  body: string;
}
