import { APIGatewayProxyHandler } from "aws-lambda";
import { app } from "./app";

// Use require instead of import to dynamically load the module from the layer
// This allows the code to work both locally and in AWS Lambda
let awsServerlessExpress: any;
try {
  // First try to load from the layer path
  awsServerlessExpress = require("/opt/nodejs/node_modules/aws-serverless-express");
} catch (error) {
  // Fallback to local path for development
  console.log("Loading aws-serverless-express from local path");
  awsServerlessExpress = require("aws-serverless-express");
}

// Load environment variables based on NODE_ENV
const env = process.env.NODE_ENV || "development";
require("dotenv").config({ path: `.env.${env}` });

// Create server for AWS Lambda
const server = awsServerlessExpress.createServer(app);

// Lambda handler
export const handler: APIGatewayProxyHandler = (event, context) => {
  console.log("Lambda handler invoked with event:", JSON.stringify(event));
  awsServerlessExpress.proxy(server, event, context);
};
