const { sendEmail } = require("./emailService");
const { generateDataTableHTML } = require("./htmlTemplate");
const { getISTTimestamp } = require("./htmlTemplate");
const { getManagerTotals, getConsolidatedTotals } = require("./dataService");
const categoryManagers = require("./categoryManagers");

/**
 * Send consolidated company-wide email with all verticals data
 * @param {Array} verticalKPIData - All vertical KPI data
 * @returns {Promise<Object>} Result of email sending
 */
async function sendConsolidatedEmail(verticalKPIData) {
  try {
    console.log(
      "ConsolidatedEmailProcessor::sendConsolidatedEmail:: Starting consolidated email process"
    );

    // Check if EMAIL_CC is configured
    const emailCC = process.env.EMAIL_CC;
    if (!emailCC) {
      console.log(
        "ConsolidatedEmailProcessor::sendConsolidatedEmail:: EMAIL_CC not configured, skipping consolidated email"
      );
      return {
        status: "Skipped",
        message: "EMAIL_CC environment variable not configured",
      };
    }

    // Calculate dates for display
    const today = new Date();
    const yesterday = new Date(today);
    yesterday.setDate(today.getDate() - 1);
    const yesterdayStr = yesterday.toISOString().split("T")[0];

    // Get consolidated totals from database
    const consolidatedTotals = await getConsolidatedTotals();

    // Generate consolidated summary with database totals
    const summary = generateConsolidatedSummary(
      verticalKPIData,
      consolidatedTotals
    );
    console.log(
      "ConsolidatedEmailProcessor::sendConsolidatedEmail:: Retrieved consolidated totals from database:",
      consolidatedTotals
    );

    // Generate manager vs category table
    const managerCategoryTable = await generateManagerCategoryTable(
      verticalKPIData
    );

    // Generate HTML content for consolidated email
    const htmlContent = generateDataTableHTML(verticalKPIData, {
      title: "Fulfillment KPI Report - WIFFY",
      description: `Comprehensive overview of all verticals performance metrics for ${yesterdayStr}. This report includes KPI data across all business verticals to provide executive-level insights into company-wide operations, order fulfillment, and supply management.`,
      summary: summary,
      managerCategoryTable: managerCategoryTable,
      managerTotals: consolidatedTotals, // Pass the SQL-calculated totals
    });

    // Email configuration for consolidated email
    const emailConfig = {
      to: emailCC, // Send to EMAIL_CC address
      subject: `Daily KPI Report - Company Wide - ${getISTTimestamp()}`,
      htmlContent: htmlContent,
      cc: "", // Empty CC as specified
      bcc: process.env.EMAIL_BCC || "", // Optional BCC recipients
      attachments: [], // No attachments for consolidated email (keeping attachment code handy but commented)
    };

    // TODO: Future enhancement - Add consolidated attachments if needed
    // const consolidatedAttachments = await generateConsolidatedAttachments(verticalKPIData, yesterdayStr);
    // emailConfig.attachments = consolidatedAttachments;

    // Send consolidated email
    console.log(
      `ConsolidatedEmailProcessor::sendConsolidatedEmail:: Sending consolidated email to: ${emailCC}`
    );
    console.log(
      `ConsolidatedEmailProcessor::sendConsolidatedEmail:: Email includes data for ${verticalKPIData.length} verticals`
    );

    const emailResult = await sendEmail(emailConfig);

    if (emailResult.success) {
      console.log(
        "ConsolidatedEmailProcessor::sendConsolidatedEmail:: Consolidated email sent successfully"
      );
      return {
        status: "Success",
        message: "Consolidated email sent successfully",
        recipient: emailCC,
        verticalCount: verticalKPIData.length,
        emailResult: emailResult,
      };
    } else {
      console.error(
        "ConsolidatedEmailProcessor::sendConsolidatedEmail:: Failed to send consolidated email:",
        emailResult.error
      );
      return {
        status: "Failed",
        message: "Failed to send consolidated email",
        recipient: emailCC,
        error: emailResult.error,
      };
    }
  } catch (error) {
    console.error(
      "ConsolidatedEmailProcessor::sendConsolidatedEmail:: Error in consolidated email process:",
      error
    );
    return {
      status: "Error",
      message: "Error in consolidated email process",
      error: error.message,
    };
  }
}

/**
 * Generate manager vs category table showing aggregated data for each manager
 * @param {Array} verticalKPIData - All vertical KPI data
 * @returns {Promise<string>} HTML formatted manager vs category table
 */
async function generateManagerCategoryTable(verticalKPIData) {
  if (!verticalKPIData || verticalKPIData.length === 0) {
    return "";
  }

  // Create manager aggregated data
  const managerData = [];

  for (const [, managerInfo] of Object.entries(categoryManagers)) {
    const managerVerticalIds = managerInfo.verticals.map((v) => v.vertical_id);

    console.log(
      `ConsolidatedEmailProcessor::generateManagerCategoryTable:: Processing manager: ${managerInfo.user_name} with vertical IDs: ${managerVerticalIds}`
    );

    // Calculate totals for this manager using SQL aggregation
    const managerTotals = await getManagerTotals(managerVerticalIds);

    // Calculate assignment rate for this manager
    const assignmentRate =
      managerTotals.openOrders + managerTotals.newOrders > 0
        ? (
            (managerTotals.ordersScheduled /
              (managerTotals.openOrders + managerTotals.newOrders)) *
            100
          ).toFixed(1)
        : "0.0";

    // Calculate deployment rate for this manager (commented out as not currently used)
    // const deploymentRate =
    //   managerTotals.activeSupply > 0
    //     ? (
    //         (managerTotals.supplyDeployed / managerTotals.activeSupply) *
    //         100
    //       ).toFixed(1)
    //     : "0.0";

    managerData.push({
      Manager: managerInfo.user_name,
      // Categories: categories,
      Verticals: managerTotals.verticalCount,
      "All Time Orders": managerTotals.allTimeOrders.toLocaleString(),
      "Open Orders": managerTotals.openOrders.toLocaleString(),
      "New Orders": managerTotals.newOrders.toLocaleString(),
      "Orders Closed": managerTotals.ordersClosed.toLocaleString(),
      "Tech Inch.": managerTotals.techInch.toLocaleString(),
      "Orders Sch.": managerTotals.ordersScheduled.toLocaleString(),
      "Orders Sch. Yest.": managerTotals.ordersSchYest.toLocaleString(),
      "Tasks Created": managerTotals.tasksCreated.toLocaleString(),
      "Assignment Rate": `${assignmentRate}%`,
      "Supp. Depl": managerTotals.supplyDeployed.toLocaleString(),
      "Supply All Task Closed":
        managerTotals.supplyAllTaskClosed.toLocaleString(),
      "Supply No Task Update":
        managerTotals.supplyNoTaskUpdate.toLocaleString(),
      "Supply Partial Task Update":
        managerTotals.supplyPartialTaskUpdate.toLocaleString(),
      "Active Supply": managerTotals.activeSupply.toLocaleString(),
      "New Supply": managerTotals.newSupply.toLocaleString(),
      "Inactive Supply": managerTotals.inactiveSupply.toLocaleString(),
      // "Deployment Rate": `${deploymentRate}%`,
    });
  }

  if (managerData.length === 0) {
    return "";
  }

  // Generate HTML table for manager data
  const headers = Object.keys(managerData[0]);
  const headerRow = headers
    .map(
      (header) =>
        `<th style="padding: 8px; background-color: #f8f9fa; border: 1px solid #dee2e6; text-align: left; font-weight: bold; font-size: 11px; white-space: nowrap;">${header}</th>`
    )
    .join("");

  const dataRows = managerData
    .map((row) => {
      const cells = headers
        .map((header) => {
          const value = row[header];
          const cellStyle =
            "padding: 8px; border: 1px solid #dee2e6; font-size: 11px; white-space: nowrap;";
          return `<td style="${cellStyle}">${value}</td>`;
        })
        .join("");
      return `<tr>${cells}</tr>`;
    })
    .join("");

  return `
    <div class="manager-category-section" style="margin: 30px 0; width: 100%;">
      <h3 style="color: #495057; margin-bottom: 15px;">📋 Manager vs Category Performance Overview</h3>
      <p style="color: #6c757d; margin-bottom: 20px; font-size: 14px;">
        Aggregated KPI metrics grouped by category managers and their assigned verticals
      </p>
      <div class="table-container" style="overflow-x: auto; border-radius: 6px; box-shadow: 0 1px 3px rgba(0,0,0,0.1); width: 100%;">
        <table style="width: 100%; border-collapse: collapse; font-size: 12px; min-width: 1200px;">
          <thead>
            <tr>${headerRow}</tr>
          </thead>
          <tbody>
            ${dataRows}
          </tbody>
        </table>
      </div>
    </div>
  `;
}

/**
 * Generate consolidated summary for all verticals in structured format
 * @param {Array} verticalKPIData - All vertical KPI data
 * @param {Object} consolidatedTotals - Database totals from getConsolidatedTotals()
 * @returns {string} HTML formatted summary content
 */
function generateConsolidatedSummary(
  verticalKPIData,
  consolidatedTotals = null
) {
  if (!verticalKPIData || verticalKPIData.length === 0) {
    return "No vertical data available for analysis.";
  }

  // Use consolidated totals from database if available, otherwise calculate from verticalKPIData
  let totals;
  if (consolidatedTotals) {
    console.log(
      "ConsolidatedEmailProcessor::generateConsolidatedSummary:: Using database totals"
    );
    totals = {
      totalVerticals: verticalKPIData.length, // Count from array
      allTimeOrders: consolidatedTotals.allTimeOrders,
      openOrders: consolidatedTotals.openOrders,
      newOrders: consolidatedTotals.newOrders,
      ordersScheduled: consolidatedTotals.ordersScheduled,
      activeSupply: consolidatedTotals.activeSupply,
      newSupply: consolidatedTotals.newSupply,
      inactiveSupply: consolidatedTotals.inactiveSupply,
      supplyDeployed: consolidatedTotals.supplyDeployed,
      techInch: consolidatedTotals.techInch,
    };
  } else {
    console.log(
      "ConsolidatedEmailProcessor::generateConsolidatedSummary:: Using JavaScript calculation"
    );
    // Calculate totals across all verticals using JavaScript reduce (fallback)
    totals = verticalKPIData.reduce(
      (acc, vertical) => {
        acc.totalVerticals += 1;
        acc.allTimeOrders += parseInt(vertical["All time orders"] || 0);
        acc.openOrders += parseInt(vertical["Open orders"] || 0);
        acc.newOrders += parseInt(vertical["New orders"] || 0);
        acc.ordersScheduled += parseInt(vertical["Orders Sch."] || 0);
        acc.activeSupply += parseInt(vertical["Active supply"] || 0);
        acc.newSupply += parseInt(vertical["New supply"] || 0);
        acc.inactiveSupply += parseInt(vertical["Inactive supply"] || 0);
        acc.supplyDeployed += parseInt(vertical["Supp. depl"] || 0);
        acc.techInch += parseInt(vertical["Tech Inch."] || 0);
        return acc;
      },
      {
        totalVerticals: 0,
        allTimeOrders: 0,
        openOrders: 0,
        newOrders: 0,
        ordersScheduled: 0,
        activeSupply: 0,
        newSupply: 0,
        inactiveSupply: 0,
        supplyDeployed: 0,
        techInch: 0,
      }
    );
  }

  // Calculate key metrics
  const assignmentRate =
    totals.openOrders + totals.newOrders > 0
      ? (
          (totals.ordersScheduled / (totals.openOrders + totals.newOrders)) *
          100
        ).toFixed(1)
      : "0.0";

  // Find top performing verticals
  const topVerticalsByOrders = [...verticalKPIData]
    .sort(
      (a, b) =>
        parseInt(b["Open orders"] || 0) - parseInt(a["Open orders"] || 0)
    )
    .slice(0, 3)
    .map((v) => v.Title?.replace(/"/g, "") || "Unknown");

  return `
    <div class="summary-section">
      <h3>📊 Company-Wide Daily KPI Summary</h3>
      <div class="summary-grid">
        <div class="summary-item">
          <strong>Total Active Verticals:</strong> ${totals.totalVerticals.toLocaleString()}
          <br><small>Business verticals currently operational across the company</small>
        </div>
        <div class="summary-item">
          <strong>Total Open Orders:</strong> ${totals.openOrders.toLocaleString()}
          <br><small>Orders currently in open status across all verticals</small>
        </div>
        <div class="summary-item">
          <strong>New Orders Created:</strong> ${totals.newOrders.toLocaleString()}
          <br><small>Fresh demand generated yesterday across all verticals</small>
        </div>
        <div class="summary-item">
          <strong>Total Suppl. deployed:</strong> ${totals.supplyDeployed.toLocaleString()}
          <br><small>Total number of technicians deployed across all verticals</small>
        </div>
        <div class="summary-item">
          <strong>Total tech. incharges:</strong> ${totals.techInch.toLocaleString()}
          <br><small>Total number of technician incharges across all verticals</small>
        </div>
        <div class="summary-item">
          <strong>Assignment Rate:</strong> ${assignmentRate}%
          <br><small>Percentage of orders that got scheduled for technicians</small>
          <br><em>Formula: (Orders Sch. / (Open Orders + New Orders)) × 100</em>
        </div>
        <div class="summary-item">
          <strong>Total Active Supply:</strong> ${totals.activeSupply.toLocaleString()}
          <br><small>Total technicians available across all verticals</small>
          <br><em>Formula: Sum of Active supply</em>
        </div>
        <div class="summary-item">
          <strong>New Supply Added:</strong> ${totals.newSupply.toLocaleString()}
          <br><small>Technicians onboarded yesterday across all verticals</small>
        </div>
        <div class="summary-item">
          <strong>Top Verticals by Orders:</strong> ${topVerticalsByOrders.join(
            ", "
          )}
          <br><small>Verticals with highest open order volume</small>
        </div>
      </div>
    </div>
  `;
}

/**
 * Generate consolidated attachments for all verticals (COMMENTED FOR FUTURE USE)
 * @param {Array} verticalKPIData - All vertical KPI data
 * @param {string} yesterdayStr - Yesterday's date string
 * @returns {Promise<Array>} Array of attachment objects
 */
/*
async function generateConsolidatedAttachments(verticalKPIData, yesterdayStr) {
  const { getOrdersForVertical } = require("./dataService");
  const { convertToCSV } = require("./csvUtils");

  const consolidatedAttachments = [];

  // Generate a single consolidated CSV with all open orders across all verticals
  const allOpenOrders = [];

  for (const vertical of verticalKPIData) {
    const openOrdersCount = parseInt(vertical["Open orders"] || 0);

    if (openOrdersCount > 0) {
      try {
        const orderData = await getOrdersForVertical(
          vertical["Vertical ID"],
          vertical.Title
        );

        if (orderData && orderData.length > 0) {
          // Add vertical information to each order
          const ordersWithVertical = orderData.map(order => ({
            ...order,
            "Vertical": vertical.Title?.replace(/"/g, '') || 'Unknown',
            "Vertical ID": vertical["Vertical ID"]
          }));

          allOpenOrders.push(...ordersWithVertical);
        }
      } catch (error) {
        console.error(
          `ConsolidatedEmailProcessor::generateConsolidatedAttachments:: Error fetching orders for vertical ${vertical["Vertical ID"]}:`,
          error
        );
      }
    }
  }

  if (allOpenOrders.length > 0) {
    const csvContent = convertToCSV(allOpenOrders);
    const attachment = {
      filename: `Company_Wide_Open_Orders_${yesterdayStr}.csv`,
      content: csvContent,
      contentType: "text/csv",
    };

    consolidatedAttachments.push(attachment);

    console.log(
      `ConsolidatedEmailProcessor::generateConsolidatedAttachments:: Created consolidated CSV with ${allOpenOrders.length} orders across all verticals`
    );
  }

  return consolidatedAttachments;
}
*/

module.exports = {
  sendConsolidatedEmail,
  generateConsolidatedSummary,
  generateManagerCategoryTable,
  // generateConsolidatedAttachments, // Commented for future use
};
