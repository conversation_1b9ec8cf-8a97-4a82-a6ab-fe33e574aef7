/**
 * Get current timestamp in IST format
 * @returns {string} - Formatted timestamp
 */
const getISTTimestamp = () => {
  const now = new Date();
  const istOffset = 5.5 * 60 * 60 * 1000; // IST is UTC+5:30
  const istTime = new Date(now.getTime() + istOffset);
  return istTime.toISOString().replace("T", " ").substring(0, 19);
};

/**
 * Generate data summary for KPI metrics
 * @param {Array} data - Array of KPI data objects
 * @param {Object} preCalculatedTotals - Optional pre-calculated totals from SQL
 * @returns {string} - HTML summary content
 */
const generateDataSummary = (data, preCalculatedTotals = null) => {
  if (!data || !Array.isArray(data) || data.length === 0) {
    return "<p>No data available for analysis.</p>";
  }

  // Use pre-calculated totals if available, otherwise calculate using JavaScript reduce
  let totals;
  if (preCalculatedTotals) {
    console.log(
      "HtmlTemplate::generateDataSummary:: Using SQL-calculated totals"
    );
    totals = {
      allTimeOrders: preCalculatedTotals.allTimeOrders,
      activeOrders:
        preCalculatedTotals.openOrders + preCalculatedTotals.newOrders,
      newOrders: preCalculatedTotals.newOrders,
      ordersScheduled: preCalculatedTotals.ordersScheduled,
      ordersClosed: preCalculatedTotals.ordersClosed,
      activeSupply: preCalculatedTotals.activeSupply,
      newSupply: preCalculatedTotals.newSupply,
      supplyDeployed: preCalculatedTotals.supplyDeployed,
      techInch: preCalculatedTotals.techInch,
    };
  } else {
    console.log(
      "HtmlTemplate::generateDataSummary:: Using JavaScript reduce calculation"
    );
    // Calculate totals across all verticals using JavaScript reduce (fallback)
    totals = data.reduce(
      (acc, row) => {
        acc.allTimeOrders += parseInt(row["All time orders"] || 0);
        acc.activeOrders +=
          parseInt(row["Open orders"] || 0) + parseInt(row["New orders"] || 0);
        acc.newOrders += parseInt(row["New orders"] || 0);
        acc.ordersScheduled += parseInt(row["Orders Sch."] || 0);
        acc.ordersClosed += parseInt(row["Orders closed"] || 0);
        acc.activeSupply += parseInt(row["Active supply"] || 0);
        acc.newSupply += parseInt(row["New supply"] || 0);
        acc.supplyDeployed += parseInt(row["Supp. depl"] || 0);
        acc.techInch += parseInt(row["Tech Inch."] || 0);
        return acc;
      },
      {
        allTimeOrders: 0,
        activeOrders: 0,
        newOrders: 0,
        ordersScheduled: 0,
        ordersClosed: 0,
        activeSupply: 0,
        newSupply: 0,
        supplyDeployed: 0,
        techInch: 0,
      }
    );
  }

  const assignmentRate =
    totals.activeOrders > 0
      ? ((totals.ordersScheduled / totals.activeOrders) * 100).toFixed(1)
      : 0;

  const techInchSupplyRatio =
    totals.supplyDeployed > 0
      ? (totals.supplyDeployed / totals.techInch).toFixed(2)
      : 0;

  return `
    <div class="summary-section">
      <h3>📊 Daily KPI Summary</h3>
      <div class="summary-grid">
        <div class="summary-item">
          <strong>Total Open Orders:</strong> ${totals.activeOrders.toLocaleString()}
          <br><small>Orders currently in open status across all verticals</small>
        </div>
        <div class="summary-item">
          <strong>New Orders Created:</strong> ${totals.newOrders.toLocaleString()}
          <br><small>Fresh demand generated yesterday</small>
        </div>
        <div class="summary-item">
          <strong>Total Suppl. deployed:</strong> ${totals.supplyDeployed.toLocaleString()}
          <br><small>Total number of technicians deployed across all verticals</small>
        </div>
        <div class="summary-item">
          <strong>Total tech. incharges:</strong> ${totals.techInch.toLocaleString()}
          <br><small>Total number of technician incharges across all verticals</small>
        </div>
        <div class="summary-item">
          <strong>Assignment Rate:</strong> ${assignmentRate}%
          <br><small>Percentage of open orders that got scheduled for technicians</small>
          <br><em>Formula: (Orders Sch. / (Open Orders + New Orders)) × 100</em>
        </div>

        <div class="summary-item">
          <strong>Tech Incharge vs Supply Deployed:</strong> ${techInchSupplyRatio}
          <br><small>Technicians per Technician incharge</small>
          <br><em>Formula: Supp. depl/Tech Inch.</em>
        </div>
        <div class="summary-item">
          <strong>Active Supply:</strong> ${totals.activeSupply.toLocaleString()}
          <br><small>Total technicians available across all verticals</small>
          <br><em>Formula: Sum of Active supply</em>
        </div>
      </div>
    </div>
  `;
  /*
          <div class="summary-item">
          <strong>Supply Utilization:</strong> ${supplyUtilization}%
          <br><small>Percentage of active supply deployed today</small>
          <br><em>Formula: (Supp. depl / Active supply) × 100</em>
        </div>
  */
};

/**
 * Generate HTML table from query results
 * @param {Array} data - Array of objects representing query results
 * @param {Object} options - Template options
 * @param {string} [options.title] - Email title
 * @param {string} [options.description] - Email description
 * @param {string} [options.summary] - Custom summary text (optional)
 * @param {string} [options.managerCategoryTable] - Manager vs category table HTML (optional)
 * @returns {string} - HTML content
 */
const generateDataTableHTML = (data, options = {}) => {
  const {
    title = "TMS KPI Report",
    description = "Vertical KPI metrics from TMS database",
    summary = null,
    managerCategoryTable = null,
    managerTotals = null,
  } = options;

  if (!data || !Array.isArray(data) || data.length === 0) {
    return generateEmptyDataHTML(title, description);
  }

  // Get column headers from the first row and remove the first column
  const headers = Object.keys(data[0]).slice(1); // Remove the first column from headers

  // Generate table headers
  const headerRow = headers
    .map(
      (header) =>
        `<th style="padding: 12px; background-color: #f8f9fa; border: 1px solid #dee2e6; text-align: left; font-weight: bold;">${escapeHtml(
          header
        )}</th>`
    )
    .join("");

  // Function to generate the style for each cell based on column index and value
  const getCellStyle = (columnIndex, value) => {
    const isNumber = !isNaN(value) && value !== "" && value !== null;

    // Apply red color for zero values in specific columns (excluding certain columns like index 4)
    if (isNumber && value === "0" && columnIndex != 3) {
      return "padding: 12px; border: 1px solid #dee2e6; color: #dc3545; font-weight: bold;";
    } else if (isNumber && columnIndex === 2 && value > "0") {
      // Highlight positive values in column 4 (typically "Orders without a service hub")
      return "padding: 12px; border: 1px solid #dee2e6; color: #dc3545; font-weight: bold;";
    }

    // Default style for other cells
    return "padding: 12px; border: 1px solid #dee2e6;";
  };

  // Function to generate table rows, excluding the first column
  const generateTableRows = (data, headers) => {
    return data
      .map((row) => {
        const cells = headers
          .map((header, columnIndex) => {
            let value = row[header];
            // Handle JSON values (like Title field)
            if (
              typeof value === "string" &&
              value.startsWith('"') &&
              value.endsWith('"')
            ) {
              value = value.slice(1, -1); // Remove quotes
            }
            const displayValue =
              value !== null && value !== undefined ? value : "";
            const cellStyle = getCellStyle(columnIndex, displayValue);
            return `<td style="${cellStyle}">${escapeHtml(
              String(displayValue)
            )}</td>`;
          })
          .join("");
        return `<tr>${cells}</tr>`;
      })
      .join("");
  };

  // Function to generate totals row
  const generateTotalsRow = (data, headers, preCalculatedTotals = null) => {
    const totals = {};

    if (preCalculatedTotals) {
      console.log(
        "HtmlTemplate::generateTotalsRow:: Using SQL-calculated totals"
      );
      // Map pre-calculated totals to table headers
      const totalsMapping = {
        "All time orders": preCalculatedTotals.allTimeOrders,
        "Open orders": preCalculatedTotals.openOrders,
        "New orders": preCalculatedTotals.newOrders,
        "Orders closed": preCalculatedTotals.ordersClosed,
        "Tech Inch.": preCalculatedTotals.techInch,
        "Orders Sch.": preCalculatedTotals.ordersScheduled,
        "Orders Sch. for yest. ": preCalculatedTotals.ordersSchYest,
        "Tasks created": preCalculatedTotals.tasksCreated,
        "Supp. depl": preCalculatedTotals.supplyDeployed,
        "Supply all task closed": preCalculatedTotals.supplyAllTaskClosed,
        "Supply no task update": preCalculatedTotals.supplyNoTaskUpdate,
        "Supply partial task update":
          preCalculatedTotals.supplyPartialTaskUpdate,
        "Active supply": preCalculatedTotals.activeSupply,
        "New supply": preCalculatedTotals.newSupply,
        "Inactive supply": preCalculatedTotals.inactiveSupply,
      };

      headers.forEach((header) => {
        if (header === "Title") {
          totals[header] = "TOTAL";
        } else if (totalsMapping[header] !== undefined) {
          totals[header] = totalsMapping[header].toLocaleString();
        } else {
          // Fallback to JavaScript calculation for unmapped headers
          const sum = data.reduce((acc, row) => {
            const value = parseInt(row[header] || 0);
            return acc + (isNaN(value) ? 0 : value);
          }, 0);
          totals[header] = sum.toLocaleString();
        }
      });
    } else {
      console.log(
        "HtmlTemplate::generateTotalsRow:: Using JavaScript reduce calculation"
      );
      // Calculate totals for each numeric column using JavaScript reduce (fallback)
      headers.forEach((header) => {
        if (header === "Title") {
          totals[header] = "TOTAL";
        } else {
          const sum = data.reduce((acc, row) => {
            const value = parseInt(row[header] || 0);
            return acc + (isNaN(value) ? 0 : value);
          }, 0);
          totals[header] = sum.toLocaleString();
        }
      });
    }

    // Generate totals row with special styling
    const cells = headers
      .map((header) => {
        const value = totals[header];
        const cellStyle =
          "padding: 12px; border: 2px solid #007bff; background-color: #e7f3ff; font-weight: bold; text-align: center;";
        return `<td style="${cellStyle}">${escapeHtml(String(value))}</td>`;
      })
      .join("");

    return `<tr style="border-top: 3px solid #007bff;">${cells}</tr>`;
  };

  const dataRows = generateTableRows(data, headers);
  const totalsRow = generateTotalsRow(data, headers, managerTotals);

  // Use custom summary if provided, otherwise generate default summary
  const summaryContent = summary
    ? summary.includes('<div class="summary-section">')
      ? summary // Already formatted HTML summary
      : `<div class="summary-section"><h3>📊 Executive Summary</h3><p style="line-height: 1.6; font-size: 15px; color: #495057;">${escapeHtml(
          summary
        )}</p></div>` // Plain text summary that needs formatting
    : generateDataSummary(data, managerTotals);

  return `
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>${escapeHtml(title)}</title>
        <style>
            body {
                font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                line-height: 1.6;
                color: #333;
                max-width: 100%;
                margin: 0;
                padding: 10px;
                background-color: #f5f5f5;
            }
            .container {
                background-color: white;
                border-radius: 8px;
                box-shadow: 0 2px 10px rgba(0,0,0,0.1);
                overflow: hidden;
            }
            .header {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                padding: 30px;
                text-align: center;
            }
            .header h1 {
                margin: 0 0 10px 0;
                font-size: 28px;
                font-weight: 300;
            }
            .header p {
                margin: 0;
                opacity: 0.9;
                font-size: 16px;
            }
            .content {
                padding: 15px;
            }
            .summary-section {
                margin-bottom: 30px;
                padding: 20px;
                background-color: #f8f9fa;
                border-radius: 6px;
                border-left: 4px solid #667eea;
            }
            .summary-grid {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
                gap: 15px;
                margin-top: 15px;
            }
            .summary-item {
                background: white;
                padding: 15px;
                border-radius: 4px;
                border: 1px solid #e9ecef;
            }
            .data-table {
                width: 100%;
                border-collapse: collapse;
                margin: 20px 0;
                font-size: 14px;
            }
            .data-table th {
                background-color: #007bff;
                color: black;
                font-weight: bold;
                text-transform: uppercase;
                font-size: 12px;
                letter-spacing: 0.5px;
            }
            .data-table tr:nth-child(even) {
                background-color: #f8f9fa;
            }
            .data-table tr:hover {
                background-color: #e9ecef;
            }
            .table-container {
                overflow-x: auto;
                margin-top: 20px;
                border-radius: 6px;
                box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            }
            .footer {
                margin-top: 30px;
                padding-top: 20px;
                border-top: 1px solid #dee2e6;
                text-align: center;
                color: #6c757d;
                font-size: 12px;
            }
            .stats {
                background-color: #e7f3ff;
                padding: 15px;
                border-radius: 5px;
                margin: 20px 0;
                text-align: center;
            }
            .timestamp {
                font-weight: 600;
                color: #495057;
            }
            @media (max-width: 768px) {
                body {
                    padding: 10px;
                }
                .header {
                    padding: 20px;
                }
                .header h1 {
                    font-size: 24px;
                }
                .content {
                    padding: 20px;
                }
                .summary-grid {
                    grid-template-columns: 1fr;
                }
                table {
                    font-size: 12px;
                }
                th, td {
                    padding: 8px 6px;
                }
            }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>${escapeHtml(title)}</h1>
                <p>${description}</p>
            </div>
            
            <div class="content">
                ${summaryContent}

                ${managerCategoryTable ? managerCategoryTable : ""}

                <div class="table-container">
                    <table class="data-table">
                        <thead>
                            <tr>${headerRow}</tr>
                        </thead>
                        <tbody>
                            ${totalsRow}
                            ${dataRows}
                        </tbody>
                    </table>
                </div>
            </div>

            <div class="definitions-footer">
                <h3 style="color: #495057; margin-bottom: 15px; border-bottom: 2px solid #dee2e6; padding-bottom: 10px;">📖 KPI Definitions & Formulas</h3>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 15px; margin-bottom: 20px;">
                    <div style="background: #f8f9fa; padding: 12px; border-radius: 4px; border-left: 3px solid #007bff;">
                        <strong>All times orders:</strong> All time orders in the system
                    </div>
                    <div style="background: #f8f9fa; padding: 12px; border-radius: 4px; border-left: 3px solid #007bff;">
                        <strong>Open orders:</strong> Orders in first stage (at time of the email)
                    </div>
                    <div style="background: #f8f9fa; padding: 12px; border-radius: 4px; border-left: 3px solid #007bff;">
                        <strong>New orders:</strong> Orders created yesterday
                    </div>
                    <div style="background: #f8f9fa; padding: 12px; border-radius: 4px; border-left: 3px solid #007bff;">
                        <strong>Orders closed:</strong> Orders closed yesterday
                    </div>
                    <div style="background: #f8f9fa; padding: 12px; border-radius: 4px; border-left: 3px solid #007bff;">
                        <strong>Tech Inch.:</strong> Technician incharges (Unique People who deployed technicians)
                    </div>
                    <div style="background: #f8f9fa; padding: 12px; border-radius: 4px; border-left: 3px solid #007bff;">
                        <strong>Orders sch.:</strong> Orders for which atleast 1 subtask was created yesterday
                    </div>
                    <div style="background: #f8f9fa; padding: 12px; border-radius: 4px; border-left: 3px solid #007bff;">
                        <strong>Orders sch. for yest.:</strong> Orders which have subtasks with start time as yesterday
                    </div>
                    <div style="background: #f8f9fa; padding: 12px; border-radius: 4px; border-left: 3px solid #007bff;">
                        <strong>Tasks created:</strong> Total subtasks created yesterday (may not be for the same day)
                    </div>
                    <div style="background: #f8f9fa; padding: 12px; border-radius: 4px; border-left: 3px solid #007bff;">
                        <strong>Supply deployed:</strong> Unique users who were assigned atleast one subtask
                    </div>
                    <div style="background: #f8f9fa; padding: 12px; border-radius: 4px; border-left: 3px solid #007bff;">
                        <strong>Supply all task closed:</strong> Unique users who closed all the subtasks that were assigned to them
                    </div>
                    <div style="background: #f8f9fa; padding: 12px; border-radius: 4px; border-left: 3px solid #007bff;">
                        <strong>Supply no task update:</strong> Unique users who did not update a single task assigned to them
                    </div>
                    <div style="background: #f8f9fa; padding: 12px; border-radius: 4px; border-left: 3px solid #007bff;">
                        <strong>Supply partial task update:</strong> Unique users who update 1 or more tasks but not all
                    </div>
                    <div style="background: #f8f9fa; padding: 12px; border-radius: 4px; border-left: 3px solid #007bff;">
                        <strong>Active supply:</strong> Users who have the primary vertical mapped to this vertical
                    </div>
                    <div style="background: #f8f9fa; padding: 12px; border-radius: 4px; border-left: 3px solid #007bff;">
                        <strong>New supply:</strong> New users created yesterday and mapped to this vertical
                    </div>
                    <div style="background: #f8f9fa; padding: 12px; border-radius: 4px; border-left: 3px solid #007bff;">
                        <strong>Inactive supply:</strong> Users mapped to this vertical (as primary) and are in inactive state
                    </div>
                </div>
            </div>

            <div class="attachments-footer" style="margin-top: 20px; padding: 20px; background-color: #f8f9fa; border-radius: 6px; border-left: 4px solid #28a745;">
                <h3 style="color: #495057; margin-bottom: 15px; font-size: 18px;">📎 Email Attachments Guide</h3>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 15px;">
                    <div style="background: white; padding: 15px; border-radius: 4px; border: 1px solid #dee2e6;">
                        <h4 style="color: #28a745; margin-bottom: 10px; font-size: 16px;">📋 Open Orders CSV</h4>
                        <p style="margin: 0; line-height: 1.5; color: #495057;">
                            <strong>Format:</strong> {VerticalName}_Open_Orders_{Date}.csv<br>
                            <strong>Contains:</strong> Detailed list of all open orders for verticals that have open orders > 0<br>
                            <strong>Columns:</strong> TMS ID, Creation date, Customer name, Description, Pincode, City, State
                        </p>
                    </div>
                    <div style="background: white; padding: 15px; border-radius: 4px; border: 1px solid #dee2e6;">
                        <h4 style="color: #007bff; margin-bottom: 10px; font-size: 16px;">📝 Tasks CSV</h4>
                        <p style="margin: 0; line-height: 1.5; color: #495057;">
                            <strong>Format:</strong> {VerticalName}_Tasks_{Date}.csv<br>
                            <strong>Contains:</strong> Task details for all verticals with task activity<br>
                            <strong>Columns:</strong> Assignee Name, Emp. Code, Task creation date/time, Task date/time, Current status, Updated on, Created by, TMS ID, Location details
                        </p>
                    </div>
                </div>
                <div style="margin-top: 15px; padding: 12px; background-color: #e7f3ff; border-radius: 4px; border-left: 3px solid #007bff;">
                    <p style="margin: 0; font-size: 14px; color: #495057;">
                        <strong>Note:</strong> All attachment filenames are prefixed with the vertical name for easy identification.
                        Open Orders attachments are only included for verticals with active open orders, while Tasks attachments are included for all verticals with task activity during the reporting period.
                    </p>
                </div>
            </div>

            <div class="footer">
                <p>Generated on: <span class="timestamp">${getISTTimestamp()} IST</span></p>
                <p>Total Records: <strong>${data.length}</strong></p>
                ${
                  managerTotals
                    ? "<p><strong>Note:</strong> All KPI totals and calculations are computed directly from the database for accuracy and consistency.</p>"
                    : ""
                }
            </div>
        </div>
    </body>
    </html>
  `;
};

/**
 * Generate HTML for empty data scenarios
 * @param {string} title - Email title
 * @param {string} description - Email description
 * @returns {string} - HTML content for empty data
 */
const generateEmptyDataHTML = (title, description) => {
  return `
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>${escapeHtml(title)}</title>
        <style>
            body {
                font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                line-height: 1.6;
                color: #333;
                max-width: 800px;
                margin: 0 auto;
                padding: 20px;
                background-color: #f5f5f5;
            }
            .container {
                background-color: white;
                border-radius: 8px;
                box-shadow: 0 2px 10px rgba(0,0,0,0.1);
                overflow: hidden;
            }
            .header {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                padding: 30px;
                text-align: center;
            }
            .header h1 {
                margin: 0 0 10px 0;
                font-size: 28px;
                font-weight: 300;
            }
            .header p {
                margin: 0;
                opacity: 0.9;
                font-size: 16px;
            }
            .no-data {
                background-color: #fff3cd;
                border: 1px solid #ffeaa7;
                color: #856404;
                padding: 20px;
                border-radius: 5px;
                margin: 20px;
            }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>${escapeHtml(title)}</h1>
                <p>${description}</p>
            </div>

            <div class="no-data">
                <h3>No Data Available</h3>
                <p>The query returned no results for the specified criteria.</p>
                <p>Generated on: ${getISTTimestamp()} IST</p>
                <p><strong>Note:</strong> All KPI totals and calculations are computed directly from the database for accuracy and consistency.</p>
            </div>

            <div class="definitions-footer" style="margin: 20px;">
                <h3 style="color: #495057; margin-bottom: 15px; border-bottom: 2px solid #dee2e6; padding-bottom: 10px;">📖 KPI Definitions & Formulas</h3>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 15px; margin-bottom: 20px;">
                    <div style="background: #f8f9fa; padding: 12px; border-radius: 4px; border-left: 3px solid #007bff;">
                        <strong>All times orders:</strong> All time orders in the system
                    </div>
                    <div style="background: #f8f9fa; padding: 12px; border-radius: 4px; border-left: 3px solid #007bff;">
                        <strong>Open orders:</strong> Orders in first stage (at time of the email)
                    </div>
                    <div style="background: #f8f9fa; padding: 12px; border-radius: 4px; border-left: 3px solid #007bff;">
                        <strong>New orders:</strong> Orders created yesterday
                    </div>
                    <div style="background: #f8f9fa; padding: 12px; border-radius: 4px; border-left: 3px solid #007bff;">
                        <strong>Orders closed:</strong> Orders closed yesterday
                    </div>
                    <div style="background: #f8f9fa; padding: 12px; border-radius: 4px; border-left: 3px solid #007bff;">
                        <strong>Tech Inch.:</strong> Technician incharges (Unique People who deployed technicians)
                    </div>
                    <div style="background: #f8f9fa; padding: 12px; border-radius: 4px; border-left: 3px solid #007bff;">
                        <strong>Orders sch.:</strong> Orders for which atleast 1 subtask was created yesterday
                    </div>
                    <div style="background: #f8f9fa; padding: 12px; border-radius: 4px; border-left: 3px solid #007bff;">
                        <strong>Orders sch. for yest.:</strong> Orders which have subtasks with start time as yesterday
                    </div>
                    <div style="background: #f8f9fa; padding: 12px; border-radius: 4px; border-left: 3px solid #007bff;">
                        <strong>Tasks created:</strong> Total subtasks created yesterday (may not be for the same day)
                    </div>
                    <div style="background: #f8f9fa; padding: 12px; border-radius: 4px; border-left: 3px solid #007bff;">
                        <strong>Supply deployed:</strong> Unique users who were assigned atleast one subtask for the day
                    </div>
                    <div style="background: #f8f9fa; padding: 12px; border-radius: 4px; border-left: 3px solid #007bff;">
                        <strong>Supply all task closed:</strong> Unique users who closed all the subtasks that were assigned to them
                    </div>
                    <div style="background: #f8f9fa; padding: 12px; border-radius: 4px; border-left: 3px solid #007bff;">
                        <strong>Supply no task update:</strong> Unique users who did not update a single task assigned to them
                    </div>
                    <div style="background: #f8f9fa; padding: 12px; border-radius: 4px; border-left: 3px solid #007bff;">
                        <strong>Supply partial task update:</strong> Unique users who update 1 or more tasks but not all
                    </div>
                    <div style="background: #f8f9fa; padding: 12px; border-radius: 4px; border-left: 3px solid #007bff;">
                        <strong>Active supply:</strong> Users who have the primary vertical mapped to this vertical
                    </div>
                    <div style="background: #f8f9fa; padding: 12px; border-radius: 4px; border-left: 3px solid #007bff;">
                        <strong>New supply:</strong> New users created yesterday and mapped to this vertical
                    </div>
                    <div style="background: #f8f9fa; padding: 12px; border-radius: 4px; border-left: 3px solid #007bff;">
                        <strong>Inactive supply:</strong> Users mapped to this vertical (as primary) and are in inactive state
                    </div>
                </div>
            </div>
        </div>
    </body>
    </html>
  `;
};

/**
 * Escape HTML special characters to prevent XSS
 * @param {string} text - Text to escape
 * @returns {string} - Escaped text
 */
const escapeHtml = (text) => {
  const map = {
    "&": "&amp;",
    "<": "&lt;",
    ">": "&gt;",
    '"': "&quot;",
    "'": "&#039;",
  };
  return text.replace(/[&<>"']/g, (m) => map[m]);
};

module.exports = {
  generateDataTableHTML,
  generateEmptyDataHTML,
  generateDataSummary,
  escapeHtml,
  getISTTimestamp,
};
