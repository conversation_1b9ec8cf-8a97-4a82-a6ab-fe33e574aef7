/**
 * Get current timestamp in IST format
 * @returns {string} - Formatted timestamp
 */
const getISTTimestamp = () => {
  const now = new Date();
  const istOffset = 5.5 * 60 * 60 * 1000; // IST is UTC+5:30
  const istTime = new Date(now.getTime() + istOffset);
  return istTime.toISOString().replace("T", " ").substring(0, 19);
};

/**
 * Generate data summary for KPI metrics
 * @param {Array} data - Array of KPI data objects
 * @returns {string} - HTML summary content
 */
const generateDataSummary = (data) => {
  if (!data || !Array.isArray(data) || data.length === 0) {
    return "<p>No data available for analysis.</p>";
  }

  // Calculate totals across all verticals
  const totals = data.reduce(
    (acc, row) => {
      acc.allTimeOrders += parseInt(row["All time orders"] || 0);
      acc.activeOrders += parseInt(row["Open orders"] || 0);
      acc.newOrders += parseInt(row["New orders"] || 0);
      acc.ordersAssigned += parseInt(row["Orders assigned"] || 0);
      acc.ordersClosed += parseInt(row["Orders closed"] || 0);
      acc.activeSupply += parseInt(row["Active supply"] || 0);
      acc.newSupply += parseInt(row["New supply"] || 0);
      acc.supplyDeployed += parseInt(row["Supply deployed"] || 0);
      return acc;
    },
    {
      allTimeOrders: 0,
      activeOrders: 0,
      newOrders: 0,
      ordersAssigned: 0,
      ordersClosed: 0,
      activeSupply: 0,
      newSupply: 0,
      supplyDeployed: 0,
    }
  );

  const assignmentRate =
    totals.activeOrders > 0
      ? ((totals.ordersAssigned / totals.activeOrders) * 100).toFixed(1)
      : 0;

  const supplyUtilization =
    totals.activeSupply > 0
      ? ((totals.supplyDeployed / totals.activeSupply) * 100).toFixed(1)
      : 0;

  return `
    <div class="summary-section">
      <h3>📊 Daily KPI Summary</h3>
      <div class="summary-grid">
        <div class="summary-item">
          <strong>Total Open Orders:</strong> ${totals.activeOrders.toLocaleString()}
          <br><small>Orders currently in open status across all verticals</small>
        </div>
        <div class="summary-item">
          <strong>New Orders Created:</strong> ${totals.newOrders.toLocaleString()}
          <br><small>Fresh demand generated yesterday</small>
        </div>
        <div class="summary-item">
          <strong>Assignment Rate:</strong> ${assignmentRate}%
          <br><small>Percentage of open orders that got assigned to technicians</small>
        </div>
        <div class="summary-item">
          <strong>Active Supply:</strong> ${totals.activeSupply.toLocaleString()}
          <br><small>Total technicians available across all verticals</small>
        </div>
        <div class="summary-item">
          <strong>Supply Utilization:</strong> ${supplyUtilization}%
          <br><small>Percentage of active supply deployed today</small>
        </div>
      </div>
    </div>
  `;
};

/**
 * Generate HTML table from query results
 * @param {Array} data - Array of objects representing query results
 * @param {Object} options - Template options
 * @param {string} [options.title] - Email title
 * @param {string} [options.description] - Email description
 * @returns {string} - HTML content
 */
const generateDataTableHTML = (data, options = {}) => {
  const {
    title = "TMS KPI Report",
    description = "Vertical KPI metrics from TMS database",
  } = options;

  if (!data || !Array.isArray(data) || data.length === 0) {
    return generateEmptyDataHTML(title, description);
  }

  // Get column headers from the first row and remove the first column
  const headers = Object.keys(data[0]).slice(1); // Remove the first column from headers

  // Generate table headers
  const headerRow = headers
    .map(
      (header) =>
        `<th style="padding: 12px; background-color: #f8f9fa; border: 1px solid #dee2e6; text-align: left; font-weight: bold;">${escapeHtml(
          header
        )}</th>`
    )
    .join("");

  // Function to generate the style for each cell based on column index and value
  const getCellStyle = (columnIndex, value) => {
    const isNumber = !isNaN(value) && value !== "" && value !== null;

    // Apply red color for zero values in specific columns (excluding certain columns like index 4)
    if (isNumber && value === "0" && columnIndex != 3) {
      return "padding: 12px; border: 1px solid #dee2e6; color: #dc3545; font-weight: bold;";
    } else if (isNumber && columnIndex === 2 && value > "0") {
      // Highlight positive values in column 4 (typically "Orders without a service hub")
      return "padding: 12px; border: 1px solid #dee2e6; color: #dc3545; font-weight: bold;";
    }

    // Default style for other cells
    return "padding: 12px; border: 1px solid #dee2e6;";
  };

  // Function to generate table rows, excluding the first column
  const generateTableRows = (data, headers) => {
    return data
      .map((row) => {
        const cells = headers
          .map((header, columnIndex) => {
            let value = row[header];
            // Handle JSON values (like Title field)
            if (
              typeof value === "string" &&
              value.startsWith('"') &&
              value.endsWith('"')
            ) {
              value = value.slice(1, -1); // Remove quotes
            }
            const displayValue =
              value !== null && value !== undefined ? value : "";
            const cellStyle = getCellStyle(columnIndex, displayValue);
            return `<td style="${cellStyle}">${escapeHtml(
              String(displayValue)
            )}</td>`;
          })
          .join("");
        return `<tr>${cells}</tr>`;
      })
      .join("");
  };

  const dataRows = generateTableRows(data, headers);

  const summaryContent = generateDataSummary(data);

  return `
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>${escapeHtml(title)}</title>
        <style>
            body {
                font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                line-height: 1.6;
                color: #333;
                max-width: 1200px;
                margin: 0 auto;
                padding: 20px;
                background-color: #f5f5f5;
            }
            .container {
                background-color: white;
                border-radius: 8px;
                box-shadow: 0 2px 10px rgba(0,0,0,0.1);
                overflow: hidden;
            }
            .header {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                padding: 30px;
                text-align: center;
            }
            .header h1 {
                margin: 0 0 10px 0;
                font-size: 28px;
                font-weight: 300;
            }
            .header p {
                margin: 0;
                opacity: 0.9;
                font-size: 16px;
            }
            .content {
                padding: 30px;
            }
            .summary-section {
                margin-bottom: 30px;
                padding: 20px;
                background-color: #f8f9fa;
                border-radius: 6px;
                border-left: 4px solid #667eea;
            }
            .summary-grid {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
                gap: 15px;
                margin-top: 15px;
            }
            .summary-item {
                background: white;
                padding: 15px;
                border-radius: 4px;
                border: 1px solid #e9ecef;
            }
            .data-table {
                width: 100%;
                border-collapse: collapse;
                margin: 20px 0;
                font-size: 14px;
            }
            .data-table th {
                background-color: #007bff;
                color: black;
                font-weight: bold;
                text-transform: uppercase;
                font-size: 12px;
                letter-spacing: 0.5px;
            }
            .data-table tr:nth-child(even) {
                background-color: #f8f9fa;
            }
            .data-table tr:hover {
                background-color: #e9ecef;
            }
            .table-container {
                overflow-x: auto;
                margin-top: 20px;
                border-radius: 6px;
                box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            }
            .footer {
                margin-top: 30px;
                padding-top: 20px;
                border-top: 1px solid #dee2e6;
                text-align: center;
                color: #6c757d;
                font-size: 12px;
            }
            .stats {
                background-color: #e7f3ff;
                padding: 15px;
                border-radius: 5px;
                margin: 20px 0;
                text-align: center;
            }
            .timestamp {
                font-weight: 600;
                color: #495057;
            }
            @media (max-width: 768px) {
                body {
                    padding: 10px;
                }
                .header {
                    padding: 20px;
                }
                .header h1 {
                    font-size: 24px;
                }
                .content {
                    padding: 20px;
                }
                .summary-grid {
                    grid-template-columns: 1fr;
                }
                table {
                    font-size: 12px;
                }
                th, td {
                    padding: 8px 6px;
                }
            }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>${escapeHtml(title)}</h1>
                <p>${description}</p>
            </div>
            
            <div class="content">
                ${summaryContent}

                <div class="table-container">
                    <table class="data-table">
                        <thead>
                            <tr>${headerRow}</tr>
                        </thead>
                        <tbody>
                            ${dataRows}
                        </tbody>
                    </table>
                </div>
            </div>
            
            <div class="footer">
                <p>Generated on: <span class="timestamp">${getISTTimestamp()} IST</span></p>
                <p>Total Records: <strong>${data.length}</strong></p>
            </div>
        </div>
    </body>
    </html>
  `;
};

/**
 * Generate HTML for empty data scenarios
 * @param {string} title - Email title
 * @param {string} description - Email description
 * @returns {string} - HTML content for empty data
 */
const generateEmptyDataHTML = (title, description) => {
  return `
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>${escapeHtml(title)}</title>
        <style>
            body {
                font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                line-height: 1.6;
                color: #333;
                max-width: 800px;
                margin: 0 auto;
                padding: 20px;
                background-color: #f5f5f5;
            }
            .container {
                background-color: white;
                border-radius: 8px;
                box-shadow: 0 2px 10px rgba(0,0,0,0.1);
                overflow: hidden;
            }
            .header {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                padding: 30px;
                text-align: center;
            }
            .header h1 {
                margin: 0 0 10px 0;
                font-size: 28px;
                font-weight: 300;
            }
            .header p {
                margin: 0;
                opacity: 0.9;
                font-size: 16px;
            }
            .no-data {
                background-color: #fff3cd;
                border: 1px solid #ffeaa7;
                color: #856404;
                padding: 20px;
                border-radius: 5px;
                margin: 20px;
            }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>${escapeHtml(title)}</h1>
                <p>${description}</p>
            </div>

            <div class="no-data">
                <h3>No Data Available</h3>
                <p>The query returned no results for the specified criteria.</p>
                <p>Generated on: ${getISTTimestamp()} IST</p>
            </div>
        </div>
    </body>
    </html>
  `;
};

/**
 * Escape HTML special characters to prevent XSS
 * @param {string} text - Text to escape
 * @returns {string} - Escaped text
 */
const escapeHtml = (text) => {
  const map = {
    "&": "&amp;",
    "<": "&lt;",
    ">": "&gt;",
    '"': "&quot;",
    "'": "&#039;",
  };
  return text.replace(/[&<>"']/g, (m) => map[m]);
};

module.exports = {
  generateDataTableHTML,
  generateEmptyDataHTML,
  generateDataSummary,
  escapeHtml,
  getISTTimestamp,
};
