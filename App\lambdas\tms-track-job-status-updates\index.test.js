const axios = require("axios");
const { handler } = require("./index");

jest.mock("axios");

beforeAll(() => {
  jest.spyOn(console, "error").mockImplementation(() => {});
  jest.spyOn(console, "warn").mockImplementation(() => {});
  jest.spyOn(console, "log").mockImplementation(() => {});
});

describe("tms-track-job-status-updates :: handler", () => {
  const mockEvent = {
    payload: {
      tms_order_id: "12345",
      tms_order_status: { label: "In Progress" },
    },
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("should post data to the apiUrl and return a successful response", async () => {
    const mockResponse = {
      status: 200,
      data: { success: true },
    };
    axios.post.mockResolvedValue(mockResponse);

    const result = await handler(mockEvent);

    expect(axios.post).toHaveBeenCalledWith(
      expect.stringContaining("https://script.google.com"),
      {
        job_id: "12345",
        status: "In Progress",
      },
      {
        headers: { "Content-Type": "application/json" },
      }
    );

    expect(result).toEqual({
      statusCode: 200,
      body: { success: true },
    });
  });

  it("should handle error from axios.post gracefully", async () => {
    axios.post.mockRejectedValue({
      message: "Network error",
      response: { status: 500 },
    });

    const result = await handler(mockEvent);

    expect(result).toEqual({
      statusCode: 500,
      body: {
        message: "Failed to send job status update.",
        error: "Network error",
      },
    });
  });

  it("should default to 500 if error response is undefined", async () => {
    axios.post.mockRejectedValue({
      message: "Unknown error",
    });

    const result = await handler(mockEvent);

    expect(result).toEqual({
      statusCode: 500,
      body: {
        message: "Failed to send job status update.",
        error: "Unknown error",
      },
    });
  });
});
