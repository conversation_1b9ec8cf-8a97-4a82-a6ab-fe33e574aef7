const axios = require("axios");

class http_utils {
  setAuth(username, password) {
    this.basicAuth = {
      auth: { username, password },
    };
  }

  performDeleteCall = (
    path,
    params,
    onComplete,
    onError,
    avoidPathPrefix = false
  ) => {
    params = {
      ...params,
      ...this.basicAuth,
    };
    axios
      .delete(path, params)
      .then((data) => {
        // if(data.code)
        onComplete(data);
      })
      .catch((error) => {
        onError(error);
      });
  };

  performPutCall = (path, params, onComplete, onError) => {
    // console.log("Default headers",axios.defaults.headers.common['Authorization'])
    params = {
      ...params,
      // additional params
    };

    axios
      .put(path, params, this.basicAuth)
      .then((data) => {
        // if(data.code)
        onComplete(data);
      })
      .catch((error) => {
        if (!this.isUnauthorizedAccess(error)) {
          onError(error);
        }
      });
  };

  performPostCall = (
    path,
    params,
    onComplete,
    onError,
    avoidPathPrefix = false
  ) => {
    console.log("performPostCall params", params);
    const headers = {
      "Content-Type": "application/json",
      "x-api-key": 123,
    };

    params = {
      ...params,
      ...this.basicAuth,
      // additional params
    };
    console.log("performPostCall modified params", params);
    axios
      .post(path, params, { headers })
      .then((data) => {
        // if(data.code)
        onComplete(data.data);
      })
      .catch((error) => {
        onError(error);
      });
  };

  performGetCall = (
    path,
    params,
    onComplete,
    onError,
    avoidPathPrefix = false
  ) => {
    params = {
      ...params,
    };
    console.log("performGetCall :: path", path);
    console.log("performGetCall :: params", params);
    axios
      .get(path, {
        params: params,
      })
      .then((data) => {
        console.log("Calling on complete", data);
        onComplete(data);
      })
      .catch((error) => {
        console.log("performGetCall :: error", error);

        if (!this.isUnauthorizedAccess(error)) {
          onError(error);
        }
      });
  };
}
module.exports = new http_utils();
