import { Router, Request, Response } from 'express';

const router = Router();

/**
 * @swagger
 * /health:
 *   get:
 *     summary: Check service health
 *     description: Returns the health status of the service
 *     responses:
 *       200:
 *         description: Service is healthy
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: Service is healthy
 *                 timestamp:
 *                   type: string
 *                   example: 2023-07-20T12:34:56.789Z
 */
router.get('/health', (req: Request, res: Response) => {
  res.status(200).json({
    status: true,
    message: 'Service is healthy',
    timestamp: new Date().toISOString(),
  });
});

export default router;
