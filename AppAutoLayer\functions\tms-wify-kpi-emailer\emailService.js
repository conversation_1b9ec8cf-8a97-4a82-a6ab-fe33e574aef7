const nodemailer = require("nodemailer");

/**
 * Get email credentials from environment variables
 * Following the established pattern from existing codebase
 */
const getEmailCredentials = () => {
  return {
    EMAIL_SMTP_HOST: process.env.EMAIL_SMTP_HOST || "smtp.gmail.com",
    EMAIL_SMTP_PORT: process.env.EMAIL_SMTP_PORT || 465,
    EMAIL_AUTH_USER: process.env.EMAIL_AUTH_USER || "<EMAIL>",
    EMAIL_AUTH_PASS: process.env.EMAIL_AUTH_PASS || "defaultpassword",
    EMAIL_FROM: process.env.EMAIL_FROM || "<EMAIL>",
  };
};

/**
 * Send email with the provided data
 * @param {Object} emailData - Email configuration object
 * @param {string} emailData.to - Recipient email addresses (comma-separated)
 * @param {string} emailData.subject - Email subject
 * @param {string} emailData.htmlContent - HTML content for email body
 * @param {string} [emailData.cc] - CC email addresses (optional)
 * @param {string} [emailData.bcc] - BCC email addresses (optional)
 * @param {Array} [emailData.attachments] - Array of attachment objects (optional)
 * @returns {Promise<Object>} - Email send result
 */
const sendEmail = async (emailData) => {
  const {
    to,
    subject,
    htmlContent,
    cc = "",
    bcc = "",
    attachments = [],
  } = emailData;

  if (!to || !subject || !htmlContent) {
    throw new Error(
      "Missing required email parameters: to, subject, or htmlContent"
    );
  }

  const emailCredentials = getEmailCredentials();

  // Create SMTP transporter
  const smtpTransport = nodemailer.createTransport({
    host: emailCredentials.EMAIL_SMTP_HOST,
    port: emailCredentials.EMAIL_SMTP_PORT,
    secure: emailCredentials.EMAIL_SMTP_PORT == 465, // true for 465, false for other ports
    auth: {
      user: emailCredentials.EMAIL_AUTH_USER,
      pass: emailCredentials.EMAIL_AUTH_PASS,
    },
  });

  // Email configuration
  const mailOptions = {
    from: `WIFY - TMS Emailer <${emailCredentials.EMAIL_FROM}>`,
    to: to,
    cc: cc,
    bcc: bcc,
    subject: `[TMS KPI] ${subject}`,
    html: htmlContent,
    attachments: attachments,
  };

  try {
    console.log("EmailService::sendEmail:: Attempting to send email");
    console.log("EmailService::sendEmail:: To:", to);
    console.log("EmailService::sendEmail:: Subject:", subject);

    const result = await smtpTransport.sendMail(mailOptions);

    console.log("EmailService::sendEmail:: Email sent successfully");
    console.log("EmailService::sendEmail:: Message ID:", result.messageId);

    return {
      status: "Success",
      messageId: result.messageId,
      response: result,
    };
  } catch (error) {
    console.error("EmailService::sendEmail:: Email send failed:", error);
    return {
      status: "Failed",
      error: error.message,
      response: error,
    };
  }
};

module.exports = {
  sendEmail,
  getEmailCredentials,
};
