import sequelize, {
  testConnection as testSequelizeConnection,
} from "./sequelize";
import { initializeModels, createTablesIfNotExist } from "../models";

// Test database connection and initialize
export const testConnection = async (): Promise<void> => {
  try {
    console.log("🔄 Testing database connection...");

    // Connect to the database using Sequelize
    await testSequelizeConnection();
    console.log("✅ Database connection established");

    // Initialize models
    initializeModels();

    // Auto-create tables if they don't exist and alter them if schema changed
    console.log("🔄 Checking, creating, and updating tables if needed...");
    await createTablesIfNotExist();

    // We're using alter: true in createTablesIfNotExist to automatically update the schema
    // This will add the new 'day' column to the Capacities table

    console.log("✅ Models initialized and synced");
  } catch (error) {
    console.error("❌ Unable to connect to the database:", error);
    throw error;
  }
};

export default sequelize;
