# TMS Bookings Service Lambda (Dev)

This is a development version of the TMS Bookings Service Lambda function. It provides APIs for managing bookings and capacity data for the TMS system.

## Features

- TypeScript-based implementation
- Express.js REST API framework
- Sequelize ORM for database operations
- Swagger-based API documentation
- API key authentication
- AWS Lambda integration with serverless-express
- Environment-specific configuration
- Support for large payloads (up to 10MB)
- Optimized batch operations for capacity management

## Architecture

This service follows a microservice architecture pattern:

```
tms-services-bookings-m-dev/
├── src/
│   ├── config/         # Configuration files
│   ├── controllers/    # API controllers
│   ├── middleware/     # Express middleware
│   ├── models/         # Sequelize models
│   ├── routes/         # API routes
│   ├── services/       # Business logic
│   ├── utils/          # Utility functions
│   ├── app.ts          # Express application setup
│   ├── index.ts        # Lambda handler
│   └── localRunner.ts  # Local development server
├── .env.development    # Development environment variables
├── .env.production     # Production environment variables
├── package.json        # Dependencies and scripts
└── tsconfig.json       # TypeScript configuration
```

## Setup

1. Install dependencies:

   ```
   npm install
   ```

2. Create environment-specific `.env` files:

   - `.env.development` - Development environment configuration
   - `.env.production` - Production environment configuration

   Each file should contain:

   ```
   # API Access Key for authentication
   API_ACCESS_KEY=tms-bookings-service-dev-key-123456

   # Server configuration
   PORT=3001
   NODE_ENV=development

   # AWS Configuration
   AWS_REGION=ap-south-1

   # Logging
   LOG_LEVEL=debug

   # Database Configuration
   DB_HOST=localhost
   DB_PORT=5432
   DB_USERNAME=postgres
   DB_PASSWORD=your-password
   DB_NAME=tms_bookings_dev
   ```

3. Build the project:
   ```
   npm run build
   ```

## Local Development

### Running the Service Locally

To run the service locally:

```
npm start
```

For development with auto-reload:

```
npm run dev
```

This will:

1. Load environment variables from `.env.development`
2. Connect to the database
3. Start the Express server on the configured port (default: 3001)
4. Make Swagger documentation available at http://localhost:3001/api-docs

### API Documentation

When running locally, Swagger documentation is available at:

```
http://localhost:3001/api-docs
```

This provides an interactive UI to:

- View all available endpoints
- Test API calls directly from the browser
- See request/response schemas
- Understand authentication requirements

## Endpoints

- `GET /health` - Health check endpoint
- `GET /test-credentials` - Test API credentials
- `GET /api-docs` - Swagger API documentation
- Additional endpoints are documented in the Swagger UI

## Authentication

All endpoints (except `/health`) require an API key to be provided in the `x-api-key` header.

For development, use:

```
x-api-key: tms-bookings-service-dev-key-123456
```

## Deployment

This lambda function is designed to be deployed to AWS Lambda with a layer for dependencies.

The deployment process:

1. Builds the TypeScript code
2. Creates a layer with all node_modules
3. Packages the function code
4. Uploads to AWS Lambda

### Layer Structure

The lambda layer is structured as:

```
/opt/nodejs/node_modules/  # Node.js dependencies
```

### Lambda Handler

The Lambda handler is defined in `src/index.ts` and uses `aws-serverless-express` to proxy requests to the Express application.

## Testing

### Unit Tests

Run unit tests with:

```
npm test
```

For faster testing without coverage:

```
npm run test:simple
```

For watch mode during development:

```
npm run test:watch
```

### API Integration Tests

For comprehensive API testing:

```
npm run test:api
```

### Extending the Test Suite

When adding new API endpoints or functionality, always update the `test-api.js` file to include tests for the new features. This ensures:

1. All API endpoints are properly tested
2. Changes don't break existing functionality
3. Documentation of expected behavior through tests

To add a new test to `test-api.js`:

1. Follow the existing pattern in the file
2. Add a new test section with a descriptive name
3. Use the `printResult` helper function to validate responses
4. Update the test count in the summary section

## Manual API Testing

To manually test the API endpoints:

1. Start the local server:

   ```
   npm start
   ```

2. Use one of these methods:

   - **Swagger UI**: Navigate to http://localhost:3001/api-docs for interactive API documentation

   - **cURL**: Use command line requests

     ```
     # Health check
     curl http://localhost:3001/health

     # Test credentials
     curl -H "x-api-key: tms-bookings-service-dev-key-123456" http://localhost:3001/test-credentials
     ```

   - **Postman**: Import the Swagger documentation for a complete collection

## Batch Capacity Optimization

The batch capacity API has been optimized to use a single database operation for both inserts and updates:

- Uses Sequelize's `bulkCreate` with `updateOnDuplicate` option
- Eliminates separate database queries to find existing records
- Reduces database round trips and improves performance
- Handles large batches efficiently (up to 5000 records per batch)

This optimization is particularly important for high-volume scenarios where multiple capacity records need to be created or updated simultaneously.
