const { Client } = require("pg");
const { google } = require("googleapis");
const fs = require("fs");
const SERVICE_ACCOUNT_FILE = "/tmp/credentials.json";
const SCOPES = ["https://www.googleapis.com/auth/spreadsheets"];

// const userId = "177886be-ba77-410a-82b2-deb762b8c1c4";
const userId = "74095c4e-5d83-4607-895f-f0df5815c0c2";
const selectedLocationGrps = [
  "KLH",
  "AMD",
  "BOM",
  "PNQ",
  "BDQ",
  "IDR",
  "STV",
  "NAG",
  "KOA",
  "BBI",
  "BLR",
  "HYD",
  "VGA",
  "DEL",
  "GGN",
  "IGI",
  "GZB",
  "TPJ",
  "TRV",
  "MAA",
  "CMB",
  "COK",
  "LKO",
  "BHO",
  "GO<PERSON>",
  "KRL",
  "JAI",
  "ISK",
  "UTP",
  "WTB",
  "VTZ",
  "UDR",
  "AJL",
  "IHP",
  "TCR",
  "BZA",
  "NIA",
  "KTU",
  "IXR",
  "MYQ",
  "GOI",
  "GAU",
  "IXC",
  "BHJ",
  "RPR",
];
const attendanceData = {
  reqObj: {
    email_id: "<EMAIL>",
    subject: "WIFY TMS Attendance requests dump Jan-09-2025 16:42",
    customer_info_fields: ["cust_mobile", "cust_full_name", "cust_email"],
    request_info_fields: [
      "request_description",
      "request_req_date",
      "request_priority",
      "request_labels",
      "request_cc_users",
      "creation_date",
      "vertical_title",
    ],
    field_label_mapping: {
      cust_mobile: "Mobile(+91)",
      cust_full_name: "Name",
      cust_email: "Email",
      request_description: "Description",
      request_req_date: "Req. Service Date",
      request_priority: "Priority",
      request_labels: "Labels",
      request_cc_users: "CC users",
      creation_date: "Creation Date",
      vertical_title: "Request vertical",
      cust_line_0: "Flat no",
      cust_line_1: "Building/Apartment name",
      cust_line_2: "Line 1",
      cust_line_3: "Line 2",
      cust_pincode: "Pincode",
      cust_city: "City",
      cust_state: "State",
      request_id: "Request ID",
      technician_name: "Technician name",
      technician_code: "Employee Code",
      day: "Day",
      time_slot: "Time slot",
      sbtsk_type_name: "Task type",
      curr_status: "Task status",
      attendance: "Attendance",
      start_date: "Start Date",
      start_time: "Start time",
      end_time: "End time",
      task_geo_verification_status: "GEO verification status",
      gai_rating: "GAI rating",
      gai_remarks: "GAI remarks",
    },
    selected_columns: [
      "cust_mobile",
      "cust_full_name",
      "cust_email",
      "request_description",
      "request_req_date",
      "request_priority",
      "request_labels",
      "request_cc_users",
      "creation_date",
      "vertical_title",
    ],
    filters:
      '{"days":["2024-01-01T00:00:00.000Z","2025-01-01T00:00:00.000Z"],"time_slots":["2024-01-01T00:00:00.000Z","2024-01-01T01:55:33.727Z"],"vertical_list":[5,4]}',
    pagination: "{}",
    org_id: 2,
    usr_id: userId,
    ip_address: "::1",
    user_agent:
      "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
  },
  filters: {
    days: ["2024-01-01T00:00:00.000Z", "2025-01-01T00:00:00.000Z"],
  },
};
const currentDate = new Date();
const endDate = new Date();
endDate.setDate(currentDate.getDate() - 365);
const customerReqData = {
  formData: {
    email_id: "<EMAIL>",
    subject: "WIFY TMS Customer requests dump Jan-13-2025 15:28",
    org_info: ["brand_name", "service_type", "vertical_fr_req_dump"],
    address_info_fields: ["cust_state", "location_group"],
    onfield_task_fields: ["sp_first_task_date", "sp_last_task_date"],
    SP_specific_fields: [
      "7033ab8c-22c1-4cf0-94e2-2c104b2bec26",
      "b274d89e-fafd-4789-999c-a848cd8e6483",
      "9ad89a24-27ab-44e1-9fe4-3f20be4316bf",
      "ceedcf1d-1f6e-4cb0-9cec-e6ae8e5c73e1",
      "b8909ff9-edd2-4572-a82c-fe4720becbc0",
      "9d30592d-deba-490e-887f-2ae78d094486",
      "7235dc23-6745-4780-971e-6dd6f9930407",
      "cd989a04-9a9c-41d7-ab14-3763c5861a1d",
      "519457b0-60fd-41a5-b1a7-02867d7a3b18",
      "c88cf034-8459-4dde-b0a2-5cadbd72a34e",
      "9f335621-9a6e-42bb-929f-561bf9a80c0f",
    ],
    pi_info: ["final_amount", "sp_sent_for_billing"],
    payout_info: ["total_quantity"],
    line_item_data: ["sp_total_quantity"],
    calculated_task_data: ["mandays"],
    field_label_mapping: {
      brand_name: "Brand Name",
      service_type: "Service Type",
      vertical_fr_req_dump: "Vertical",
      cust_mobile: "Mobile(+91)",
      cust_full_name: "Name",
      cust_email: "Email",
      request_description: "Description",
      request_req_date: "Req. Service Date",
      request_priority: "Priority",
      request_labels: "Labels",
      request_cc_users: "CC users",
      creation_date: "Creation Date",
      vertical_title: "Request vertical",
      cust_line_0: "Flat no",
      cust_line_1: "Building/Apartment name",
      cust_line_2: "Line 1",
      cust_line_3: "Line 2",
      cust_pincode: "Pincode",
      cust_city: "City",
      cust_state: "State",
      location_group: "Location Group",
      assignee_name: "Assignee name",
      task_status: "Task status",
      task_start_date: "Task start date",
      assigned_by: "Assigned by",
      assigned_date: "Assigned date",
      assigned_time: "Assigned time",
      task_status_remark: "Task status remark",
      task_geo_verification_status: "GEO verification status",
      "assignee_phone_no.": "Assignee Phone No.",
      sp_first_task_date: "SP First task date",
      sp_last_task_date: "SP Last task date",
      gai_remarks: "GAI remarks",
      "08ce8925-8923-4c80-923b-45e1dd17e0b0": "Client ID",
      "7033ab8c-22c1-4cf0-94e2-2c104b2bec26": "Business Unit",
      "b274d89e-fafd-4789-999c-a848cd8e6483": "Work Type ",
      "9ad89a24-27ab-44e1-9fe4-3f20be4316bf": "Site Type",
      "ceedcf1d-1f6e-4cb0-9cec-e6ae8e5c73e1": "Old Site Ref",
      "ce8de42a-3a35-40b1-93f7-5871d6e5fe86": "Work Description",
      "9446ae26-5c77-496d-b87a-719b56573f4c": "Drawing Received Date",
      "4036b63d-ba39-4e00-9aa7-41351f763d97": "Site Note",
      "1b9e9baa-e830-455e-b1a7-9783aea5830b": "Line Item Remark Date",
      "b2c8e073-49cc-4f92-a966-db94f3e0d310": "Line Item Remarks",
      "e1897795-58f6-4250-984c-00cb4cc6f7d2": "Dwell (Next Follow up) Date",
      "f14efb7f-6e52-4e2f-8807-36b6a1eef4d8": "Dwell Status",
      "f03a676a-6fae-420e-bf4f-644c089ee8ec": "Dwell Comments",
      "e1a60e6e-20c3-45af-9063-48be25c1aa5b": "Completion Status",
      "82264689-c5f4-4c76-b617-7ab134972a3d": "Completion Comments",
      "eadb139e-f076-4c4b-a815-42822fbd8c94": "SOW",
      "660496d9-4bd9-453e-9bfb-6c64a699d785": "SOW Date",
      "ecd2074e-57e1-483f-baba-df14d36863c0":
        "Handover Document Received Date :",
      "b8909ff9-edd2-4572-a82c-fe4720becbc0": "Fulfillment By",
      "3584aede-4f10-4d07-9d9b-b64819546ace": "Contractor Total Payout",
      "aa4b2005-c229-4c77-9b61-fe77d0ebe88b": "Contractor Payout Date",
      "1ee680d8-7f92-49e8-94a3-0307661c084a": "QC Status (Dwell):",
      "0ddee6ff-5c6f-4747-b441-5b69d4ab71e7": "QC Comments (Dwell):",
      "31eb4eee-d2df-434c-a2bd-c0e609cef195": "QC Received Date (Dwell):",
      "4c74dcc3-2ec4-4156-85e0-6a2d084292fd": "QC Status (Final) ",
      "bc27d2ff-3664-445d-9516-c9a57c7339ba": "QC Comments (Final):",
      "270cc594-bdc6-4d22-a467-f2e3b619e2fb": "QC Received Date (Final):",
      "9d30592d-deba-490e-887f-2ae78d094486": "PO Number",
      "50c978bb-4e59-4e90-9478-70ad580dab5c": "PO Recieved Date",
      "517d0b81-a559-47b7-a230-53d1ba46f211": "Mode of PO Received",
      "60c6f5ff-c03c-48ec-8f0b-7802802671ac": "SQFT PO Received",
      "ec029c6d-1460-4e69-bfbb-d17020213a4a": "Billable Amt (PO Recieved)",
      "7235dc23-6745-4780-971e-6dd6f9930407": "PO Billing Status",
      "ed82cd58-7ca7-435b-ac62-56e513f01f3b": "PO Comments:",
      "cd989a04-9a9c-41d7-ab14-3763c5861a1d": "FOC Status",
      "e75a6d5d-7ce9-4125-ae04-91899936f2e0": "FOC Comments",
      "e3d7eb05-1a66-41fe-8930-3e1be6367220": "Accounts Comment:",
      "519457b0-60fd-41a5-b1a7-02867d7a3b18": "Invoice Date",
      "c88cf034-8459-4dde-b0a2-5cadbd72a34e": "Invoice Number",
      "9f335621-9a6e-42bb-929f-561bf9a80c0f": "Invoice Amount",
      "********-8a16-419d-b219-f138cce1699a": "Mode of Payment",
      "3f95b6bc-1d46-40f4-95db-bf7803a7d16d": "Payment Received Amt",
      "9509eb46-3a2d-404b-aa4a-2be47fd14fcb": "Payment Received Date",
      "f3b24901-830a-4d9e-b79c-bfa62ff66d30": "Proforma Invoice No",
      "531d9100-5e46-43b2-ac09-e1185ab96ec9": "Proforma Invoice Sent Date",
      "f1548ce1-70a7-41ee-9f37-c4bd6f006195": "Completion Cretificate Rating:",
      "23560ebf-d4b4-4582-9050-1293ff224898":
        "Completion Certificate Feedback Remarks:",
      authority_428: "MKW OPS - Executive",
      authority_429: "MKW Billing Incharge",
      authority_430: "MKW Key Account Manager",
      authority_431: "MKW Ops Manager",
      authority_432: "MKW City Manager",
      authority_433: "MKW Regional Head",
      authority_441: "MKW Project Manager",
      avg_gai_rating: "Avg. GAI rating",
      no_of_gai_rated_tasks: "No. of GAI rated tasks",
      sp_locked_for_change: "SP Locked for change (Yes/No)",
      sp_locked_for_change_by: "SP Locked for change by",
      sp_locking_date: "SP Locking Date",
      mandays: "Mandays",
      sp_work_progress: "SP Work progress percentage",
      sp_total_quantity: "SP Total quantity",
      sp_total_price: "SP Total price",
      total_quantity: "Vendor Bill Amount",
      total_price: "Deductions",
      total_payout: "Paid Amount",
      final_amount: "Final amount",
      sp_sent_for_billing: "SP sent for billing",
      inhouse_deployed_count: "Inhouse deployed count",
      inhouse_deployed_total_hours:
        "Calculated Working Hours Based on Slot Inhouse",
      inhouse_actual_total_hours:
        "Calculated Working Hours Based on Task Start&End time-Inhouse",
      contractor_deployed_total_hours:
        "Calculated Working Hours Based on Slot Contractor",
      "ec12c224-8435-43b8-867f-766cb02f59de": "Drawing File",
      "770ddcf9-8ff2-4aa4-abd1-c20b8759baa5": "Take Over Document",
      "19e1a2b4-761f-40a9-ad95-665d5b154cd1": "Handover documents",
      "f07c83eb-a051-43e9-8412-a502d7f9d078": "SOW File",
      "a96c41b2-e820-47c1-9ecc-4216deb3f528": "PO File",
      "0aeccd19-e387-4461-b367-f0639abfd96b": "Completion Certificate",
      "452f4794-50a8-47fd-a192-93ed0ae72498": "QC File",
      "22f0e0d4-f5f1-4275-aaed-643a92b08475": "Proforma Invoice File",
      "dfb5e81e-c854-4658-a20d-dce8006adb77": "Invoice File",
      "0d9aef1c-f490-461c-af27-99b2afa02b14": "Payment Received Document:",
    },
    selected_columns: [
      "brand_name",
      "service_type",
      "vertical_fr_req_dump",
      "cust_state",
      "location_group",
      "sp_first_task_date",
      "sp_last_task_date",
      "7033ab8c-22c1-4cf0-94e2-2c104b2bec26",
      "b274d89e-fafd-4789-999c-a848cd8e6483",
      "9ad89a24-27ab-44e1-9fe4-3f20be4316bf",
      "ceedcf1d-1f6e-4cb0-9cec-e6ae8e5c73e1",
      "b8909ff9-edd2-4572-a82c-fe4720becbc0",
      "9d30592d-deba-490e-887f-2ae78d094486",
      "7235dc23-6745-4780-971e-6dd6f9930407",
      "cd989a04-9a9c-41d7-ab14-3763c5861a1d",
      "519457b0-60fd-41a5-b1a7-02867d7a3b18",
      "c88cf034-8459-4dde-b0a2-5cadbd72a34e",
      "mandays",
      "sp_total_quantity",
      "total_quantity",
      "final_amount",
      "sp_sent_for_billing",
      "inhouse_deployed_count",
      "inhouse_deployed_total_hours",
      "inhouse_actual_total_hours",
      "contractor_deployed_total_hours",
      "9f335621-9a6e-42bb-929f-561bf9a80c0f",
    ],
    filters: {
      assgn_to_prvdr_date: [endDate.toISOString(), currentDate.toISOString()],
      srvc_status_category: ["CLOSED", "DONE", "ACTIVE"],
      verticals_list: [4],
    },
    pagination: "{}",
    org_id: 2,
    usr_id: userId,
    ip_addr: "::1",
    user_agent:
      "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
    srvc_type_id: "0",
    is_customer_access: "0",
  },
  filters: {
    assgn_to_prvdr_date: [endDate.toISOString(), currentDate.toISOString()],
    srvc_status_category: ["CLOSED", "DONE", "ACTIVE"],
    verticals_list: [4],
  },
};

const dbClient = new Client({
  host: process.env.DB_HOST,
  database: process.env.DB_NAME,
  user: process.env.DB_USER,
  password: process.env.DB_PASSWORD,
  port: 5432,
});

async function getCustomerRequestData() {
  try {
    await dbClient.connect();
    console.log("getCustomerRequestData :: Connected to the database!");

    const res = await dbClient.query(
      `SELECT public.tms_get_srvc_req_dumps_fr_site_attendance_lambda('${JSON.stringify(
        customerReqData.formData
      )}', '${JSON.stringify(customerReqData.filters)}')`
    );

    const resp = res.rows[0].tms_get_srvc_req_dumps_fr_site_attendance_lambda;
    console.log("getCustomerRequestData :: resp :: ", resp);
    return resp;
  } catch (error) {
    console.error("getCustomerRequestData :: Error :", error);
  } finally {
    dbClient.end();
  }
}

async function getSiteAttendanceData() {
  try {
    await dbClient.connect();
    console.log("getSiteAttendanceData :: Connected to the database");

    const res = await client.query(
      `SELECT public.tms_get_attendance_req_dumps_fr_usr_as_array('${JSON.stringify(
        attendanceData.reqObj
      )}', '${JSON.stringify(attendanceData.filters)}')`
    );

    const resp = res.rows[0].tms_get_attendance_req_dumps_fr_usr_as_array;
    console.log("getSiteAttendanceData :: resp :: ", resp);
    return resp;
  } catch (err) {
    console.error("getSiteAttendanceData :: Error:", err);
  } finally {
    dbClient.end();
  }
}

async function updateGoogleSheet({ data, rangeStart, sheetName }) {
  const auth = new google.auth.GoogleAuth({
    keyFile: SERVICE_ACCOUNT_FILE,
    scopes: SCOPES,
    autoRetry: true,
    maxRetries: 5,
    retryDelayMultiplier: 2,
    totalTimeout: 60000,
  });

  const sheets = google.sheets({
    version: "v4",
    auth,
    axios: {
      timeout: 60000,
    },
  });
  const spreadsheetId = process.env.SPREADSHEET_ID;
  const chunkSize = 500;
  const startingRow = parseInt(rangeStart.replace(/[^\d]/g, ""), 10) || 1;
  const delay = (ms) => new Promise((resolve) => setTimeout(resolve, ms));

  const columnOrder = [
    "TMS ID",
    "Status",
    "Brand Name",
    "Vertical",
    "State",
    "Location Group",
    "SP First task date",
    "SP Last task date",
    "Business Unit",
    "Work Type",
    "Site Type",
    "Old Site Ref",
    "Fulfillment By",
    "PO Number",
    "PO Billing Status",
    "FOC Status",
    "Invoice Date",
    "Invoice Number",
    "Invoice Amount",
    "Mandays",
    "SP Total quantity",
    "Vendor Bill Amount",
    "Final amount",
    "SP sent for billing",
    "Inhouse deployed count",
    "Inhouse Cost",
    "Total Cost",
    "GM",
    "Location Code",
    "City Manager",
    "Invoice Month",
    "End Month",
    "Calculated Working Hours Based on Slot Inhouse",
    "Calculated Working Hours Based on Task Start&End time-Inhouse",
    "Calculated Working Hours Based on Slot Contractor",
  ];

  // Step 1: Trim all keys in each row
  data = data.map((row) => {
    const trimmedRow = {};
    for (const key in row) {
      if (Object.hasOwnProperty.call(row, key)) {
        const trimmedKey = key.trim(); // Trim the key
        trimmedRow[trimmedKey] = row[key]; // Assign value to the trimmed key
      }
    }
    return trimmedRow;
  });

  for (let i = 0; i < data.length; i += chunkSize) {
    const chunk = data.slice(i, i + chunkSize);

    // Map data to the desired column order
    const values = chunk.map((row) => {
      //row = Object.entries(row).map(([key, value]) => [key.trim(), value]);
      if (row["Inhouse deployed count"]) {
        row["Inhouse Cost"] = +row["Inhouse deployed count"] * 1200;
      } else {
        row["Inhouse Cost"] = 0;
      }
      if (row["Location Group"]) {
        // Get the first value from "Location Group" if it contains multiple values
        const firstLocation = row["Location Group"].split(",")[0].trim();

        if (selectedLocationGrps.includes(firstLocation)) {
          row["Location Code"] = firstLocation;
        } else {
          row["Location Code"] = "";
        }
      } else {
        row["Location Code"] = "";
      }
      if (
        (row["Inhouse Cost"] || row["Inhouse Cost"] == 0) &&
        (row["Vendor Bill Amount"] || row["Vendor Bill Amount"] == 0)
      ) {
        row["Total Cost"] = +row["Vendor Bill Amount"] + row["Inhouse Cost"];
      } else {
        row["Total Cost"] = 0;
      }
      if (
        (row["Total Cost"] || row["Total Cost"] == 0) &&
        (row["Invoice Amount"] || row["Invoice Amount"] != 0)
      ) {
        row["GM"] = parseFloat(
          (
            ((+row["Invoice Amount"] - +row["Total Cost"]) /
              +row["Invoice Amount"]) *
            100
          ).toFixed(2)
        );
      } else {
        row["GM"] = 0;
      }

      return columnOrder.map((key) => {
        // console.log("key", key);
        // const matchingKey = rowKeys.find((rowKey) => rowKey == key);
        // console.log("matchingKey", matchingKey);
        // Return the value if the key exists, otherwise return an empty string
        if (
          key == "City Manager" ||
          key == "End Month" ||
          key == "Invoice Month"
        ) {
          return;
        }
        return key === undefined || row[key] === null ? "" : row[key];
      });
    });

    const resource = { values };

    // Calculate the range for the current chunk
    const currentRangeStartRow = startingRow + i; // Adjust based on the chunk index
    const currentRange = `${sheetName}!A${currentRangeStartRow}`;

    try {
      console.log(
        `updateGoogleSheet :: Uploading chunk ${i + 1} to ${
          i + chunk.length
        }, starting at row ${currentRangeStartRow}`
      );
      await sheets.spreadsheets.values.update({
        spreadsheetId,
        range: currentRange,
        valueInputOption: "USER_ENTERED",
        resource,
      });
      console.log(
        `updateGoogleSheet :: Delay for 10 seconds after chunk ${i + 1}`
      );
      await delay(10000);
    } catch (error) {
      console.error(
        "updateGoogleSheet :: Error updating Google Sheet:",
        error.message
      );
      throw error;
    }
  }

  console.log("updateGoogleSheet :: Google Sheet updated successfully.");
}

exports.handler = async (event) => {
  const credentials = require("./creds.json");

  fs.writeFileSync(SERVICE_ACCOUNT_FILE, JSON.stringify(credentials));
  let sheetName = "cust_req";
  const customerRequestData = await getCustomerRequestData();
  await updateGoogleSheet({
    data: customerRequestData,
    rangeStart: `${sheetName}!A2`,
    sheetName,
  });

  // const siteAttendanceData = await getSiteAttendanceData();
  // sheetName = "attendance_dump";
  // await updateGoogleSheet({data: siteAttendanceData, rangeStart: `${sheetName}!A2`, sheetName});

  // await updateGoogleSheet(autoAssignRawData, "AA-Insights!A1", "A7");

  return {
    statusCode: 200,
    body: JSON.stringify("Data updated successfully"),
  };
};
