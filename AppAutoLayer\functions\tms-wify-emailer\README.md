# TMS Wify Emailer

A Lambda function that queries database data and sends it via email using SMTP credentials from environment variables.

## Features

- Queries ACE Implementation Progress data from PostgreSQL database
- Generates professional HTML email templates with data tables
- Sends emails using configurable SMTP settings
- Modular design with separate email service and template modules
- Comprehensive error handling and logging

## Environment Variables

### Database Configuration
```
DB_HOST=your-database-host
DB_NAME=your-database-name
DB_USER=your-database-user
DB_PASSWORD=your-database-password
DB_PORT=5432
```

### SMTP Configuration
```
EMAIL_SMTP_HOST=smtp.gmail.com
EMAIL_SMTP_PORT=465
EMAIL_AUTH_USER=<EMAIL>
EMAIL_AUTH_PASS=your-email-password
EMAIL_FROM=<EMAIL>
```

### Email Recipients
```
EMAIL_RECIPIENTS=<EMAIL>,<EMAIL>
EMAIL_CC=<EMAIL>,<EMAIL> (optional)
EMAIL_BCC=<EMAIL>,<EMAIL> (optional)
```

## File Structure

```
tms-wify-emailer/
├── index.js              # Main Lambda handler
├── emailService.js       # Email sending functionality
├── htmlTemplate.js       # HTML template generation
├── package.json          # Dependencies
└── README.md             # This file
```

## Usage

### Local Testing
```bash
# Install dependencies
npm install

# Set environment variables in .env file
# Run the function (if you have a local test setup)
```

### Lambda Deployment
Deploy this function to AWS Lambda and configure the environment variables in the Lambda console.

## Modules

### emailService.js
- `sendEmail(emailData)` - Sends email with provided configuration
- `getEmailCredentials()` - Retrieves SMTP credentials from environment

### htmlTemplate.js
- `generateDataTableHTML(data, options)` - Creates HTML table from query results
- `generateEmptyDataHTML(title, description)` - Creates HTML for empty data scenarios
- `escapeHtml(text)` - Escapes HTML special characters

## Email Template Features

- Professional responsive design
- Data presented in clean HTML tables
- Record count and generation timestamp
- Hover effects and alternating row colors
- Handles empty data scenarios gracefully
- XSS protection through HTML escaping

## Error Handling

The function includes comprehensive error handling:
- Database connection errors
- Email sending failures
- Invalid configuration
- Empty data scenarios

All errors are logged with descriptive messages following the established logging pattern: `ClassName::functionName:: message`.

## Security

- HTML content is escaped to prevent XSS attacks
- SMTP credentials are stored in environment variables
- No sensitive data is logged in console outputs
