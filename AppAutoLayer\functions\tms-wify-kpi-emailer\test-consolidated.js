/**
 * Test script for consolidated email functionality
 * This script tests the consolidated email processor without actually sending emails
 */

const {
  generateConsolidatedSummary,
  generateManagerCategoryTable,
} = require("./consolidatedEmailProcessor");
const { generateDataTableHTML } = require("./htmlTemplate");

// Mock the database connection for testing
process.env.DB_HOST = "localhost";
process.env.DB_NAME = "test_db";
process.env.DB_USER = "test_user";
process.env.DB_PASSWORD = "test_password";
process.env.DB_PORT = "5432";

// Mock vertical KPI data for testing
const mockVerticalKPIData = [
  {
    "Vertical ID": 4,
    Title: "MKW Installation",
    "All time orders": 1250,
    "Open orders": 45,
    "New orders": 8,
    "Orders closed": 5,
    "Tech Inch.": 3,
    "Orders Sch.": 12,
    "Orders Sch. for yest. ": 10,
    "Tasks created": 15,
    "Supp. depl": 18,
    "Supply all task closed": 12,
    "Supply no task update": 2,
    "Supply partial task update": 4,
    "Active supply": 25,
    "New supply": 2,
    "Inactive supply": 5,
  },
  {
    "Vertical ID": 5,
    Title: "MKW Services",
    "All time orders": 890,
    "Open orders": 32,
    "New orders": 5,
    "Orders closed": 3,
    "Tech Inch.": 2,
    "Orders Sch.": 8,
    "Orders Sch. for yest. ": 7,
    "Tasks created": 10,
    "Supp. depl": 15,
    "Supply all task closed": 10,
    "Supply no task update": 1,
    "Supply partial task update": 4,
    "Active supply": 20,
    "New supply": 1,
    "Inactive supply": 3,
  },
  {
    "Vertical ID": 9,
    Title: "Water Services",
    "All time orders": 567,
    "Open orders": 28,
    "New orders": 6,
    "Orders closed": 4,
    "Tech Inch.": 2,
    "Orders Sch.": 10,
    "Orders Sch. for yest. ": 8,
    "Tasks created": 12,
    "Supp. depl": 12,
    "Supply all task closed": 8,
    "Supply no task update": 1,
    "Supply partial task update": 3,
    "Active supply": 15,
    "New supply": 0,
    "Inactive supply": 2,
  },
];

async function testConsolidatedEmail() {
  console.log("=== Testing Consolidated Email Functionality ===\n");

  try {
    // Test 1: Generate consolidated summary
    console.log("1. Testing generateConsolidatedSummary...");
    const summary = generateConsolidatedSummary(mockVerticalKPIData);
    console.log("✅ Summary generated successfully");
    console.log(
      "Summary contains HTML:",
      summary.includes('<div class="summary-section">')
    );
    console.log("Summary length:", summary.length, "characters");
    console.log("\n" + "=".repeat(80) + "\n");

    // Test 2: Generate manager vs category table
    console.log("2. Testing generateManagerCategoryTable...");
    const managerCategoryTable = await generateManagerCategoryTable(
      mockVerticalKPIData
    );
    console.log("✅ Manager category table generated successfully");
    console.log(
      "Table contains HTML:",
      managerCategoryTable.includes("manager-category-section")
    );
    console.log("Table length:", managerCategoryTable.length, "characters");
    console.log("\n" + "=".repeat(80) + "\n");

    // Test 3: Generate HTML content with custom summary and manager table
    console.log(
      "3. Testing HTML generation with custom summary and manager table..."
    );
    const htmlContent = generateDataTableHTML(mockVerticalKPIData, {
      title: "Company-Wide Daily KPI Report - All Verticals",
      description:
        "Comprehensive overview of all verticals performance metrics for testing.",
      summary: summary,
      managerCategoryTable: managerCategoryTable,
    });

    console.log("✅ HTML content generated successfully");
    console.log("HTML length:", htmlContent.length, "characters");
    console.log(
      "Contains summary section:",
      htmlContent.includes("Company-Wide Daily KPI Summary")
    );
    console.log(
      "Contains manager table:",
      htmlContent.includes("Manager vs Category Performance Overview")
    );
    console.log("\n" + "=".repeat(80) + "\n");

    // Test 4: Test with empty data
    console.log("4. Testing with empty data...");
    const emptySummary = generateConsolidatedSummary([]);
    console.log("✅ Empty data handled correctly");
    console.log("Empty summary:", emptySummary);
    console.log("\n" + "=".repeat(80) + "\n");

    // Test 5: Test EMAIL_CC environment variable check
    console.log("5. Testing EMAIL_CC environment variable...");
    const originalEmailCC = process.env.EMAIL_CC;

    // Test without EMAIL_CC
    delete process.env.EMAIL_CC;
    const { sendConsolidatedEmail } = require("./consolidatedEmailProcessor");
    const resultWithoutCC = await sendConsolidatedEmail(mockVerticalKPIData);
    console.log(
      "✅ Without EMAIL_CC:",
      resultWithoutCC.status,
      "-",
      resultWithoutCC.message
    );

    // Test with EMAIL_CC (but won't actually send)
    process.env.EMAIL_CC = "<EMAIL>";
    console.log("✅ EMAIL_CC configured for testing");

    // Restore original value
    if (originalEmailCC) {
      process.env.EMAIL_CC = originalEmailCC;
    }

    console.log("\n" + "=".repeat(80) + "\n");
    console.log("🎉 All tests completed successfully!");
  } catch (error) {
    console.error("❌ Test failed:", error);
    process.exit(1);
  }
}

// Run tests if this file is executed directly
if (require.main === module) {
  testConsolidatedEmail();
}

module.exports = {
  testConsolidatedEmail,
  mockVerticalKPIData,
};
