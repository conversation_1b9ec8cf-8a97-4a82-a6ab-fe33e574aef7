const { Client } = require("pg");
require("dotenv").config();

/**
 * Get vertical KPI data from database
 * @returns {Promise<Array>} Array of vertical KPI data
 */
async function getVerticalKPIData() {
  const client = new Client({
    host: process.env.DB_HOST,
    database: process.env.DB_NAME,
    user: process.env.DB_USER,
    password: process.env.DB_PASSWORD,
    port: process.env.DB_PORT, // default PostgreSQL port
  });

  await client.connect();

  // Calculate dynamic dates
  const today = new Date();
  const yesterday = new Date(today);
  yesterday.setDate(today.getDate() - 1);

  const todayStr = today.toISOString().split("T")[0]; // YYYY-MM-DD format
  const yesterdayStr = yesterday.toISOString().split("T")[0]; // YYYY-MM-DD format

  console.log(
    "DataService::getVerticalKPIData:: Using dates - Yesterday:",
    yesterdayStr,
    "Today:",
    todayStr
  );

  const res = await client.query(
    `
      select vertical.db_id as "Vertical ID",
             vertical.settings_data->'vertical_title' as "Title",
             (
                SELECT COUNT(DISTINCT srvc_req.db_id)
                FROM cl_tx_srvc_req srvc_req
                WHERE srvc_req.srvc_prvdr = 2
                AND srvc_req.prvdr_vertical = vertical.db_id
                AND srvc_req.is_deleted IS NOT TRUE
             ) AS "All time orders",
             (
                SELECT COUNT(DISTINCT srvc_req.db_id)
                FROM cl_tx_srvc_req srvc_req
                WHERE srvc_req.srvc_prvdr = 2
                AND srvc_req.prvdr_vertical = vertical.db_id
                AND srvc_req.is_deleted IS NOT true
                and srvc_req.status = 'open'
             ) AS "Open orders",
             (
                SELECT COUNT(DISTINCT srvc_req.db_id)
                FROM cl_tx_srvc_req srvc_req
                WHERE srvc_req.srvc_prvdr = 2
                AND srvc_req.prvdr_vertical = vertical.db_id
                AND srvc_req.is_deleted IS NOT true
                and (srvc_req.c_meta).time::timestamp > $1
                and (srvc_req.c_meta).time::timestamp < $2
             ) AS "New orders",
             (
                SELECT COUNT(DISTINCT srvc_req.db_id)
                FROM cl_tx_srvc_req srvc_req
                inner join cl_tx_sbtsk as sbtsk
                   on sbtsk.org_id = 2
                  and (sbtsk.c_meta).time::timestamp > $1
                  and (sbtsk.c_meta).time::timestamp < $2
                  and sbtsk.srvc_req_id = srvc_req.db_id
                WHERE srvc_req.srvc_prvdr = 2
                AND srvc_req.prvdr_vertical = vertical.db_id
                AND srvc_req.is_deleted IS NOT true
             ) as "Orders assigned",
             (
                SELECT COUNT(DISTINCT srvc_req.db_id)
                FROM cl_tx_srvc_req srvc_req
                inner join cl_tx_srvc_req_trnstn_log as close_log
                   on close_log.srvc_req_id = srvc_req.db_id
                  and close_log.status_key = 'closed'
                  and close_log.trnstn_date > $1
                  and close_log.trnstn_date < $2
                WHERE srvc_req.srvc_prvdr = 2
                AND srvc_req.prvdr_vertical = vertical.db_id
                AND srvc_req.is_deleted IS NOT true
             ) as "Orders closed",
             (
                SELECT COUNT(DISTINCT tech.usr_id)
                FROM cl_tx_users tech
                WHERE tech.primary_vertical = vertical.db_id
                and tech.is_active = true
             ) AS "Active supply",
             (
                SELECT COUNT(DISTINCT tech.usr_id)
                FROM cl_tx_users tech
                WHERE tech.primary_vertical = vertical.db_id
                and (tech.c_meta).time::timestamp > $1
                and (tech.c_meta).time::timestamp < $2
             ) AS "New supply",
             (
                SELECT COUNT(DISTINCT tech.usr_id)
                FROM cl_tx_users tech
                WHERE tech.primary_vertical = vertical.db_id
                and tech.is_active = false
             ) AS "Deactivated supply",
             (
                select count(distinct sbtsk.assigned_to)
                from cl_tx_sbtsk as sbtsk
                inner join cl_tx_srvc_req srvc_req
                   on srvc_req.db_id = sbtsk.srvc_req_id
                  AND srvc_req.prvdr_vertical = vertical.db_id
                  AND srvc_req.is_deleted IS NOT true
                where sbtsk.org_id = 2
                and sbtsk.start_time > $1
                and sbtsk.start_time < $2
             ) as "Supply deployed",
             (
                select count(distinct sbtsk.assigned_to)
                from cl_tx_sbtsk as sbtsk
                inner join cl_tx_srvc_req srvc_req
                   on srvc_req.db_id = sbtsk.srvc_req_id
                  AND srvc_req.prvdr_vertical = vertical.db_id
                  AND srvc_req.is_deleted IS NOT true
                where sbtsk.org_id = 2
                and sbtsk.start_time > $1
                and sbtsk.start_time < $2
                and sbtsk.status <> 'open'
             ) as "Supply started > 0 tasks",
             (
                select count(distinct sbtsk.assigned_to)
                from cl_tx_sbtsk as sbtsk
                inner join cl_tx_srvc_req srvc_req
                   on srvc_req.db_id = sbtsk.srvc_req_id
                  AND srvc_req.prvdr_vertical = vertical.db_id
                  AND srvc_req.is_deleted IS NOT true
                where sbtsk.org_id = 2
                and sbtsk.start_time > $1
                and sbtsk.start_time < $2
                and sbtsk.status <> 'closed'
             ) as "Supply closed > 0 tasks"
        from cl_tx_orgs_settings as vertical
       where vertical.settings_type = 'SP_CUSTOM_FIELDS'
         and vertical.org_id = 2
         and vertical.db_id <> 2
       group by vertical.db_id
    `,
    [yesterdayStr, todayStr]
  );

  await client.end();
  const resp = res.rows;
  console.log(
    "DataService::getVerticalKPIData:: Retrieved rows:",
    resp.length
  );
  return resp;
}

/**
 * Get orders for a specific vertical
 * @param {number} verticalId - The vertical ID
 * @param {string} verticalTitle - The vertical title for logging
 * @returns {Promise<Array>} Array of order data
 */
async function getOrdersForVertical(verticalId, verticalTitle) {
  const client = new Client({
    host: process.env.DB_HOST,
    database: process.env.DB_NAME,
    user: process.env.DB_USER,
    password: process.env.DB_PASSWORD,
    port: process.env.DB_PORT,
  });

  await client.connect();

  console.log(
    `DataService::getOrdersForVertical:: Fetching orders for vertical ${verticalId} (${verticalTitle})`
  );

  const res = await client.query(
    `
      SELECT srvc_req.display_code as "TMS ID",
             TO_CHAR(DATE((srvc_req.c_meta).time), 'YYYY-MM-DD') as "Creation date",
             srvc_req.form_data->>'cust_full_name' as "Customer name",
             srvc_req.form_data->>'request_description' as "Description",
             srvc_req.cust_pincode as "Pincode",
             srvc_req.form_data->>'cust_city' as "City",
             srvc_req.form_data->>'cust_state' as "State"
        FROM cl_tx_srvc_req srvc_req
       WHERE srvc_req.srvc_prvdr = 2
         AND srvc_req.prvdr_vertical = $1
         AND srvc_req.is_deleted IS NOT true
         and srvc_req.status = 'open'
       order by srvc_req.db_id desc
    `,
    [verticalId]
  );

  await client.end();

  console.log(
    `DataService::getOrdersForVertical:: Retrieved ${res.rows.length} orders for vertical ${verticalId}`
  );

  return res.rows;
}

module.exports = {
  getVerticalKPIData,
  getOrdersForVertical,
};
