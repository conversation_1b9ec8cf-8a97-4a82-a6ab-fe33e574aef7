const { Client } = require("pg");
require("dotenv").config();

/**
 * Get vertical KPI data from database
 * @returns {Promise<Array>} Array of vertical KPI data
 */
async function getVerticalKPIData() {
  const client = new Client({
    host: process.env.DB_HOST,
    database: process.env.DB_NAME,
    user: process.env.DB_USER,
    password: process.env.DB_PASSWORD,
    port: process.env.DB_PORT, // default PostgreSQL port
  });

  await client.connect();

  // Calculate dynamic dates
  const today = new Date();
  const yesterday = new Date(today);
  yesterday.setDate(today.getDate() - 1);

  const todayStr = today.toISOString().split("T")[0]; // YYYY-MM-DD format
  const yesterdayStr = yesterday.toISOString().split("T")[0]; // YYYY-MM-DD format

  console.log(
    "DataService::getVerticalKPIData:: Using dates - Yesterday:",
    yesterdayStr,
    "Today:",
    todayStr
  );

  const res = await client.query(
    `
      select vertical.db_id as "Vertical ID",
             vertical.settings_data->'vertical_title' as "Title",
             (
                SELECT COUNT(DISTINCT srvc_req.db_id)
                  FROM cl_tx_srvc_req srvc_req
                 WHERE srvc_req.srvc_prvdr = 2
                   AND srvc_req.prvdr_vertical = vertical.db_id
                   AND srvc_req.is_deleted IS NOT TRUE
             ) AS "All time orders",
             (
                SELECT COUNT(DISTINCT srvc_req.db_id)
                  FROM cl_tx_srvc_req srvc_req
                 WHERE srvc_req.srvc_prvdr = 2
                   AND srvc_req.prvdr_vertical = vertical.db_id
                   AND srvc_req.is_deleted IS NOT true
                   and srvc_req.status = 'open'
             ) AS "Open orders",
             (
                SELECT COUNT(DISTINCT srvc_req.db_id)
                  FROM cl_tx_srvc_req srvc_req
                 WHERE srvc_req.srvc_prvdr = 2
                   AND srvc_req.prvdr_vertical = vertical.db_id
                   AND srvc_req.is_deleted IS NOT true
                   and (srvc_req.c_meta).time::timestamp > $1
                   and (srvc_req.c_meta).time::timestamp < $2
             ) AS "New orders",
             (
                SELECT COUNT(DISTINCT srvc_req.db_id)
                  FROM cl_tx_srvc_req srvc_req
                 inner join cl_tx_srvc_req_trnstn_log as close_log
                    on close_log.srvc_req_id = srvc_req.db_id
                   and close_log.status_key = 'closed'
                   and close_log.trnstn_date > $1
                   and close_log.trnstn_date < $2
                 WHERE srvc_req.srvc_prvdr = 2
                   AND srvc_req.prvdr_vertical = vertical.db_id
                   AND srvc_req.is_deleted IS NOT true
             ) as "Orders closed",
             (
                SELECT COUNT(DISTINCT sbtsk.c_by )
                  FROM cl_tx_srvc_req srvc_req
                 inner join cl_tx_sbtsk as sbtsk
                    on sbtsk.org_id = 2
                   and (sbtsk.c_meta).time::timestamp > $1
                   and (sbtsk.c_meta).time::timestamp < $2
                   and sbtsk.srvc_req_id = srvc_req.db_id
                 WHERE srvc_req.srvc_prvdr = 2
                   AND srvc_req.prvdr_vertical = vertical.db_id
                   AND srvc_req.is_deleted IS NOT true
             ) as "Tech Inch.",
             (
                SELECT COUNT(DISTINCT srvc_req.db_id)
                  FROM cl_tx_srvc_req srvc_req
                 inner join cl_tx_sbtsk as sbtsk
                    on sbtsk.org_id = 2
                   and (sbtsk.c_meta).time::timestamp > $1
                   and (sbtsk.c_meta).time::timestamp < $2
                   and sbtsk.srvc_req_id = srvc_req.db_id
                 WHERE srvc_req.srvc_prvdr = 2
                   AND srvc_req.prvdr_vertical = vertical.db_id
                   AND srvc_req.is_deleted IS NOT true
             ) as "Orders Sch.",
             (
                SELECT COUNT(DISTINCT srvc_req.db_id)
                  FROM cl_tx_srvc_req srvc_req
                 inner join cl_tx_sbtsk as sbtsk
                    on sbtsk.org_id = 2
                   and sbtsk.start_time > $1
                   and sbtsk.start_time < $2
                   and sbtsk.srvc_req_id = srvc_req.db_id
                 WHERE srvc_req.srvc_prvdr = 2
                   AND srvc_req.prvdr_vertical = vertical.db_id
                   AND srvc_req.is_deleted IS NOT true
             ) as "Orders Sch. for yest. ",
             (
                SELECT count(distinct sbtsk.db_id )
                  FROM cl_tx_srvc_req srvc_req
                 inner join cl_tx_sbtsk as sbtsk
                    on sbtsk.org_id = 2
                   and (sbtsk.c_meta).time::timestamp > $1
                   and (sbtsk.c_meta).time::timestamp < $2
                   and sbtsk.srvc_req_id = srvc_req.db_id
                 WHERE srvc_req.srvc_prvdr = 2
                   AND srvc_req.prvdr_vertical = vertical.db_id
                   AND srvc_req.is_deleted IS NOT true
             ) as "Tasks created",
             (
                select count(distinct sbtsk.assigned_to)
                  from cl_tx_sbtsk as sbtsk
                 inner join cl_tx_srvc_req srvc_req
                    on srvc_req.db_id = sbtsk.srvc_req_id
                   AND srvc_req.prvdr_vertical = vertical.db_id
                   AND srvc_req.is_deleted IS NOT true
                 where sbtsk.org_id = 2
                   and sbtsk.start_time > $1
                   and sbtsk.start_time < $2
             ) as "Supp. depl",
             (
                select count(*)
                  from (
                        SELECT sbtsk.assigned_to
                          FROM cl_tx_sbtsk AS sbtsk
                         INNER JOIN cl_tx_srvc_req srvc_req
                            ON srvc_req.db_id = sbtsk.srvc_req_id
                           AND srvc_req.prvdr_vertical = vertical.db_id
                           AND srvc_req.is_deleted IS NOT TRUE
                         WHERE sbtsk.org_id = 2
                           AND sbtsk.start_time > $1
                           AND sbtsk.start_time < $2
                         GROUP BY sbtsk.assigned_to
                        having COUNT(sbtsk.db_id) = COUNT(sbtsk.db_id) FILTER (WHERE sbtsk.status = 'closed')
                       ) as assignee
             ) as "Supply all task closed",
             (
                select count(*)
                  from (
                        SELECT sbtsk.assigned_to
                          FROM cl_tx_sbtsk AS sbtsk
                         INNER JOIN cl_tx_srvc_req srvc_req
                            ON srvc_req.db_id = sbtsk.srvc_req_id
                           AND srvc_req.prvdr_vertical = vertical.db_id
                           AND srvc_req.is_deleted IS NOT TRUE
                         WHERE sbtsk.org_id = 2
                           AND sbtsk.start_time > $1
                           AND sbtsk.start_time < $2
                         GROUP BY sbtsk.assigned_to
                        having COUNT(sbtsk.db_id) = COUNT(sbtsk.db_id) FILTER (WHERE sbtsk.status = 'open')
                       ) as assignee
             ) as "Supply no task update",
             (
                select count(*)
                  from (
                        SELECT sbtsk.assigned_to
                          FROM cl_tx_sbtsk AS sbtsk
                         INNER JOIN cl_tx_srvc_req srvc_req
                            ON srvc_req.db_id = sbtsk.srvc_req_id
                           AND srvc_req.prvdr_vertical = vertical.db_id
                           AND srvc_req.is_deleted IS NOT TRUE
                         WHERE sbtsk.org_id = 2
                           AND sbtsk.start_time > $1
                           AND sbtsk.start_time < $2
                         GROUP BY sbtsk.assigned_to
                        having COUNT(sbtsk.db_id) > COUNT(sbtsk.db_id) FILTER (WHERE sbtsk.status = 'open')
                           and COUNT(sbtsk.db_id) > COUNT(sbtsk.db_id) FILTER (WHERE sbtsk.status = 'closed')
                       ) as assignee
             ) as "Supply partial task update",
             (
                SELECT COUNT(DISTINCT tech.usr_id)
                  FROM cl_tx_users tech
                 WHERE tech.primary_vertical = vertical.db_id
                   and tech.is_active = true
             ) AS "Active supply",
             (
                  SELECT COUNT(DISTINCT tech.usr_id)
                  FROM cl_tx_users tech
                  WHERE tech.primary_vertical = vertical.db_id
                  and (tech.c_meta).time::timestamp > $1
                  and (tech.c_meta).time::timestamp < $2
             ) AS "New supply",
             (
                SELECT COUNT(DISTINCT tech.usr_id)
                  FROM cl_tx_users tech
                 WHERE tech.primary_vertical = vertical.db_id
                   and tech.is_active = false
             ) AS "Inactive supply"
        from cl_tx_orgs_settings as vertical
       where vertical.settings_type = 'SP_CUSTOM_FIELDS'
         and vertical.org_id = 2
         and vertical.db_id <> 2
         and vertical.db_id <> 1084
         and vertical.db_id <> 827
       group by vertical.db_id
    `,
    [yesterdayStr, todayStr]
  );

  await client.end();
  const resp = res.rows;
  console.log("DataService::getVerticalKPIData:: Retrieved rows:", resp.length);
  return resp;
}

/**
 * Get orders for a specific vertical
 * @param {number} verticalId - The vertical ID
 * @param {string} verticalTitle - The vertical title for logging
 * @returns {Promise<Array>} Array of order data
 */
async function getOrdersForVertical(verticalId, verticalTitle) {
  const client = new Client({
    host: process.env.DB_HOST,
    database: process.env.DB_NAME,
    user: process.env.DB_USER,
    password: process.env.DB_PASSWORD,
    port: process.env.DB_PORT,
  });

  await client.connect();

  console.log(
    `DataService::getOrdersForVertical:: Fetching orders for vertical ${verticalId} (${verticalTitle})`
  );

  const res = await client.query(
    `
      SELECT srvc_req.display_code as "TMS ID",
             TO_CHAR(DATE((srvc_req.c_meta).time), 'YYYY-MM-DD') as "Creation date",
             srvc_req.form_data->>'cust_full_name' as "Customer name",
             srvc_req.form_data->>'request_description' as "Description",
             srvc_req.cust_pincode as "Pincode",
             srvc_req.form_data->>'cust_city' as "City",
             srvc_req.form_data->>'cust_state' as "State"
        FROM cl_tx_srvc_req srvc_req
       WHERE srvc_req.srvc_prvdr = 2
         AND srvc_req.prvdr_vertical = $1
         AND srvc_req.is_deleted IS NOT true
         and srvc_req.status = 'open'
       order by srvc_req.db_id desc
    `,
    [verticalId]
  );

  await client.end();

  console.log(
    `DataService::getOrdersForVertical:: Retrieved ${res.rows.length} orders for vertical ${verticalId}`
  );

  return res.rows;
}

/**
 * Get tasks for a specific vertical
 * @param {number} verticalId - The vertical ID
 * @param {string} verticalTitle - The vertical title for logging
 * @returns {Promise<Array>} Array of task data
 */
async function getTasksForVertical(verticalId, verticalTitle) {
  const client = new Client({
    host: process.env.DB_HOST,
    database: process.env.DB_NAME,
    user: process.env.DB_USER,
    password: process.env.DB_PASSWORD,
    port: process.env.DB_PORT,
  });

  await client.connect();

  // Calculate dynamic dates
  const today = new Date();
  const yesterday = new Date(today);
  yesterday.setDate(today.getDate() - 1);

  const todayStr = today.toISOString().split("T")[0]; // YYYY-MM-DD format
  const yesterdayStr = yesterday.toISOString().split("T")[0]; // YYYY-MM-DD format

  console.log(
    `DataService::getTasksForVertical:: Fetching tasks for vertical ${verticalId} (${verticalTitle}) from ${yesterdayStr} to ${todayStr}`
  );

  const res = await client.query(
    `
      SELECT technician."name" as "Assignee Name",
             technician.user_code as "Emp. Code",
             TO_CHAR(DATE((sbtsk.c_meta).time), 'YYYY-MM-DD') as "Task cr. Date",
             TO_CHAR((sbtsk.c_meta).time::timestamp at time zone 'utc' at time zone 'Asia/kolkata','HH12:MIPM') as "Task cr. Time",
             TO_CHAR(DATE(sbtsk.start_time), 'YYYY-MM-DD') as "Task date",
             (
               TO_CHAR((sbtsk.start_time)::timestamp at time zone 'utc' at time zone 'Asia/kolkata','HH12:MIPM')
               || '-' ||
               TO_CHAR((sbtsk.end_time)::timestamp at time zone 'utc' at time zone 'Asia/kolkata','HH12:MIPM')
             ) as "Task time",
             sbtsk_status.title as "Curr. Task status",
             TO_CHAR((sbtsk.u_meta).time at time zone 'utc' at time zone 'Asia/kolkata', 'YYYY-MM-DD HH12:MIPM') as "Updated on",
             c_by."name" as "Created by",
             srvc_req.display_code as "TMS ID",
             srvc_req.cust_pincode as "Pincode",
             srvc_req.form_data->>'cust_city' as "City",
             srvc_req.form_data->>'cust_state' as "State"
        FROM cl_tx_sbtsk AS sbtsk
       INNER JOIN cl_tx_srvc_req srvc_req
          ON srvc_req.db_id = sbtsk.srvc_req_id
         AND srvc_req.prvdr_vertical = $3
         AND srvc_req.is_deleted IS NOT true
       INNER JOIN cl_tx_users technician
          ON technician.usr_id = any(sbtsk.assigned_to)
       INNER JOIN cl_cf_sbtsk_statuses as sbtsk_status
          ON sbtsk_status.sbtsk_type_id = sbtsk.sbtsk_type
         AND sbtsk_status.status_key = sbtsk.status
       INNER JOIN cl_tx_users c_by
          ON c_by.usr_id = sbtsk.c_by
       WHERE sbtsk.org_id = 2
         AND sbtsk.start_time > $1
         AND sbtsk.start_time < $2
       ORDER BY sbtsk.start_time DESC
    `,
    [yesterdayStr, todayStr, verticalId]
  );

  await client.end();

  console.log(
    `DataService::getTasksForVertical:: Retrieved ${res.rows.length} tasks for vertical ${verticalId}`
  );

  return res.rows;
}

/**
 * Get manager totals using SQL aggregation for specific vertical IDs
 * @param {Array<number>} verticalIds - Array of vertical IDs for the manager
 * @returns {Promise<Object>} Object containing aggregated totals for the manager
 */
async function getManagerTotals(verticalIds) {
  const client = new Client({
    host: process.env.DB_HOST,
    database: process.env.DB_NAME,
    user: process.env.DB_USER,
    password: process.env.DB_PASSWORD,
    port: process.env.DB_PORT,
  });

  await client.connect();

  // Calculate dynamic dates
  const today = new Date();
  const yesterday = new Date(today);
  yesterday.setDate(today.getDate() - 1);

  const todayStr = today.toISOString().split("T")[0]; // YYYY-MM-DD format
  const yesterdayStr = yesterday.toISOString().split("T")[0]; // YYYY-MM-DD format

  console.log(
    "DataService::getManagerTotals:: Using dates - Yesterday:",
    yesterdayStr,
    "Today:",
    todayStr,
    "Vertical IDs:",
    verticalIds
  );

  const res = await client.query(
    `
      select (
                SELECT COUNT(DISTINCT srvc_req.db_id)
                  FROM cl_tx_srvc_req srvc_req
                 WHERE srvc_req.srvc_prvdr = 2
                   AND srvc_req.prvdr_vertical = any(array_agg(vertical.db_id))
                   AND srvc_req.is_deleted IS NOT TRUE
             ) AS "All time orders",
             (
                SELECT COUNT(DISTINCT srvc_req.db_id)
                  FROM cl_tx_srvc_req srvc_req
                 WHERE srvc_req.srvc_prvdr = 2
                   AND srvc_req.prvdr_vertical = any(array_agg(vertical.db_id))
                   AND srvc_req.is_deleted IS NOT true
                   and srvc_req.status = 'open'
             ) AS "Open orders",
             (
                SELECT COUNT(DISTINCT srvc_req.db_id)
                  FROM cl_tx_srvc_req srvc_req
                 WHERE srvc_req.srvc_prvdr = 2
                   AND srvc_req.prvdr_vertical = any(array_agg(vertical.db_id))
                   AND srvc_req.is_deleted IS NOT true
                   and (srvc_req.c_meta).time::timestamp > $1
                   and (srvc_req.c_meta).time::timestamp < $2
             ) AS "New orders",
             (
                SELECT COUNT(DISTINCT srvc_req.db_id)
                  FROM cl_tx_srvc_req srvc_req
                 inner join cl_tx_srvc_req_trnstn_log as close_log
                    on close_log.srvc_req_id = srvc_req.db_id
                   and close_log.status_key = 'closed'
                   and close_log.trnstn_date > $1
                   and close_log.trnstn_date < $2
                 WHERE srvc_req.srvc_prvdr = 2
                   AND srvc_req.prvdr_vertical = any(array_agg(vertical.db_id))
                   AND srvc_req.is_deleted IS NOT true
             ) as "Orders closed",
             (
                SELECT COUNT(DISTINCT sbtsk.c_by )
                  FROM cl_tx_srvc_req srvc_req
                 inner join cl_tx_sbtsk as sbtsk
                    on sbtsk.org_id = 2
                   and (sbtsk.c_meta).time::timestamp > $1
                   and (sbtsk.c_meta).time::timestamp < $2
                   and sbtsk.srvc_req_id = srvc_req.db_id
                 WHERE srvc_req.srvc_prvdr = 2
                   AND srvc_req.prvdr_vertical = any(array_agg(vertical.db_id))
                   AND srvc_req.is_deleted IS NOT true
             ) as "Active TIs",
             (
                SELECT COUNT(DISTINCT srvc_req.db_id)
                  FROM cl_tx_srvc_req srvc_req
                 inner join cl_tx_sbtsk as sbtsk
                    on sbtsk.org_id = 2
                   and (sbtsk.c_meta).time::timestamp > $1
                   and (sbtsk.c_meta).time::timestamp < $2
                   and sbtsk.srvc_req_id = srvc_req.db_id
                 WHERE srvc_req.srvc_prvdr = 2
                   AND srvc_req.prvdr_vertical = any(array_agg(vertical.db_id))
                   AND srvc_req.is_deleted IS NOT true
             ) as "Ord(s) Sch.",
             (
                SELECT COUNT(DISTINCT srvc_req.db_id)
                  FROM cl_tx_srvc_req srvc_req
                 inner join cl_tx_sbtsk as sbtsk
                    on sbtsk.org_id = 2
                   and sbtsk.start_time > $1
                   and sbtsk.start_time < $2
                   and sbtsk.srvc_req_id = srvc_req.db_id
                 WHERE srvc_req.srvc_prvdr = 2
                   AND srvc_req.prvdr_vertical = any(array_agg(vertical.db_id))
                   AND srvc_req.is_deleted IS NOT true
             ) as "Ord(s) Sch. for yest. ",
             (
                SELECT count(distinct sbtsk.db_id )
                  FROM cl_tx_srvc_req srvc_req
                 inner join cl_tx_sbtsk as sbtsk
                    on sbtsk.org_id = 2
                   and (sbtsk.c_meta).time::timestamp > $1
                   and (sbtsk.c_meta).time::timestamp < $2
                   and sbtsk.srvc_req_id = srvc_req.db_id
                 WHERE srvc_req.srvc_prvdr = 2
                   AND srvc_req.prvdr_vertical = any(array_agg(vertical.db_id))
                   AND srvc_req.is_deleted IS NOT true
             ) as "Tasks created",
             (
                select count(distinct sbtsk.assigned_to)
                  from cl_tx_sbtsk as sbtsk
                 inner join cl_tx_srvc_req srvc_req
                    on srvc_req.db_id = sbtsk.srvc_req_id
                   AND srvc_req.prvdr_vertical = any(array_agg(vertical.db_id))
                   AND srvc_req.is_deleted IS NOT true
                 where sbtsk.org_id = 2
                   and sbtsk.start_time > $1
                   and sbtsk.start_time < $2
             ) as "Supp. depl",
             (
                select count(*)
      			from (
      				  	SELECT sbtsk.assigned_to
      					  FROM cl_tx_sbtsk AS sbtsk
      					 INNER JOIN cl_tx_srvc_req srvc_req
      					    ON srvc_req.db_id = sbtsk.srvc_req_id
      					   AND srvc_req.prvdr_vertical = any(array_agg(vertical.db_id))
      					   AND srvc_req.is_deleted IS NOT TRUE
      					 WHERE sbtsk.org_id = 2
      					   AND sbtsk.start_time > $1
      					   AND sbtsk.start_time < $2
      					 GROUP BY sbtsk.assigned_to
      					having COUNT(sbtsk.db_id) = COUNT(sbtsk.db_id) FILTER (WHERE sbtsk.status = 'closed')
      			     ) as assignee
             ) as "Supply all closed",
             (
                select count(*)
      			from (
      				  	SELECT sbtsk.assigned_to
      					  FROM cl_tx_sbtsk AS sbtsk
      					 INNER JOIN cl_tx_srvc_req srvc_req
      					    ON srvc_req.db_id = sbtsk.srvc_req_id
      					   AND srvc_req.prvdr_vertical = any(array_agg(vertical.db_id))
      					   AND srvc_req.is_deleted IS NOT TRUE
      					 WHERE sbtsk.org_id = 2
      					   AND sbtsk.start_time > $1
      					   AND sbtsk.start_time < $2
      					 GROUP BY sbtsk.assigned_to
      					having COUNT(sbtsk.db_id) = COUNT(sbtsk.db_id) FILTER (WHERE sbtsk.status = 'open')
      			     ) as assignee
             ) as "Supply no update",
             (
                select count(*)
      			from (
      				  	SELECT sbtsk.assigned_to
      					  FROM cl_tx_sbtsk AS sbtsk
      					 INNER JOIN cl_tx_srvc_req srvc_req
      					    ON srvc_req.db_id = sbtsk.srvc_req_id
      					   AND srvc_req.prvdr_vertical = any(array_agg(vertical.db_id))
      					   AND srvc_req.is_deleted IS NOT TRUE
      					 WHERE sbtsk.org_id = 2
      					   AND sbtsk.start_time > $1
      					   AND sbtsk.start_time < $2
      					 GROUP BY sbtsk.assigned_to
      					having COUNT(sbtsk.db_id) > COUNT(sbtsk.db_id) FILTER (WHERE sbtsk.status = 'open')
      					   and COUNT(sbtsk.db_id) > COUNT(sbtsk.db_id) FILTER (WHERE sbtsk.status = 'closed')
      			     ) as assignee
             ) as "Supp. part update",
             (
                SELECT COUNT(DISTINCT tech.usr_id)
                  FROM cl_tx_users tech
                 WHERE tech.primary_vertical = any(array_agg(vertical.db_id))
                   and tech.is_active = true
             ) AS "Active supply",
             (
                  SELECT COUNT(DISTINCT tech.usr_id)
                  FROM cl_tx_users tech
                  WHERE tech.primary_vertical = any(array_agg(vertical.db_id))
                  and (tech.c_meta).time::timestamp > $1
                  and (tech.c_meta).time::timestamp < $2
             ) AS "New supply",
             (
                SELECT COUNT(DISTINCT tech.usr_id)
                  FROM cl_tx_users tech
                 WHERE tech.primary_vertical = any(array_agg(vertical.db_id))
                   and tech.is_active = false
             ) AS "Inactive supply"
        from cl_tx_orgs_settings as vertical
       where vertical.settings_type = 'SP_CUSTOM_FIELDS'
         and vertical.org_id = 2
         and vertical.db_id <> 2
         and vertical.db_id = any($3)
       group by vertical.org_id
    `,
    [yesterdayStr, todayStr, verticalIds]
  );

  await client.end();

  const result = res.rows[0] || {};
  console.log("DataService::getManagerTotals:: Retrieved totals:", result);

  // Convert string values to numbers and return with consistent property names
  return {
    allTimeOrders: parseInt(result["All time orders"] || 0),
    openOrders: parseInt(result["Open orders"] || 0),
    newOrders: parseInt(result["New orders"] || 0),
    ordersClosed: parseInt(result["Orders closed"] || 0),
    techInch: parseInt(result["Active TIs"] || 0),
    ordersScheduled: parseInt(result["Ord(s) Sch."] || 0),
    ordersSchYest: parseInt(result["Ord(s) Sch. for yest. "] || 0),
    tasksCreated: parseInt(result["Tasks created"] || 0),
    supplyDeployed: parseInt(result["Supp. depl"] || 0),
    supplyAllTaskClosed: parseInt(result["Supply all closed"] || 0),
    supplyNoTaskUpdate: parseInt(result["Supply no update"] || 0),
    supplyPartialTaskUpdate: parseInt(result["Supp. part update"] || 0),
    activeSupply: parseInt(result["Active supply"] || 0),
    newSupply: parseInt(result["New supply"] || 0),
    inactiveSupply: parseInt(result["Inactive supply"] || 0),
    verticalCount: verticalIds.length,
  };
}

/**
 * Get consolidated totals using SQL aggregation for all verticals
 * @returns {Promise<Object>} Object containing aggregated totals for all verticals
 */
async function getConsolidatedTotals() {
  const client = new Client({
    host: process.env.DB_HOST,
    database: process.env.DB_NAME,
    user: process.env.DB_USER,
    password: process.env.DB_PASSWORD,
    port: process.env.DB_PORT,
  });

  await client.connect();

  // Calculate dynamic dates
  const today = new Date();
  const yesterday = new Date(today);
  yesterday.setDate(today.getDate() - 1);

  const todayStr = today.toISOString().split("T")[0]; // YYYY-MM-DD format
  const yesterdayStr = yesterday.toISOString().split("T")[0]; // YYYY-MM-DD format

  console.log(
    "DataService::getConsolidatedTotals:: Using dates - Yesterday:",
    yesterdayStr,
    "Today:",
    todayStr
  );

  const res = await client.query(
    `
      select (
                SELECT COUNT(DISTINCT srvc_req.db_id)
                  FROM cl_tx_srvc_req srvc_req
                 WHERE srvc_req.srvc_prvdr = 2
                   AND srvc_req.prvdr_vertical = any(array_agg(vertical.db_id))
                   AND srvc_req.is_deleted IS NOT TRUE
             ) AS "All time orders",
             (
                SELECT COUNT(DISTINCT srvc_req.db_id)
                  FROM cl_tx_srvc_req srvc_req
                 WHERE srvc_req.srvc_prvdr = 2
                   AND srvc_req.prvdr_vertical = any(array_agg(vertical.db_id))
                   AND srvc_req.is_deleted IS NOT true
                   and srvc_req.status = 'open'
             ) AS "Open orders",
             (
                SELECT COUNT(DISTINCT srvc_req.db_id)
                  FROM cl_tx_srvc_req srvc_req
                 WHERE srvc_req.srvc_prvdr = 2
                   AND srvc_req.prvdr_vertical = any(array_agg(vertical.db_id))
                   AND srvc_req.is_deleted IS NOT true
                   and (srvc_req.c_meta).time::timestamp > $1
                   and (srvc_req.c_meta).time::timestamp < $2
             ) AS "New orders",
             (
                SELECT COUNT(DISTINCT srvc_req.db_id)
                  FROM cl_tx_srvc_req srvc_req
                 WHERE srvc_req.srvc_prvdr = 2
                   AND srvc_req.prvdr_vertical = any(array_agg(vertical.db_id))
                   AND srvc_req.is_deleted IS NOT true
                   and (srvc_req.u_meta).time::timestamp > $1
                   and (srvc_req.u_meta).time::timestamp < $2
                   and srvc_req.status = 'closed'
             ) AS "Orders closed",
             (
                SELECT COUNT(DISTINCT sbtsk.c_by )
                  FROM cl_tx_srvc_req srvc_req
                 inner join cl_tx_sbtsk as sbtsk
                    on sbtsk.org_id = 2
                   and (sbtsk.c_meta).time::timestamp > $1
                   and (sbtsk.c_meta).time::timestamp < $2
                   and sbtsk.srvc_req_id = srvc_req.db_id
                 WHERE srvc_req.srvc_prvdr = 2
                   AND srvc_req.prvdr_vertical = any(array_agg(vertical.db_id))
                   AND srvc_req.is_deleted IS NOT true
             ) as "Tech Inch.",
             (
                SELECT COUNT(DISTINCT srvc_req.db_id)
                  FROM cl_tx_srvc_req srvc_req
                 inner join cl_tx_sbtsk as sbtsk
                    on sbtsk.org_id = 2
                   and (sbtsk.c_meta).time::timestamp > $1
                   and (sbtsk.c_meta).time::timestamp < $2
                   and sbtsk.srvc_req_id = srvc_req.db_id
                 WHERE srvc_req.srvc_prvdr = 2
                   AND srvc_req.prvdr_vertical = any(array_agg(vertical.db_id))
                   AND srvc_req.is_deleted IS NOT true
             ) as "Orders Sch.",
             (
                SELECT COUNT(DISTINCT srvc_req.db_id)
                  FROM cl_tx_srvc_req srvc_req
                 inner join cl_tx_sbtsk as sbtsk
                    on sbtsk.org_id = 2
                   and sbtsk.start_time > $1
                   and sbtsk.start_time < $2
                   and sbtsk.srvc_req_id = srvc_req.db_id
                 WHERE srvc_req.srvc_prvdr = 2
                   AND srvc_req.prvdr_vertical = any(array_agg(vertical.db_id))
                   AND srvc_req.is_deleted IS NOT true
             ) as "Ord(s) Sch. for yest. ",
             (
                SELECT count(distinct sbtsk.db_id )
                  FROM cl_tx_srvc_req srvc_req
                 inner join cl_tx_sbtsk as sbtsk
                    on sbtsk.org_id = 2
                   and (sbtsk.c_meta).time::timestamp > $1
                   and (sbtsk.c_meta).time::timestamp < $2
                   and sbtsk.srvc_req_id = srvc_req.db_id
                 WHERE srvc_req.srvc_prvdr = 2
                   AND srvc_req.prvdr_vertical = any(array_agg(vertical.db_id))
                   AND srvc_req.is_deleted IS NOT true
             ) as "Tasks created",
             (
                select count(distinct sbtsk.assigned_to)
                  from cl_tx_sbtsk as sbtsk
                 inner join cl_tx_srvc_req srvc_req
                    on srvc_req.db_id = sbtsk.srvc_req_id
                   AND srvc_req.prvdr_vertical = any(array_agg(vertical.db_id))
                   AND srvc_req.is_deleted IS NOT true
                 where sbtsk.org_id = 2
                   and sbtsk.start_time > $1
                   and sbtsk.start_time < $2
             ) as "Supp. depl",
             (
                select count(*)
                  from (
                        SELECT sbtsk.assigned_to
                          FROM cl_tx_sbtsk AS sbtsk
                         INNER JOIN cl_tx_srvc_req srvc_req
                            ON srvc_req.db_id = sbtsk.srvc_req_id
                           AND srvc_req.prvdr_vertical = any(array_agg(vertical.db_id))
                           AND srvc_req.is_deleted IS NOT TRUE
                         WHERE sbtsk.org_id = 2
                           AND sbtsk.start_time > $1
                           AND sbtsk.start_time < $2
                         GROUP BY sbtsk.assigned_to
                        having COUNT(sbtsk.db_id) = COUNT(sbtsk.db_id) FILTER (WHERE sbtsk.status = 'closed')
                       ) as assignee
             ) as "Supply all task closed",
             (
                select count(*)
                  from (
                        SELECT sbtsk.assigned_to
                          FROM cl_tx_sbtsk AS sbtsk
                         INNER JOIN cl_tx_srvc_req srvc_req
                            ON srvc_req.db_id = sbtsk.srvc_req_id
                           AND srvc_req.prvdr_vertical = any(array_agg(vertical.db_id))
                           AND srvc_req.is_deleted IS NOT TRUE
                         WHERE sbtsk.org_id = 2
                           AND sbtsk.start_time > $1
                           AND sbtsk.start_time < $2
                         GROUP BY sbtsk.assigned_to
                        having COUNT(sbtsk.db_id) = COUNT(sbtsk.db_id) FILTER (WHERE sbtsk.status <> 'closed')
                       ) as assignee
             ) as "Supply no task update",
             (
                select count(*)
                  from (
                        SELECT sbtsk.assigned_to
                          FROM cl_tx_sbtsk AS sbtsk
                         INNER JOIN cl_tx_srvc_req srvc_req
                            ON srvc_req.db_id = sbtsk.srvc_req_id
                           AND srvc_req.prvdr_vertical = any(array_agg(vertical.db_id))
                           AND srvc_req.is_deleted IS NOT TRUE
                         WHERE sbtsk.org_id = 2
                           AND sbtsk.start_time > $1
                           AND sbtsk.start_time < $2
                         GROUP BY sbtsk.assigned_to
                        having COUNT(sbtsk.db_id) > COUNT(sbtsk.db_id) FILTER (WHERE sbtsk.status = 'closed')
                           and COUNT(sbtsk.db_id) FILTER (WHERE sbtsk.status = 'closed') > 0
                       ) as assignee
             ) as "Supply partial task update",
             (
                SELECT COUNT(DISTINCT tech.usr_id)
                  FROM cl_tx_users tech
                 WHERE tech.primary_vertical = any(array_agg(vertical.db_id))
                   and tech.is_active = true
             ) AS "Active supply",
             (
                  SELECT COUNT(DISTINCT tech.usr_id)
                  FROM cl_tx_users tech
                  WHERE tech.primary_vertical = any(array_agg(vertical.db_id))
                  and (tech.c_meta).time::timestamp > $1
                  and (tech.c_meta).time::timestamp < $2
             ) AS "New supply",
             (
                SELECT COUNT(DISTINCT tech.usr_id)
                  FROM cl_tx_users tech
                 WHERE tech.primary_vertical = any(array_agg(vertical.db_id))
                   and tech.is_active = false
             ) AS "Inactive supply",
             count(vertical.db_id) as "Vertical count"
        from cl_tx_orgs_settings as vertical
       where vertical.settings_type = 'SP_CUSTOM_FIELDS'
         and vertical.org_id = 2
         and vertical.db_id <> 2
         and vertical.db_id <> 1084
         and vertical.db_id <> 827
       group by vertical.org_id
    `,
    [yesterdayStr, todayStr]
  );

  await client.end();

  const result = res.rows[0] || {};
  console.log("DataService::getConsolidatedTotals:: Retrieved totals:", result);

  // Return normalized totals object
  return {
    allTimeOrders: parseInt(result["All time orders"] || 0),
    openOrders: parseInt(result["Open orders"] || 0),
    newOrders: parseInt(result["New orders"] || 0),
    ordersClosed: parseInt(result["Orders closed"] || 0),
    techInch: parseInt(result["Tech Inch."] || 0),
    ordersScheduled: parseInt(result["Orders Sch."] || 0),
    ordersSchYest: parseInt(result["Ord(s) Sch. for yest. "] || 0),
    tasksCreated: parseInt(result["Tasks created"] || 0),
    supplyDeployed: parseInt(result["Supp. depl"] || 0),
    supplyAllTaskClosed: parseInt(result["Supply all task closed"] || 0),
    supplyNoTaskUpdate: parseInt(result["Supply no task update"] || 0),
    supplyPartialTaskUpdate: parseInt(
      result["Supply partial task update"] || 0
    ),
    activeSupply: parseInt(result["Active supply"] || 0),
    newSupply: parseInt(result["New supply"] || 0),
    inactiveSupply: parseInt(result["Inactive supply"] || 0),
    verticalCount: parseInt(result["Vertical count"] || 0),
  };
}

module.exports = {
  getVerticalKPIData,
  getOrdersForVertical,
  getTasksForVertical,
  getManagerTotals,
  getConsolidatedTotals,
};
