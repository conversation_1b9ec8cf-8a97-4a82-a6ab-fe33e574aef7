const categoryManagers = require("./categoryManagers");
const { getVerticalKPIData } = require("./dataService");
const { processManagerEmails } = require("./managerEmailProcessor");
require("dotenv").config(); // Load environment variables

exports.handler = async () => {
  try {
    console.log(
      "TmsWifyKpiEmailer::handler:: Starting KPI email process for category managers"
    );

    // Get data from database
    const verticalKPIData = await getVerticalKPIData();
    console.log(
      "TmsWifyKpiEmailer::handler:: Retrieved data rows:",
      verticalKPIData.length
    );

    // Process and send emails to all category managers
    const results = await processManagerEmails(
      verticalKPIData,
      categoryManagers
    );

    console.log(
      `TmsWifyKpiEmailer::handler:: Email process completed. Success: ${results.successCount}, Failed: ${results.failureCount}`
    );

    return {
      statusCode: 200,
      body: JSON.stringify({
        message: "Category manager KPI emails processed",
        totalManagers: results.emailResults.length,
        successCount: results.successCount,
        failureCount: results.failureCount,
        recordsCount: verticalKPIData.length,
        totalAttachmentsCount: results.totalAttachments.length,
        results: results.emailResults,
      }),
    };
  } catch (error) {
    console.error("TmsWifyKpiEmailer::handler:: Error in handler:", error);
    return {
      statusCode: 500,
      body: JSON.stringify({
        message: "Internal server error",
        error: error.message,
      }),
    };
  }
};
