import CapacityModel from "./capacity";
import BookingModel from "./booking";
import sequelizeInstance from "../config/sequelize";

// Export models and sequelize instance directly
export const Capacity = CapacityModel;
export const Booking = BookingModel;
export const sequelize = sequelizeInstance;

// Initialize all models and their relationships
export const initializeModels = () => {
  // The associations are already defined in the model files
  // This function is mainly for explicitly triggering the initialization
  // Success is logged at a higher level with emojis
};

// Function to sync all models with the database
export const syncModels = async (options = { alter: true }) => {
  try {
    await sequelize.sync(options);
    // Success is logged at a higher level with emojis
  } catch (error) {
    console.error("❌ Error syncing database:", error);
    throw error;
  }
};

// Function to create tables if they don't exist and alter them if needed
export const createTablesIfNotExist = async () => {
  try {
    console.log("🔍 Checking if tables exist and updating schema if needed...");

    // Use alter: true to create tables if they don't exist and update them if they do
    // This will add the new 'day' column to the Capacities table
    await sequelize.sync({ alter: true });

    console.log("✅ Tables created or updated successfully");

    // Update existing records to populate the day column
    try {
      console.log("🔄 Checking for records with null day values...");

      // First check if there are any records with null day values
      const [results] = await sequelize.query(`
        SELECT COUNT(*) as count
        FROM "Capacities"
        WHERE day IS NULL
      `);

      const nullCount = parseInt((results as any)[0].count, 10);

      if (nullCount > 0) {
        console.log(
          `🔄 Found ${nullCount} records with null day values. Updating...`
        );

        // PostgreSQL doesn't support LIMIT in UPDATE statements
        // So we'll update all records at once
        await sequelize.query(`
          UPDATE "Capacities"
          SET day = TO_CHAR(startTime, 'YYYY-MM-DD')
          WHERE day IS NULL
        `);

        console.log(`🔄 Updated ${nullCount} records with day values`);

        console.log("✅ All existing records updated with day values");
      } else {
        console.log("✅ No records with null day values found");
      }
    } catch (updateError) {
      console.warn(
        "⚠️ Warning: Could not update existing records:",
        updateError
      );
      // Continue execution even if this fails
    }

    return true;
  } catch (error) {
    console.error("❌ Error creating/updating tables:", error);
    throw error;
  }
};

// Export models and sequelize instance as default
const models = {
  Capacity,
  Booking,
  sequelize,
};

export default models;
