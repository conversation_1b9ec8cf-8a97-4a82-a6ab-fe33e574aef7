# TMS Redis Insights Lambda

This Lambda function provides comprehensive insights into TMS Redis usage patterns, memory consumption, and key distribution.

## Features

- **Pattern Analysis**: Analyzes Redis keys and groups them by patterns (prefix before first underscore)
- **Pattern Memory Usage**: Calculates memory usage in GB for each pattern by sampling keys
- **Memory Usage**: Reports Redis memory consumption statistics
- **Server Information**: Provides Redis server version and uptime information
- **Sample Keys**: Optionally includes sample keys for each pattern for debugging purposes
- **Batch Processing**: Efficiently processes large Redis datasets using SCAN with batching

## Configuration

Set the following environment variables:

```bash
REDIS_HOST=your-redis-endpoint.amazonaws.com
REDIS_PORT=6379
REDIS_PASSWORD=your-redis-password  # Optional
```

## Usage

### Basic Invocation

```javascript
// Event payload (all fields optional)
{
  "includeSamples": true  // Include sample keys in response (default: true)
}
```

### Response Format

```javascript
{
  "statusCode": 200,
  "body": {
    "success": true,
    "data": {
      "patternAnalysis": {
        "totalKeys": 15420,
        "uniquePatterns": 8,
        "patterns": {
          "user": 8500,
          "session": 3200,
          "cache": 2100,
          "temp": 890,
          "config": 450,
          "lock": 180,
          "queue": 75,
          "stats": 25
        },
        "patternMemoryUsage": {
          "user": {
            "totalBytes": 425000000,
            "totalGB": "0.3958",
            "avgBytesPerKey": 50000,
            "sampledKeys": 50,
            "totalKeys": 8500
          },
          "session": {
            "totalBytes": 160000000,
            "totalGB": "0.1490",
            "avgBytesPerKey": 50000,
            "sampledKeys": 50,
            "totalKeys": 3200
          },
          "cache": {
            "totalBytes": 315000000,
            "totalGB": "0.2934",
            "avgBytesPerKey": 150000,
            "sampledKeys": 50,
            "totalKeys": 2100
          }
        },
        "totalEstimatedMemoryBytes": 900000000,
        "totalEstimatedMemoryGB": "0.8382",
        "timestamp": "2025-01-20T10:30:00.000Z"
      },
      "memoryInfo": {
        "used_memory": "52428800",
        "used_memory_human": "50.00M",
        "used_memory_rss": "67108864"
      },
      "serverInfo": {
        "redis_version": "6.2.0",
        "uptime_in_seconds": "86400",
        "uptime_in_days": "1"
      },
      "sampleKeys": {
        "user": ["user_123", "user_456", "user_789"],
        "session": ["session_abc", "session_def", "session_ghi"],
        "cache": ["cache_data_1", "cache_data_2", "cache_data_3"]
      },
      "analysisTimestamp": "2025-01-20T10:30:00.000Z"
    }
  }
}
```

## Pattern Extraction Logic

The function extracts patterns by taking the prefix before the first underscore:

- `user_123` → `user`
- `session_abc_def` → `session`
- `cache_data_456` → `cache`
- `simplekey` → `simplekey` (no underscore)

## Performance Considerations

- Uses Redis SCAN command with batching to avoid blocking the Redis server
- Processes keys in batches of 1000 for optimal performance
- Logs progress every 10,000 processed keys for large datasets
- Sample key collection can be disabled for better performance on large datasets

## Testing

Run the test suite:

```bash
npm test
```

The test suite includes:

- Unit tests for pattern extraction logic
- Mock Redis integration tests
- Error handling scenarios

## Deployment

This function is designed to be deployed using the AppAutoLayer deployment system. Ensure all dependencies are installed:

```bash
npm install
```

## Error Handling

The function includes comprehensive error handling:

- Redis connection failures
- Redis command timeouts
- Memory allocation issues
- Graceful disconnection in all scenarios

## Monitoring

The function logs detailed information about:

- Connection status
- Processing progress
- Pattern analysis results
- Error conditions
- Performance metrics
