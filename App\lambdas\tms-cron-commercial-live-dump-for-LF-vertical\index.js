const { Client } = require("pg");
const { google } = require("googleapis");
const fs = require("fs");
const SERVICE_ACCOUNT_FILE = "/tmp/credentials.json";
const SCOPES = ["https://www.googleapis.com/auth/spreadsheets"];

// const userId = "177886be-ba77-410a-82b2-deb762b8c1c4";
const userId = "74095c4e-5d83-4607-895f-f0df5815c0c2";
const selectedLocationGrps = [
  "KLH",
  "AMD",
  "BOM",
  "PNQ",
  "BDQ",
  "IDR",
  "STV",
  "NAG",
  "KOA",
  "BBI",
  "BLR",
  "HYD",
  "VGA",
  "DEL",
  "GGN",
  "IGI",
  "GZB",
  "TPJ",
  "TRV",
  "MAA",
  "CMB",
  "COK",
  "LKO",
  "BHO",
  "GOI",
  "KRL",
  "JAI",
  "ISK",
  "UTP",
  "WTB",
  "VTZ",
  "UDR",
  "AJL",
  "IHP",
  "TCR",
  "BZA",
  "NIA",
  "KTU",
  "IXR",
  "MYQ",
  "GOI",
  "GAU",
  "IXC",
  "BHJ",
  "RPR",
];

const currentDate = new Date();
const endDate = new Date();
endDate.setDate(currentDate.getDate() - 365);
const verticalTypeId = 11;

const customerReqData = {
  formData: {
    email_id: "<EMAIL>",
    subject: "WIFY TMS Customer requests dump May-29-2025 12:58 PM",
    org_info: ["brand_name", "service_type", "vertical_fr_req_dump"],
    customer_info_fields: ["cust_mobile", "cust_full_name", "cust_email"],
    address_info_fields: [
      "cust_line_0",
      "cust_line_1",
      "cust_line_2",
      "cust_line_3",
      "cust_pincode",
      "cust_city",
      "cust_state",
      "location_group",
    ],
    request_info_fields: [
      "request_description",
      "request_req_date",
      "request_priority",
      "request_labels",
      "request_cc_users",
      "creation_date",
      "vertical_title",
    ],
    onfield_task_fields: [
      "assignee_name",
      "task_status",
      "task_start_date",
      "assigned_by",
      "assigned_date",
      "assigned_time",
      "task_status_remark",
      "task_geo_verification_status",
      "assignee_phone_no.",
      "sp_first_task_date",
      "sp_last_task_date",
      "gai_remarks",
    ],
    SP_specific_fields: [
      "4145d8b2-223f-4952-9397-e756f8595299",
      "d9f97ede-83d6-4119-af96-e65d1a9ab7c8",
      "2219be24-bd31-40c2-a793-a1c0535f6c4c",
      "6922213e-b692-4b6c-b651-cdf0019e65fe",
      "c7e6789a-ee0c-4057-850c-d355c7755b84",
      "3270d813-c67f-429e-8ce2-848f32006a93",
      "a94a5011-5a27-427d-a5ab-5d4f4055c252",
      "760f9076-533d-40aa-aac2-eec54aeb5fc0",
      "ff7f923c-0f31-47da-81f6-b009fa2a33e1",
    ],
    gai_info_fields: ["avg_gai_rating", "no_of_gai_rated_tasks"],
    locked_for_change: [
      "sp_locked_for_change",
      "sp_locked_for_change_by",
      "sp_locking_date",
    ],
    calculated_task_data: ["mandays", "sp_work_progress"],
    line_item_data: ["sp_total_quantity", "sp_total_price"],
    pi_info: ["final_amount", "sp_sent_for_billing"],
    sp_attachment_field_info: ["3b68fbd4-7cc9-4448-aaa6-5145b6cfe7bc"],
    field_label_mapping: {
      brand_name: "Brand",
      service_type: "Service Type",
      vertical_fr_req_dump: "Vertical",
      cust_mobile: "Mobile(+91)",
      cust_full_name: "Name",
      cust_email: "Email",
      cust_line_0: "Flat no",
      cust_line_1: "Building/Apartment name",
      cust_line_2: "Line 1",
      cust_line_3: "Line 2",
      cust_pincode: "Pincode",
      cust_city: "City",
      cust_state: "State",
      location_group: "Location Group",
      request_description: "Description",
      request_req_date: "Req. Service Date",
      request_priority: "Priority",
      request_labels: "Labels",
      request_cc_users: "CC users",
      creation_date: "Creation Date",
      vertical_title: "Request vertical",
      assignee_name: "Assignee name",
      task_status: "Task status",
      task_start_date: "Task start date",
      assigned_by: "Assigned by",
      assigned_date: "Assigned date",
      assigned_time: "Assigned time",
      task_status_remark: "Task status remark",
      task_geo_verification_status: "GEO verification status",
      "assignee_phone_no.": "Assignee Phone No.",
      sp_first_task_date: "SP First task date",
      sp_last_task_date: "SP Last task date",
      gai_remarks: "GAI remarks",
      "4145d8b2-223f-4952-9397-e756f8595299": "Qty :",
      "d9f97ede-83d6-4119-af96-e65d1a9ab7c8": "Technician Name for Amazon : ",
      "2219be24-bd31-40c2-a793-a1c0535f6c4c":
        "Technician Rate (Product assembly rates only, No TA or Additional amount) :",
      "6922213e-b692-4b6c-b651-cdf0019e65fe": "Already Paid to Technician :",
      "c7e6789a-ee0c-4057-850c-d355c7755b84": "Additional Amount :",
      "3270d813-c67f-429e-8ce2-848f32006a93": "Additional Amount reason :",
      "a94a5011-5a27-427d-a5ab-5d4f4055c252": "Additional Revenue",
      "760f9076-533d-40aa-aac2-eec54aeb5fc0": "COD method by wify ",
      "ff7f923c-0f31-47da-81f6-b009fa2a33e1": "COD Amount",
      avg_gai_rating: "Avg. GAI rating",
      no_of_gai_rated_tasks: "No. of GAI rated tasks",
      sp_locked_for_change: "SP Locked for change (Yes/No)",
      sp_locked_for_change_by: "SP Locked for change by",
      sp_locking_date: "SP Locking Date",
      mandays: "Mandays",
      sp_work_progress: "SP Work progress percentage",
      sp_total_quantity: "SP Total quantity",
      sp_total_price: "SP Total price",
      final_amount: "Final amount",
      sp_sent_for_billing: "SP sent for billing",
      "3b68fbd4-7cc9-4448-aaa6-5145b6cfe7bc": "Invoice File",
      profit_and_loss_data: "Profit and loss data",
      tech_cost_breakdown: "Tech cost breakdown",
      service_type_id: "Service Type ID",
    },
    selected_columns: [
      "brand_name",
      "service_type",
      "vertical_fr_req_dump",
      "mandays",
      "sp_total_quantity",
      "sp_total_price",
      "final_amount",
      "sp_sent_for_billing",
      "profit_and_loss_data",
      "tech_cost_breakdown",
      "service_type_id",
    ],
    filters:
      '{"assgn_to_prvdr_date":["2024-12-29T07:28:21.357Z","2025-05-29T07:28:21.357Z"],"verticals_list":[11]}',
    pagination: "{}",
    org_id: 2,
    usr_id: userId,
    ip_addr: "************",
    user_agent:
      "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
    srvc_type_id: "0",
    vertical_id: verticalTypeId,
    is_customer_access: "0",
  },
  filters: {
    assgn_to_prvdr_date: [endDate.toISOString(), currentDate.toISOString()],
    srvc_status_category: ["CLOSED", "DONE"],
    verticals_list: [verticalTypeId],
  },
};
const verticalConfigQuery = {
  org_id: 2,
  usr_id: userId,
  vertical_id: verticalTypeId,
  ip_addr: "::1",
  user_agent:
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
};

async function getCustomerRequestData({ filters }) {
  const dbClient = new Client({
    host: process.env.DB_HOST,
    database: process.env.DB_NAME,
    user: process.env.DB_USER,
    password: process.env.DB_PASSWORD,
    port: 5432,
  });
  try {
    await dbClient.connect();
    console.log("getCustomerRequestData :: Connected to the database!");

    console.log(
      "getCustomerRequestData :: formData",
      JSON.stringify(customerReqData.formData)
    );
    console.log("getCustomerRequestData :: filters", JSON.stringify(filters));

    const res = await dbClient.query(
      `SELECT public.tms_get_srvc_req_dumps_fr_lf_vertical_lambda('${JSON.stringify(
        customerReqData.formData
      )}', '${JSON.stringify(filters)}')`
    );

    const resp = res.rows[0].tms_get_srvc_req_dumps_fr_lf_vertical_lambda;
    // console.log("getCustomerRequestData :: resp :: ", resp);
    return resp;
  } catch (error) {
    console.error("getCustomerRequestData :: Error :", error);
  } finally {
    await dbClient.end();
  }
}

async function getVerticalTypeConfigData() {
  const dbClient = new Client({
    host: process.env.DB_HOST,
    database: process.env.DB_NAME,
    user: process.env.DB_USER,
    password: process.env.DB_PASSWORD,
    port: 5432,
  });
  try {
    await dbClient.connect();
    console.log("getVerticalTypeConfigData :: Connected to the database");
    console.log(
      `getVerticalTypeConfigData :: ✅ Connected to DB: ${dbClient.database} at ${dbClient.host}:${dbClient.port}`
    );

    const res = await dbClient.query(
      `SELECT public.tms_hlpr_get_config_data_for_srvc_type_or_vertical('${JSON.stringify(
        verticalConfigQuery
      )}')`
    );

    // Access the first row
    const firstRow = res.rows[0];
    if (
      !firstRow ||
      !firstRow.tms_hlpr_get_config_data_for_srvc_type_or_vertical
    ) {
      throw new Error("No data returned from function");
    }

    const resp = firstRow.tms_hlpr_get_config_data_for_srvc_type_or_vertical;
    console.log("getVerticalTypeConfigData :: resp :: ", resp);
    return resp?.data?.config_data;
  } catch (err) {
    console.error("getVerticalTypeConfigData :: Error:", err);
  } finally {
    await dbClient.end();
  }
}

async function getSrvcTypesConfigData(srvcTypeIds) {
  const dbClient = new Client({
    host: process.env.DB_HOST,
    database: process.env.DB_NAME,
    user: process.env.DB_USER,
    password: process.env.DB_PASSWORD,
    port: 5432,
  });
  const query = {
    srvc_type_ids: srvcTypeIds,
  };
  console.log("getSrvcTypesConfigData query", JSON.stringify(query));
  try {
    await dbClient.connect();
    console.log("getSrvcTypesConfigData :: Connected to the database");

    const res = await dbClient.query(
      `SELECT public.tms_hlpr_get_srvc_types_config_data('${JSON.stringify(
        query
      )}')`
    );

    // Access the first row
    const firstRow = res.rows[0];
    if (!firstRow || !firstRow.tms_hlpr_get_srvc_types_config_data) {
      throw new Error("No data returned from function");
    }

    const resp = firstRow.tms_hlpr_get_srvc_types_config_data;
    console.log("getSrvcTypesConfigData :: resp :: ", resp);
    return resp;
  } catch (err) {
    console.error("getSrvcTypesConfigData :: Error:", err);
  } finally {
    await dbClient.end();
  }
}

function getSimplifiedVerticalAndSrvcTypeConfig({
  verticalTypeConfigData,
  srvcTypesConfigObj,
}) {
  const revenueColumnMeta = verticalTypeConfigData?.revenue_column_meta;
  const srvcTypeRevenueColumnMeta =
    verticalTypeConfigData?.srvc_type_revenue_column_meta;
  let verticalTypePnlModifiedData = {};
  let srvcTypePnlModifiedData = {};
  const spCustomFields = verticalTypeConfigData?.sp_cust_fields_json;
  const spCustomFieldsObj = JSON.parse(spCustomFields || "{}");
  const spTranslatedFields = spCustomFieldsObj?.translatedFields || [];
  let overAllPnLConfigedKeys = [];
  let overAllPnLConfigedKeyVsLabel = {};

  if (revenueColumnMeta) {
    const parsedRevenueColumnMeta = JSON.parse(revenueColumnMeta);
    verticalTypePnlModifiedData[verticalTypeId] = {};
    parsedRevenueColumnMeta.forEach((config) => {
      const { item_name, qty, sku } = config;
      overAllPnLConfigedKeys = [...overAllPnLConfigedKeys, sku, item_name, qty];
      verticalTypePnlModifiedData[verticalTypeId]["sku"] = sku;
      verticalTypePnlModifiedData[verticalTypeId]["item_name"] = item_name;
      verticalTypePnlModifiedData[verticalTypeId]["qty"] = qty;

      spTranslatedFields.forEach((singleSpTranslatedFields) => {
        if (singleSpTranslatedFields.widget == "select") {
          if (
            !verticalTypePnlModifiedData[verticalTypeId][
              "selectFieldsVsOptions"
            ]
          ) {
            verticalTypePnlModifiedData[verticalTypeId][
              "selectFieldsVsOptions"
            ] = {};
          }
          verticalTypePnlModifiedData[verticalTypeId]["selectFieldsVsOptions"][
            singleSpTranslatedFields.key
          ] = singleSpTranslatedFields.options;
        }
      });
    });
    console.log("verticalTypePnlModifiedData", verticalTypePnlModifiedData);
  }

  if (srvcTypeRevenueColumnMeta) {
    const parsedSrvcTypeRevenueColumnMeta = JSON.parse(
      srvcTypeRevenueColumnMeta
    );
    parsedSrvcTypeRevenueColumnMeta.forEach((config) => {
      const { srvc_type, item_name, qty, sku } = config;
      const srvcCustomFields =
        srvcTypesConfigObj?.[srvc_type]?.srvc_cust_fields_json;
      const srvcTypeCustomFieldsObj = JSON.parse(srvcCustomFields || "{}");
      const srvcTypeTranslatedFields =
        srvcTypeCustomFieldsObj?.translatedFields || [];
      overAllPnLConfigedKeys = [...overAllPnLConfigedKeys, sku, item_name, qty];

      if (!srvcTypePnlModifiedData[srvc_type]) {
        srvcTypePnlModifiedData[srvc_type] = {};
      }
      srvcTypePnlModifiedData[srvc_type]["sku"] = sku;
      srvcTypePnlModifiedData[srvc_type]["item_name"] = item_name;
      srvcTypePnlModifiedData[srvc_type]["qty"] = qty;

      srvcTypeTranslatedFields.forEach((singleSrvcTypeTranslatedFields) => {
        if (singleSrvcTypeTranslatedFields.widget == "select") {
          if (!srvcTypePnlModifiedData[srvc_type]["selectFieldsVsOptions"]) {
            srvcTypePnlModifiedData[srvc_type]["selectFieldsVsOptions"] = {};
          }
          srvcTypePnlModifiedData[srvc_type]["selectFieldsVsOptions"][
            singleSrvcTypeTranslatedFields.key
          ] = singleSrvcTypeTranslatedFields.options;
        }
      });
    });
  }
  console.log("srvcTypePnlModifiedData", srvcTypePnlModifiedData);
  return { verticalTypePnlModifiedData, srvcTypePnlModifiedData };
}

async function clearGoogleSheet({ sheets, sheetName, spreadsheetId }) {
  // Clear the sheet if it has any data
  try {
    const getResp = await sheets.spreadsheets.values.get({
      spreadsheetId,
      range: `${sheetName}`, // Use custom range or default to full sheet
    });

    const existingRows = getResp.data.values || [];

    if (existingRows.length > 0) {
      console.log(
        `Sheet ${sheetName} already has ${existingRows.length} rows. Clearing it...`
      );

      await sheets.spreadsheets.values.clear({
        spreadsheetId,
        range: `${sheetName}`, // Clears the entire sheet/tab
      });

      console.log(`Sheet ${sheetName} cleared successfully.`);
    }
  } catch (err) {
    console.error(
      `Error checking or clearing the sheet ${sheetName}:`,
      err.message
    );
    throw err;
  }
}

async function updateGoogleSheet({
  data,
  rangeStart,
  sheetName,
  srvcTypesConfigObj,
  verticalTypeConfigData,
  verticalTypePnlModifiedData,
  srvcTypePnlModifiedData,
}) {
  const auth = new google.auth.GoogleAuth({
    keyFile: SERVICE_ACCOUNT_FILE,
    scopes: SCOPES,
    autoRetry: true,
    maxRetries: 5,
    retryDelayMultiplier: 2,
    totalTimeout: 60000,
  });

  const sheets = google.sheets({
    version: "v4",
    auth,
    axios: {
      timeout: 60000,
    },
  });
  const spreadsheetId = process.env.SPREADSHEET_ID;

  await clearGoogleSheet({
    sheets,
    sheetName: `${sheetName}!A2:Z`,
    spreadsheetId,
  });

  const chunkSize = 3000;
  console.log("rangeStart", rangeStart);
  console.log("total data", data?.length);
  const startingRow = parseInt(rangeStart.replace(/[^\d]/g, ""), 10) || 1;
  const delay = (ms) => new Promise((resolve) => setTimeout(resolve, ms));
  console.log("startingRow", startingRow);
  const columnOrder = [
    "Ticket Id",
    "Brand",
    "Item_SKU",
    "Product_Type",
    "Category",
    "Price to brand",
    "WIFY Cost code",
    "Cost",
    "Product Qty",
    "Total Mandays",
    "Total Direct Cost",
    "Additional Cost/ Expenses",
    "Total cost",
    "Total Direct Revenue",
    "Addional Revenue/ Expenses",
    "Total Revenue",
    "Gross Margin",
    "GM%",
  ];

  // Step 1: Trim all keys in each row
  data = data.map((row) => {
    const trimmedRow = {};
    for (const key in row) {
      if (Object.hasOwnProperty.call(row, key)) {
        const trimmedKey = key.trim(); // Trim the key
        trimmedRow[trimmedKey] = row[key]; // Assign value to the trimmed key
      }
    }
    return trimmedRow;
  });

  for (let i = 0; i < data.length; i += chunkSize) {
    const chunk = data.slice(i, i + chunkSize);

    // Map data to the desired column order
    const values = chunk.map((row) => {
      let sumOfActualCost = 0;
      let sumOfMandays = 0;
      // console.log("single row",row);
      if (row["Profit and loss data"]) {
        const sku = row["Profit and loss data"]["sku"];
        row["Item_SKU"] = sku;
        row["Addional Revenue/ Expenses"] =
          row["Profit and loss data"]["additional_revenue"] > 0
            ? row["Profit and loss data"]["additional_revenue"]
            : "";
        row["Additional Cost/ Expenses"] =
          row["Profit and loss data"]["additional_cost"] > 0
            ? row["Profit and loss data"]["additional_cost"]
            : "";
        if (sku) {
          row["Price to brand"] =
            row["Profit and loss data"]?.["revenue_master_data"]?.[
              `${sku}_actual_revenue_fr_sheet`
            ];
        }
        if (row["Profit and loss data"]?.["revenue_master_data"]) {
          const srvcType = row["Service Type ID"];
          const itemNameKey =
            srvcTypePnlModifiedData?.[srvcType]?.item_name ||
            verticalTypePnlModifiedData?.[verticalTypeId]?.item_name;
          const qtyKey =
            srvcTypePnlModifiedData?.[srvcType]?.qty ||
            verticalTypePnlModifiedData?.[verticalTypeId]?.qty;
          const selectFieldsVsOptions =
            srvcTypePnlModifiedData?.[srvcType]?.selectFieldsVsOptions ||
            verticalTypePnlModifiedData?.[verticalTypeId]
              ?.selectFieldsVsOptions;
          const itemNameValue =
            row["Profit and loss data"]?.["revenue_master_data"]?.[itemNameKey];
          const qtyValue =
            row["Profit and loss data"]?.["revenue_master_data"]?.[qtyKey];
          if (selectFieldsVsOptions?.[itemNameKey]) {
            let productType;
            selectFieldsVsOptions[itemNameKey].forEach((singleOptionsField) => {
              if (singleOptionsField.value == itemNameValue) {
                productType = singleOptionsField?.label;
              }
            });
            row["Product_Type"] = productType;
          } else {
            row["Product_Type"] = itemNameValue;
          }

          if (selectFieldsVsOptions?.[qtyKey]) {
            let qty;
            selectFieldsVsOptions[qtyKey].forEach((singleOptionsField) => {
              if (singleOptionsField.value == qtyValue) {
                qty = singleOptionsField?.label;
              }
            });
            row["Product Qty"] = qty;
          } else {
            row["Product Qty"] = qtyValue;
          }
        }
        if (sku && row["Profit and loss data"]?.["technician_master_data"]) {
          const technicianCost =
            row["Profit and loss data"]?.["technician_master_data"]?.[
              `${sku}_technicianCost`
            ];
          row["Cost"] =
            row["Profit and loss data"]?.["technician_master_data"]?.[
              `${sku}_technicianCost`
            ];
        }

        if (row["Tech cost breakdown"]) {
          console.log("inside tech BD");
          const techCostBreakdownData = row["Tech cost breakdown"] || [];
          techCostBreakdownData.forEach((singleTechCostBreakDownData) => {
            const mandays = singleTechCostBreakDownData.mandays;
            if (mandays) {
              // sumOfActualCost = sumOfActualCost + (mandays * technicianCost)
              sumOfMandays = sumOfMandays + mandays;
            }
          });
          // console.log("sumOfActualCost",sumOfActualCost);
          console.log("sumOfMandays", sumOfMandays);

          // row["Total Direct Cost"] = sumOfActualCost ? sumOfActualCost : '';
          row["Total Mandays"] = sumOfMandays ? sumOfMandays : "";
        }
      }

      return columnOrder.map((key) => {
        return key === undefined || row[key] === null ? "" : row[key];
      });
    });
    console.log("values", values);
    const resource = { values };

    // Calculate the range for the current chunk
    const currentRangeStartRow = startingRow + i; // Adjust based on the chunk index
    const currentRange = `${sheetName}!A${currentRangeStartRow}`;
    console.log("currentRange", currentRange);

    try {
      console.log(
        `updateGoogleSheet :: Uploading chunk ${i + 1} to ${
          i + chunk.length
        }, starting at row ${currentRangeStartRow}`
      );
      await sheets.spreadsheets.values.update({
        spreadsheetId,
        range: currentRange,
        valueInputOption: "USER_ENTERED",
        resource,
      });
      console.log(
        `updateGoogleSheet :: Delay for 2 seconds after chunk ${i + 1}`
      );
      await delay(2000);
    } catch (error) {
      console.error(
        "updateGoogleSheet :: Error updating Google Sheet:",
        error.message
      );
      throw error;
    }
  }

  console.log("updateGoogleSheet :: Google Sheet updated successfully.");
}

async function fetchYearlyDataInChunks() {
  const totalChunks = 3; // 12 months / 4 = 3 chunks
  const monthsPerChunk = 4;

  let allResults = [];

  let endDate = new Date(); // today

  for (let i = 0; i < totalChunks; i++) {
    // Calculate the start date for this chunk
    const startDate = new Date(endDate);
    startDate.setMonth(startDate.getMonth() - monthsPerChunk);

    // Format to YYYY-MM-DD (only date matters in DB)
    const startDateStr = startDate.toISOString().split("T")[0];
    const endDateStr = endDate.toISOString().split("T")[0];

    const filters = {
      assgn_to_prvdr_date: [startDateStr, endDateStr],
      srvc_status_category: ["CLOSED", "DONE"],
      verticals_list: [verticalTypeId],
    };

    console.log(`Fetching chunk ${i + 1}: ${startDateStr} to ${endDateStr}`);

    const chunkData = (await getCustomerRequestData({ filters })) || [];
    console.log(
      `chunkData for chunk ${i + 1} has total data: ${chunkData.length}`
    );

    allResults = [...allResults, ...chunkData];

    // Move the endDate back for the next iteration
    endDate = new Date(startDate);
    endDate.setDate(endDate.getDate() - 1);
  }

  return allResults;
}

exports.handler = async (event) => {
  const credentials = require("./creds.json");

  fs.writeFileSync(SERVICE_ACCOUNT_FILE, JSON.stringify(credentials));
  let sheetName = "SheetOne";
  const verticalTypeConfigData = await getVerticalTypeConfigData();
  console.log("verticalTypeConfigData", verticalTypeConfigData);

  const srvcTypeIds = verticalTypeConfigData?.srvc_type_id;
  const srvcTypesConfigObj = await getSrvcTypesConfigData(srvcTypeIds);
  console.log("srvcTypesConfigObj", srvcTypesConfigObj);

  const { verticalTypePnlModifiedData, srvcTypePnlModifiedData } =
    getSimplifiedVerticalAndSrvcTypeConfig({
      verticalTypeConfigData,
      srvcTypesConfigObj,
    });

  const customerRequestData = await fetchYearlyDataInChunks();

  console.log("total customerRequestData", customerRequestData?.length);

  await updateGoogleSheet({
    data: customerRequestData,
    rangeStart: `${sheetName}!A2`,
    sheetName,
    srvcTypesConfigObj,
    verticalTypeConfigData,
    verticalTypePnlModifiedData,
    srvcTypePnlModifiedData,
  });

  return {
    statusCode: 200,
    body: JSON.stringify("Data updated successfully"),
  };
};
