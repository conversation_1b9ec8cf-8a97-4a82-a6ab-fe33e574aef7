const axios = require("axios");
const oauth = require("oauth-1.0a");
const crypto = require("crypto");
const productOptionsMap = require("./productOptionsMap");
const articleProductOptions = require("./ArticleVsProductMap");

function emptyMultipleFields(
  manipulatedFieldValues,
  labelToKeyMap,
  labelsArray
) {
  // Assuming labelToKeyMap and manipulatedFieldValues are already defined
  labelsArray.forEach((label) => {
    const key = labelToKeyMap[label];
    if (key) {
      manipulatedFieldValues[key] = "";
    }
  });
}

const modifyFieldInMeta = (meta, fieldKey, newKeyValueObj) => {
  meta.map((singleField, index) => {
    if (singleField.key == fieldKey) {
      singleField = {
        ...singleField,
        ...newKeyValueObj,
      };
      meta[index] = singleField;
    }
  });
};

const getObjByKeyFrmArray = (array = [], key, value) => {
  return array.filter((singleObj) => singleObj[key] == value)?.[0];
};

function hideEverythingOnTheFormExcept(meta, exceptionLabels = []) {
  meta.map((singleField, index) => {
    singleField = {
      ...singleField,
      hide: !exceptionLabels.includes(singleField.label),
    };
    meta[index] = singleField;
  });
}

const getArrayByKeyFrmArray = (array = [], key, filterArray = []) => {
  return array.filter((singleObj) => filterArray.includes(singleObj[key]));
};

function updateManipulatedFieldValues({
  manipulatedFieldValues = {},
  labelsToCheck = [],
  meta,
  allValues,
  labelToKeyMap,
  selectLabel,
}) {
  const isLabelsPresent = labelsToCheck.filter((_label) =>
    selectedLabelIs(meta, allValues, labelToKeyMap, selectLabel, _label)
  );
  if (
    labelsToCheck.length === 0 ||
    !labelsToCheck.includes(isLabelsPresent[0])
  ) {
    manipulatedFieldValues[labelToKeyMap[selectLabel]] = "";
  }
}

function getNumberOfProductsToShow(
  allValues,
  numberOfLineItemsOptionsMap,
  labelToKeyMap,
  request_data
) {
  const selectedRadio =
    allValues[labelToKeyMap["Number of Products"]] ||
    request_data[labelToKeyMap["Number of Products"]];

  const radioOption = getObjByKeyFrmArray(
    numberOfLineItemsOptionsMap,
    "value",
    selectedRadio
  );
  return radioOption?.label;
}

function getNumberOfReplacementProductsToShow(
  allValues,
  numberOfLineItemsOptionsMap,
  labelToKeyMap,
  request_data
) {
  const selectedRadio =
    allValues[labelToKeyMap["Replacement Product Count"]] ||
    request_data[labelToKeyMap["Replacement Product Count"]];

  const radioOption = getObjByKeyFrmArray(
    numberOfLineItemsOptionsMap,
    "value",
    selectedRadio
  );
  return radioOption?.label;
}

function modifyProductFields(meta, productDetails, labelToKeyMap, updateMeta) {
  Object.keys(productDetails).map((singleLabel) => {
    modifyFieldInMeta(
      meta,
      labelToKeyMap[productDetails[singleLabel]],
      updateMeta
    );
  });
}

function buildOptions(data) {
  return data.map((item) => ({
    label: item.cl_cf_prds_title,
    value: item.cl_cf_prds_id,
  }));
}

function filterProductsByParentId(products, parentId) {
  return products.filter(
    (product) => product.cl_cf_prds_par_id === String(parentId)
  );
}

function getKeyByValue(data, value) {
  return Object.keys(data).find((key) => data[key] === value);
}

function getLabelByValue(data, value) {
  const item = data.find((obj) => obj.value === value);
  return item ? item.label : undefined;
}

function getValuesByLabels(objectArray, labelsArray) {
  return objectArray
    .filter((item) => labelsArray.includes(item.label))
    .map((item) => item.value);
}

function hideMultipleFields(meta, labelToKeyMap, mandatoryLabels = []) {
  mandatoryLabels.forEach((singleField) => {
    modifyFieldInMeta(meta, labelToKeyMap[singleField], {
      hide: true,
    });
  });
}

function showMultipleFields(meta, labelToKeyMap, mandatoryLabels = []) {
  mandatoryLabels.forEach((singleField) => {
    modifyFieldInMeta(meta, labelToKeyMap[singleField], {
      hide: false,
    });
  });
}

function getOptionsByFieldKey(meta, key) {
  const field = meta.find((item) => item.key === key);
  return field ? field.options : [];
}

function getCategoryLabels(productOptionsMap, brand) {
  return productOptionsMap[brand] ? Object.keys(productOptionsMap[brand]) : [];
}

function getSubCategoriesLabels(productOptionsMap, brand, category) {
  return productOptionsMap[brand] && productOptionsMap[brand][category]
    ? Object.keys(productOptionsMap[brand][category])
    : [];
}

function filteredOptions(options, optionsToSelect) {
  if (!optionsToSelect) {
    return [];
  }
  return options.filter((option) => optionsToSelect.includes(option.label));
}

function getProdNameOptions(data, targetKey) {
  for (const category in data) {
    for (const subcategory in data[category]) {
      if (data[category][subcategory][targetKey] !== undefined) {
        return data[category][subcategory][targetKey];
      }
    }
  }
  return null; // Return null if the key is not found
}

function getProductByArticle(articleLabel, data) {
  const index = data.articleNo.indexOf(articleLabel);
  if (index !== -1) {
    return data.productName[index];
  }
  return null; // Return null if the article label is not found
}

const handler = async (event) => {
  let responseStatus = false;
  let responseMessage = "Unknown error";
  let manipulatedFieldValues = {};

  let { meta, allValues, changedValues, request_data, currentMeta } = event;

  console.log("event", event);
  console.log("meta", JSON.stringify(meta));

  const labelToKeyMap = {};

  meta.map((singleField) => {
    labelToKeyMap[singleField.label || singleField.cust_component_value] =
      singleField.key;
  });

  console.log(labelToKeyMap);

  const replacementProductFieldsMap = [
    {
      reason_fr_replacement_label: "Reason for Replacement 1",
      replacement_type_label: "Replacement type 1",
    },
    {
      reason_fr_replacement_label: "Reason for Replacement 2",
      replacement_type_label: "Replacement type 2",
    },
    {
      reason_fr_replacement_label: "Reason for Replacement 3",
      replacement_type_label: "Replacement type 3",
    },
    {
      reason_fr_replacement_label: "Reason for Replacement 4",
      replacement_type_label: "Replacement type 4",
    },
    {
      reason_fr_replacement_label: "Reason for Replacement 5",
      replacement_type_label: "Replacement type 5",
    },
  ];

  const fullProductMap = [
    {
      prod_name: "Product Name 1",
      article_no: "Article no 1",
      replacement_qty: "Replacement Qty 1",
      date_of_purchase: "Date of Purchase 1",
      replacement_remarks: "Replacement remarks 1",
    },
    {
      prod_name: "Product Name 2",
      article_no: "Article no 2",
      replacement_qty: "Replacement Qty 2",
      date_of_purchase: "Date of Purchase 2",
      replacement_remarks: "Replacement remarks 2",
    },
    {
      prod_name: "Product Name 3",
      article_no: "Article no 3",
      replacement_qty: "Replacement Qty 3",
      date_of_purchase: "Date of Purchase 3",
      replacement_remarks: "Replacement remarks 3",
    },
    {
      prod_name: "Product Name 4",
      article_no: "Article no 4",
      replacement_qty: "Replacement Qty 4",
      date_of_purchase: "Date of Purchase 4",
      replacement_remarks: "Replacement remarks 4",
    },
    {
      prod_name: "Product Name 5",
      article_no: "Article no 5",
      replacement_qty: "Replacement Qty 5",
      date_of_purchase: "Date of Purchase 5",
      replacement_remarks: "Replacement remarks 5",
    },
  ];

  const spareProductMap = [
    {
      parent_prod_name: "Parent Product Name 1",
      article_no: "Article no for Spare 1",
      replacement_qty: "Replacement Qty 1",
      date_of_purchase: "Date of Purchase 1",
      replacement_remarks: "Replacement remarks 1",
    },
    {
      parent_prod_name: "Parent Product Name 2",
      article_no: "Article no for Spare 2",
      replacement_qty: "Replacement Qty 2",
      date_of_purchase: "Date of Purchase 2",
      replacement_remarks: "Replacement remarks 2",
    },
    {
      parent_prod_name: "Parent Product Name 3",
      article_no: "Article no for Spare 3",
      replacement_qty: "Replacement Qty 3",
      date_of_purchase: "Date of Purchase 3",
      replacement_remarks: "Replacement remarks 3",
    },
    {
      parent_prod_name: "Parent Product Name 4",
      article_no: "Article no for Spare 4",
      replacement_qty: "Replacement Qty 4",
      date_of_purchase: "Date of Purchase 4",
      replacement_remarks: "Replacement remarks 4",
    },
    {
      parent_prod_name: "Parent Product Name 5",
      article_no: "Article no for Spare 5",
      replacement_qty: "Replacement Qty 5",
      date_of_purchase: "Date of Purchase 5",
      replacement_remarks: "Replacement remarks 5",
    },
  ];

  if (changedValues[labelToKeyMap["Replacement required:"]]) {
    // Empty basic replacement fields
    emptyMultipleFields(manipulatedFieldValues, labelToKeyMap, [
      "Type of Customer",
      "Replacement Product Count",
      "Transporter name",
      "Docket no.",
      "DN no.",
      "Plant",
      "Replacement Invoice",
    ]);

    // Empty all replacement product fields for each possible product
    for (let i = 0; i < replacementProductFieldsMap.length; i++) {
      // Empty reason and type fields
      emptyMultipleFields(manipulatedFieldValues, labelToKeyMap, [
        replacementProductFieldsMap[i]["reason_fr_replacement_label"],
        replacementProductFieldsMap[i]["replacement_type_label"],
      ]);

      // Empty full product fields
      emptyMultipleFields(manipulatedFieldValues, labelToKeyMap, [
        fullProductMap[i]["prod_name"],
        fullProductMap[i]["article_no"],
        fullProductMap[i]["replacement_qty"],
        fullProductMap[i]["date_of_purchase"],
        fullProductMap[i]["replacement_remarks"],
      ]);

      // Empty spare product fields
      emptyMultipleFields(manipulatedFieldValues, labelToKeyMap, [
        spareProductMap[i]["parent_prod_name"],
        spareProductMap[i]["article_no"],
        spareProductMap[i]["replacement_qty"],
        spareProductMap[i]["date_of_purchase"],
        spareProductMap[i]["replacement_remarks"],
      ]);
    }
  }

  const productFieldsMap = [
    {
      prod_cat_label: "Product 1 category",
      prod_sub_cat_label: "Product 1 sub category",
      prod_name: "Product 1 Name",
      qty_label: "Product 1 Qty",
    },
    {
      prod_cat_label: "Product 2 category",
      prod_sub_cat_label: "Product 2 sub category",
      prod_name: "Product 2 Name",
      qty_label: "Product 2 Qty",
    },
    {
      prod_cat_label: "Product 3 category",
      prod_sub_cat_label: "Product 3 sub category",
      prod_name: "Product 3 Name",
      qty_label: "Product 3 Qty",
    },
    {
      prod_cat_label: "Product 4 category",
      prod_sub_cat_label: "Product 4 sub category",
      prod_name: "Product 4 Name",
      qty_label: "Product 4 Qty",
    },
    {
      prod_cat_label: "Product 5 category",
      prod_sub_cat_label: "Product 5 sub category",
      prod_name: "Product 5 Name",
      qty_label: "Product 5 Qty",
    },
  ];

  const productBrandFields = [
    "Product 1 Brand",
    "Product 2 Brand",
    "Product 3 Brand",
    "Product 4 Brand",
    "Product 5 Brand",
  ];

  hideEverythingOnTheFormExcept(meta, [
    "Type of service:",
    "Registered by",
    "Name",
    "Contact number",
    "Source of Request",
    "Review Remark",
    "Followup Remarks",
  ]);

  const typeOfService =
    allValues[labelToKeyMap["Type of service:"]] ||
    request_data[labelToKeyMap["Type of service:"]];
  console.log("typeOfService ", typeOfService);

  if (typeOfService) {
    modifyFieldInMeta(meta, labelToKeyMap["Source of purchase:"], {
      hide: false,
    });
  }

  const optionsToShowProducts = [
    "Installation Guidance",
    "Product Complaint",
    "Missing Material in new box",
    "Damaged Material in new box",
    "Misc",
  ];

  const optionsFrMatIspection = [
    "Damage Material Inspection",
    "Fresh Material Inspection",
  ];

  const optionForDealerOEM = [
    "Dealer Display Inspection",
    "Dealer /OEM Display Inspection",
  ];
  const optionFrAPC = ["APC Display Inspection"];
  const optionForTraining = ["Training"];
  const optionForExhibition = ["Exhibition"];
  const optionForWarehouseVisit = ["Warehouse Visit"];

  const typeOfServiceOptions = getObjByKeyFrmArray(
    meta,
    "key",
    labelToKeyMap["Type of service:"]
  ).options;

  const optionsToShowProductsKeys = getValuesByLabels(
    typeOfServiceOptions,
    optionsToShowProducts
  ); //1
  const optionsFrMatIspectionKeys = getValuesByLabels(
    typeOfServiceOptions,
    optionsFrMatIspection
  ); //2
  const optionsForDealerOEMKeys = getValuesByLabels(
    typeOfServiceOptions,
    optionForDealerOEM
  ); //3
  const optionsToShowAPCKeys = getValuesByLabels(
    typeOfServiceOptions,
    optionFrAPC
  ); //4
  const optionsForTrainingKeys = getValuesByLabels(
    typeOfServiceOptions,
    optionForTraining
  ); //5
  const optionsForExhibitionKeys = getValuesByLabels(
    typeOfServiceOptions,
    optionForExhibition
  ); //6
  const optionsForWarehouseVisitKeys = getValuesByLabels(
    typeOfServiceOptions,
    optionForWarehouseVisit
  ); //7

  if (typeOfService && optionsToShowProductsKeys.includes(typeOfService)) {
    //1
    modifyFieldInMeta(meta, labelToKeyMap["Number of Products"], {
      hide: false,
    });
  } else if (
    typeOfService &&
    optionsFrMatIspectionKeys.includes(typeOfService)
  ) {
    //2
    showMultipleFields(meta, labelToKeyMap, [
      "Dealer Name",
      "Dealer Code",
      "Salesperson",
      "Closure remarks:",
    ]);
  } else if (typeOfService && optionsForDealerOEMKeys.includes(typeOfService)) {
    //3
    showMultipleFields(meta, labelToKeyMap, [
      "Dealer/OEM Code",
      "Dealer/OEM Name",
      "Salesperson",
      "Closure remarks:",
    ]);
  } else if (typeOfService && optionsToShowAPCKeys.includes(typeOfService)) {
    //4
    showMultipleFields(meta, labelToKeyMap, ["APC Name", "Closure remarks:"]);
  } else if (typeOfService && optionsForTrainingKeys.includes(typeOfService)) {
    //5
    showMultipleFields(meta, labelToKeyMap, ["Purpose", "Closure remarks:"]);
  } else if (
    typeOfService &&
    optionsForExhibitionKeys.includes(typeOfService)
  ) {
    //6
    showMultipleFields(meta, labelToKeyMap, ["Event Name", "Closure remarks:"]);
  } else if (
    typeOfService &&
    optionsForWarehouseVisitKeys.includes(typeOfService)
  ) {
    //7
    showMultipleFields(meta, labelToKeyMap, ["Location", "Closure remarks:"]);
  }

  const numberOfProductCountOptionsMap = getObjByKeyFrmArray(
    meta,
    "key",
    labelToKeyMap["Number of Products"]
  ).options;

  const numberOfProductsToShow =
    getNumberOfProductsToShow(
      allValues,
      numberOfProductCountOptionsMap,
      labelToKeyMap,
      request_data
    ) || request_data[labelToKeyMap["Number of Products"]];

  // Iterate over the productFieldsMap array
  for (let i = 0; i < productFieldsMap.length; i++) {
    let singleProduct = productFieldsMap[i]; // Get the current product field

    // Check if the current index is within the number of products to show
    if (i < parseInt(numberOfProductsToShow)) {
      // Modify the meta data to show the product Brand field and set its options
      modifyFieldInMeta(meta, labelToKeyMap[productBrandFields[i]], {
        hide: false,
        required: true,
      });

      const prodBrandFieldValue =
        allValues[labelToKeyMap[productBrandFields[i]]] ||
        request_data[labelToKeyMap[productBrandFields[i]]];

      if (prodBrandFieldValue) {
        // Modify the meta data to show the product Brand field and set its options
        const brandOptions = getOptionsByFieldKey(
          meta,
          labelToKeyMap[productBrandFields[i]]
        );

        const selectedBrandLabel = getLabelByValue(
          brandOptions,
          prodBrandFieldValue
        );

        const categoryLabels = getCategoryLabels(
          productOptionsMap,
          selectedBrandLabel
        );

        const categoryOptions = getOptionsByFieldKey(
          meta,
          labelToKeyMap[productFieldsMap[i]["prod_cat_label"]]
        );

        const filteredCatOptions = filteredOptions(
          categoryOptions,
          categoryLabels
        );

        modifyFieldInMeta(
          meta,
          labelToKeyMap[productFieldsMap[i]["prod_cat_label"]],
          {
            hide: false,
            required: true,
            options: filteredCatOptions,
          }
        );
      }

      const newProductBrandField =
        changedValues[labelToKeyMap[productBrandFields[i]]];

      if (newProductBrandField) {
        //current value of brand name
        //clear fields when brand is changed
        manipulatedFieldValues[
          labelToKeyMap[productFieldsMap[i]["prod_cat_label"]]
        ] = "";
        manipulatedFieldValues[
          labelToKeyMap[productFieldsMap[i]["prod_sub_cat_label"]]
        ] = "";
        manipulatedFieldValues[
          labelToKeyMap[productFieldsMap[i]["prod_name"]]
        ] = "";
        manipulatedFieldValues[
          labelToKeyMap[productFieldsMap[i]["qty_label"]]
        ] = "";
        // Modify the meta data to show the product Brand field and set its options
        const brandOptions = getOptionsByFieldKey(
          meta,
          labelToKeyMap[productBrandFields[i]]
        );

        const selectedBrandLabel = getLabelByValue(
          brandOptions,
          newProductBrandField
        );

        const categoryLabels = getCategoryLabels(
          productOptionsMap,
          selectedBrandLabel
        );

        const categoryOptions = getOptionsByFieldKey(
          meta,
          labelToKeyMap[productFieldsMap[i]["prod_cat_label"]]
        );

        const filteredCatOptions = filteredOptions(
          categoryOptions,
          categoryLabels
        );

        modifyFieldInMeta(
          meta,
          labelToKeyMap[productFieldsMap[i]["prod_cat_label"]],
          {
            hide: false,
            required: true,
            options: filteredCatOptions,
          }
        );
      }

      let prodCatField =
        allValues[labelToKeyMap[productFieldsMap[i]["prod_cat_label"]]] ||
        request_data[labelToKeyMap[productFieldsMap[i]["prod_cat_label"]]];

      let newProdCatField =
        changedValues[labelToKeyMap[productFieldsMap[i]["prod_cat_label"]]];

      console.log("log prodCatField", prodCatField);
      //when cat field has stored values
      if (prodCatField) {
        //product category is filled from form
        showMultipleFields(meta, labelToKeyMap, [
          productFieldsMap[i]["prod_cat_label"],
          productFieldsMap[i]["prod_sub_cat_label"],
        ]);

        const categoryOptions = getOptionsByFieldKey(
          meta,
          labelToKeyMap[productFieldsMap[i]["prod_cat_label"]]
        );
        const brandOptions = getOptionsByFieldKey(
          meta,
          labelToKeyMap[productBrandFields[i]]
        );

        const selectedCatLabel = getLabelByValue(categoryOptions, prodCatField);
        console.log("log selectedCatLabel", selectedCatLabel);
        console.log(
          "log brand field",
          allValues[labelToKeyMap[productBrandFields[i]]]
        );
        const selectedBrandLabel = getLabelByValue(
          brandOptions,
          allValues[labelToKeyMap[productBrandFields[i]]] ||
            request_data[labelToKeyMap[productBrandFields[i]]]
        );
        console.log("log selectedBrandLabel", selectedBrandLabel);

        const subCategoryLabel = getSubCategoriesLabels(
          productOptionsMap,
          selectedBrandLabel,
          selectedCatLabel
        );

        console.log("log subCategoryLabel", subCategoryLabel);

        const subCategoryOptions = getOptionsByFieldKey(
          meta,
          labelToKeyMap[productFieldsMap[i]["prod_sub_cat_label"]]
        );

        console.log("log subCategoryOptions", subCategoryOptions);

        const filteredSubCatOptions = filteredOptions(
          subCategoryOptions,
          subCategoryLabel
        );

        console.log("log filteredSubCatOptions", filteredSubCatOptions);

        if (filteredSubCatOptions.length == 0) {
          showMultipleFields(meta, labelToKeyMap, [
            productFieldsMap[i]["prod_cat_label"],
            productFieldsMap[i]["qty_label"],
            "Warranty Details",
            "In Warranty",
            "Invoice Available",
            // "Source of purchase:",
            "Replacement required:",
            "Closure remarks:",
            "Invoice:",
          ]);
          modifyFieldInMeta(
            meta,
            labelToKeyMap[productFieldsMap[i]["prod_sub_cat_label"]],
            {
              hide: true,
            }
          );
        } else if (filteredSubCatOptions.length > 0) {
          modifyFieldInMeta(
            meta,
            labelToKeyMap[productFieldsMap[i]["prod_sub_cat_label"]],
            {
              hide: false,
              required: true,
              options: filteredSubCatOptions,
            }
          );
        }
        modifyFieldInMeta(
          meta,
          labelToKeyMap[productFieldsMap[i]["prod_cat_label"]],
          {
            required: true,
          }
        );
      }

      //when cat field is changed
      if (newProdCatField) {
        //current value of category just changed
        //clear fields because of change in prod category
        manipulatedFieldValues[
          labelToKeyMap[productFieldsMap[i]["prod_sub_cat_label"]]
        ] = "";
        manipulatedFieldValues[
          labelToKeyMap[productFieldsMap[i]["prod_name"]]
        ] = "";
        manipulatedFieldValues[
          labelToKeyMap[productFieldsMap[i]["qty_label"]]
        ] = "";

        const categoryOptions = getOptionsByFieldKey(
          meta,
          labelToKeyMap[productFieldsMap[i]["prod_cat_label"]]
        );
        const brandOptions = getOptionsByFieldKey(
          meta,
          labelToKeyMap[productBrandFields[i]]
        );

        const selectedCatLabel = getLabelByValue(
          categoryOptions,
          newProdCatField
        );
        const selectedBrandLabel = getLabelByValue(
          brandOptions,
          allValues[labelToKeyMap[productBrandFields[i]]]
        );

        const subCategoryLabel = getSubCategoriesLabels(
          productOptionsMap,
          selectedBrandLabel,
          selectedCatLabel
        );

        const subCategoryOptions = getOptionsByFieldKey(
          meta,
          labelToKeyMap[productFieldsMap[i]["prod_sub_cat_label"]]
        );

        const filteredSubCatOptions = filteredOptions(
          subCategoryOptions,
          subCategoryLabel
        );

        if (filteredSubCatOptions.length == 0) {
          showMultipleFields(meta, labelToKeyMap, [
            productFieldsMap[i]["prod_cat_label"],
            productFieldsMap[i]["qty_label"],
            "Warranty Details",
            "In Warranty",
            "Invoice Available",
            // "Source of purchase:",
            "Replacement required:",
            "Closure remarks:",
            "Invoice:",
          ]);
          modifyFieldInMeta(
            meta,
            labelToKeyMap[productFieldsMap[i]["prod_sub_cat_label"]],
            {
              hide: true,
            }
          );
        }
        if (filteredSubCatOptions.length > 0) {
          modifyFieldInMeta(
            meta,
            labelToKeyMap[productFieldsMap[i]["prod_sub_cat_label"]],
            {
              hide: false,
              required: true,
              options: filteredSubCatOptions,
            }
          );
        }
        // showMultipleFields(meta, labelToKeyMap, []);
      }

      const prodSubCatField =
        allValues[labelToKeyMap[productFieldsMap[i]["prod_sub_cat_label"]]] ||
        request_data[labelToKeyMap[productFieldsMap[i]["prod_sub_cat_label"]]];

      //when cat field is stored
      if (prodSubCatField) {
        //product sub cat is filled from form
        showMultipleFields(meta, labelToKeyMap, [
          productFieldsMap[i]["prod_cat_label"],
          productFieldsMap[i]["prod_sub_cat_label"],
          productFieldsMap[i]["prod_name"],
        ]);
        modifyFieldInMeta(
          meta,
          labelToKeyMap[productFieldsMap[i]["prod_cat_label"]],
          {
            required: true,
          }
        );

        const subCategoryOptions = getOptionsByFieldKey(
          meta,
          labelToKeyMap[productFieldsMap[i]["prod_sub_cat_label"]]
        );

        const prodNameOptions = getOptionsByFieldKey(
          meta,
          labelToKeyMap[productFieldsMap[i]["prod_name"]]
        );

        const selectedSubCatLabel = getLabelByValue(
          subCategoryOptions,
          prodSubCatField
        );

        const prodNamesLabels = getProdNameOptions(
          productOptionsMap,
          selectedSubCatLabel
        );

        const filteredProdNameOptions = filteredOptions(
          prodNameOptions,
          prodNamesLabels
        );

        modifyFieldInMeta(
          meta,
          labelToKeyMap[productFieldsMap[i]["prod_sub_cat_label"]],
          {
            required: true,
          }
        );

        if (filteredProdNameOptions.length == 0) {
          showMultipleFields(meta, labelToKeyMap, [
            productFieldsMap[i]["qty_label"],
            "Warranty Details",
            "In Warranty",
            "Invoice Available",
            // "Source of purchase:",
            "Replacement required:",
            "Closure remarks:",
            "Invoice:",
          ]);
          modifyFieldInMeta(
            meta,
            labelToKeyMap[productFieldsMap[i]["prod_name"]],
            {
              hide: true,
            }
          );
        } else if (filteredProdNameOptions.length > 0) {
          modifyFieldInMeta(
            meta,
            labelToKeyMap[productFieldsMap[i]["prod_name"]],
            {
              hide: false,
              required: true,
              options: filteredProdNameOptions,
            }
          );
        }
      }

      const newSubCatField =
        changedValues[labelToKeyMap[productFieldsMap[i]["prod_sub_cat_label"]]];

      if (newSubCatField) {
        //current value of sub category just changed
        const subCategoryOptions = getOptionsByFieldKey(
          meta,
          labelToKeyMap[productFieldsMap[i]["prod_sub_cat_label"]]
        );

        const prodNameOptions = getOptionsByFieldKey(
          meta,
          labelToKeyMap[productFieldsMap[i]["prod_name"]]
        );

        const selectedSubCatLabel = getLabelByValue(
          subCategoryOptions,
          newSubCatField
        );

        const prodNamesLabels = getProdNameOptions(
          productOptionsMap,
          selectedSubCatLabel
        );

        const filteredProdNameOptions = filteredOptions(
          prodNameOptions,
          prodNamesLabels
        );

        console.log(
          "log new subcatfilteredProdNameOptions: ",
          filteredProdNameOptions
        );
        if (filteredProdNameOptions.length == 0) {
          console.log("running 0 names if: ");
          showMultipleFields(meta, labelToKeyMap, [
            "Warranty Details",
            "In Warranty",
            "Invoice Available",
            // "Source of purchase:",
            "Replacement required:",
            "Closure remarks:",
            "Invoice:",
          ]);
          modifyFieldInMeta(
            meta,
            labelToKeyMap[productFieldsMap[i]["prod_name"]],
            {
              hide: true,
            }
          );
        }
        if (filteredProdNameOptions.length > 0) {
          modifyFieldInMeta(
            meta,
            labelToKeyMap[productFieldsMap[i]["prod_name"]],
            {
              hide: false,
              required: true,
              options: filteredProdNameOptions,
            }
          );
        }
      }

      let prodNameLabelField =
        allValues[labelToKeyMap[productFieldsMap[i]["prod_name"]]] ||
        request_data[labelToKeyMap[productFieldsMap[i]["prod_name"]]];
      if (prodNameLabelField) {
        //product name is filled from form
        showMultipleFields(meta, labelToKeyMap, [
          productFieldsMap[i]["prod_cat_label"],
          productFieldsMap[i]["prod_sub_cat_label"],
          productFieldsMap[i]["prod_name"],
          productFieldsMap[i]["qty_label"],
          "Warranty Details",
          "In Warranty",
          "Invoice Available",
          // "Source of purchase:",
          "Replacement required:",
          "Closure remarks:",
          "Invoice:",
        ]);
        modifyFieldInMeta(
          meta,
          labelToKeyMap[productFieldsMap[i]["prod_name"]],
          {
            required: true,
          }
        );
        modifyFieldInMeta(
          meta,
          labelToKeyMap[productFieldsMap[i]["prod_sub_cat_label"]],
          {
            required: true,
          }
        );
      }
    }
  }

  const replacementRequiredValue =
    allValues[labelToKeyMap["Replacement required:"]] ||
    request_data[labelToKeyMap["Replacement required:"]];

  console.log("replacementRequiredValue==> ", replacementRequiredValue);

  if (replacementRequiredValue == "41f85919-4103-4cfe-99aa-70ce6dd6a381") {
    // show replacement fields if replacement required is yes
    showMultipleFields(meta, labelToKeyMap, [
      "Type of Customer",
      "Replacement Product Count",
      "Transporter name",
      "Docket no.",
      "DN no.",
      "Plant",
      "Replacement Invoice",
    ]);
  }

  const replacementProductCountOptionsMap = getObjByKeyFrmArray(
    meta,
    "key",
    labelToKeyMap["Replacement Product Count"]
  ).options;

  const numberOfReplacementProductsToShow =
    getNumberOfReplacementProductsToShow(
      allValues,
      replacementProductCountOptionsMap,
      labelToKeyMap,
      request_data
    ) || request_data[labelToKeyMap["Replacement Product Count"]];

  console.log(
    "numberOfReplacementProductsToShow",
    numberOfReplacementProductsToShow
  );

  for (let i = 0; i < replacementProductFieldsMap.length; i++) {
    console.log(
      "log replacement loop",
      i,
      "numberOfReplacementProductsToShow",
      numberOfReplacementProductsToShow
    );
    if (i < parseInt(numberOfReplacementProductsToShow)) {
      showMultipleFields(meta, labelToKeyMap, [
        replacementProductFieldsMap[i]["reason_fr_replacement_label"],
        replacementProductFieldsMap[i]["replacement_type_label"],
      ]);

      // Get the replacement type value for this index
      const replacementTypeKey =
        labelToKeyMap[replacementProductFieldsMap[i]["replacement_type_label"]];
      const replacementTypeValue =
        allValues[replacementTypeKey] || request_data[replacementTypeKey];

      // Hide both full product and spare fields first
      hideMultipleFields(meta, labelToKeyMap, [
        fullProductMap[i]["prod_name"],
        fullProductMap[i]["article_no"],
        fullProductMap[i]["replacement_qty"],
        fullProductMap[i]["date_of_purchase"],
        fullProductMap[i]["replacement_remarks"],
        spareProductMap[i]["parent_prod_name"],
        spareProductMap[i]["article_no"],
        spareProductMap[i]["replacement_qty"],
        spareProductMap[i]["date_of_purchase"],
        spareProductMap[i]["replacement_remarks"],
      ]);

      // If replacement type is "Full Product" (using the UUID from your system)
      if (
        !changedValues[labelToKeyMap["Replacement required:"]] &&
        replacementTypeValue === "cc346fd2-a5fa-4c92-8ee6-d780e61bd6d2"
      ) {
        showMultipleFields(meta, labelToKeyMap, [
          fullProductMap[i]["prod_name"],
          fullProductMap[i]["article_no"],
          fullProductMap[i]["replacement_qty"],
          fullProductMap[i]["date_of_purchase"],
          fullProductMap[i]["replacement_remarks"],
        ]);

        //Setting Article Options
        modifyFieldInMeta(
          meta,
          labelToKeyMap[fullProductMap[i]["article_no"]],
          {
            options: articleProductOptions.articleNo,
          }
        );

        // Make product name disabled as it will be auto-filled based on article number
        modifyFieldInMeta(meta, labelToKeyMap[fullProductMap[i]["prod_name"]], {
          disabled: true,
        });

        // Auto-fill product name based on article number if it exists
        const articleKey = labelToKeyMap[fullProductMap[i]["article_no"]];
        console.log("log articleKey", articleKey);
        const articleValue = allValues[articleKey] || request_data[articleKey];
        console.log("log article value", articleValue);

        if (articleValue) {
          console.log(
            "log full prod index",
            i,
            "prod key",
            fullProductMap[i]["prod_name"]
          );
          manipulatedFieldValues[
            labelToKeyMap[fullProductMap[i]["prod_name"]]
          ] = getProductByArticle(articleValue, articleProductOptions) || "";
        }
      }
      // If replacement type is "Spare" (using the UUID from your system)
      else if (
        !changedValues[labelToKeyMap["Replacement required:"]] &&
        replacementTypeValue === "e9dad7cc-1395-46ee-9f2f-7922ce5bb73f"
      ) {
        showMultipleFields(meta, labelToKeyMap, [
          spareProductMap[i]["parent_prod_name"],
          spareProductMap[i]["article_no"],
          spareProductMap[i]["replacement_qty"],
          spareProductMap[i]["date_of_purchase"],
          spareProductMap[i]["replacement_remarks"],
        ]);
      }
    }
  }

  console.log("final meta", JSON.stringify(meta));

  responseStatus = true;
  responseMessage = "success";
  const response = {
    status: responseStatus,
    message: responseMessage,
    data: { meta, allValues, changedValues, manipulatedFieldValues },
  };
  return response;
};

exports.handler = handler;
