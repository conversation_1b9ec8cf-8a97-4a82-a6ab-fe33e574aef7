import { Router, Request, Response } from 'express';
import { authenticate<PERSON><PERSON><PERSON><PERSON> } from '../middleware/auth';

const router = Router();

/**
 * @swagger
 * /test-credentials:
 *   get:
 *     summary: Test API credentials
 *     description: Validates the API key and returns success if valid
 *     security:
 *       - ApiKeyAuth: []
 *     responses:
 *       200:
 *         description: Credentials are valid
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: API credentials are valid
 *                 timestamp:
 *                   type: string
 *                   example: 2023-07-20T12:34:56.789Z
 *       401:
 *         description: Invalid API key
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "Unauthorized: Invalid API key"
 *                 timestamp:
 *                   type: string
 *                   example: 2023-07-20T12:34:56.789Z
 */
router.get('/test-credentials', authenticate<PERSON><PERSON><PERSON><PERSON>, (req: Request, res: Response) => {
  res.status(200).json({
    status: true,
    message: 'API credentials are valid',
    timestamp: new Date().toISOString(),
  });
});

export default router;
