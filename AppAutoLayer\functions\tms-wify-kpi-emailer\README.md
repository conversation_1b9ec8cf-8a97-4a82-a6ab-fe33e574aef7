# TMS Wify KPI Emailer

A Lambda function that queries vertical KPI data from the database and sends comprehensive reports via email using SMTP credentials from environment variables.

## Features

- Queries vertical-wise KPI metrics including orders, supply, and deployment statistics
- Generates professional HTML email templates with data tables and summary analytics
- Sends emails using configurable SMTP settings
- Modular design with separate email service and template modules
- Comprehensive error handling and logging
- Includes contextual analysis and KPI summaries

## Environment Variables

### Database Configuration
```
DB_HOST=your-database-host
DB_NAME=your-database-name
DB_USER=your-database-user
DB_PASSWORD=your-database-password
DB_PORT=5432
```

### SMTP Configuration
```
EMAIL_SMTP_HOST=smtp.gmail.com
EMAIL_SMTP_PORT=465
EMAIL_AUTH_USER=<EMAIL>
EMAIL_AUTH_PASS=your-email-password
EMAIL_FROM=<EMAIL>
```

### Email Recipients
```
EMAIL_RECIPIENTS=<EMAIL>,<EMAIL>
EMAIL_CC=<EMAIL>,<EMAIL> (optional)
EMAIL_BCC=<EMAIL>,<EMAIL> (optional)
```

## File Structure

```
tms-wify-kpi-emailer/
├── index.js              # Main Lambda handler
├── emailService.js       # Email sending functionality
├── htmlTemplate.js       # HTML template generation
├── package.json          # Dependencies
└── README.md             # This file
```

## KPI Metrics Included

The function queries and reports the following metrics for each vertical:

### Order Metrics
- **All time orders**: Total orders ever created for the vertical
- **Active orders**: Currently open orders
- **New orders**: Orders created in the last 24 hours
- **Orders assigned**: New orders that got assigned to technicians
- **Orders closed**: Orders completed in the last 24 hours

### Supply Metrics
- **Active supply**: Total active technicians in the vertical
- **New supply**: Technicians onboarded in the last 24 hours
- **Deactivated supply**: Inactive technicians
- **Supply deployed**: Technicians assigned tasks today
- **Supply started > 0 tasks**: Technicians who started at least one task
- **Supply closed > 0 tasks**: Technicians who completed at least one task

## Usage

### Local Testing
```bash
# Install dependencies
npm install

# Set environment variables in .env file
# Run the function (if you have a local test setup)
```

### Lambda Deployment
Deploy this function to AWS Lambda and configure the environment variables in the Lambda console.

## Modules

### emailService.js
- `sendEmail(emailData)` - Sends email with provided configuration
- `getEmailCredentials()` - Retrieves SMTP credentials from environment

### htmlTemplate.js
- `generateDataTableHTML(data, options)` - Creates HTML table from query results
- `generateEmptyDataHTML(title, description)` - Creates HTML for empty data scenarios
- `generateDataSummary(data)` - Creates contextual analysis summary
- `escapeHtml(text)` - Escapes HTML special characters
- `getISTTimestamp()` - Gets current timestamp in IST format

## Email Template Features

- **Responsive Design**: Works on desktop and mobile devices
- **Professional Styling**: Clean, modern appearance with gradient headers
- **Data Summary**: Contextual analysis with key insights and percentages
- **Interactive Tables**: Hover effects and alternating row colors
- **Comprehensive Metrics**: All KPI data in an easy-to-read format
- **Timestamp**: Generation time in IST timezone
- **Record Count**: Total number of verticals included in the report

## Error Handling

- Database connection errors are caught and logged
- Email sending failures are handled gracefully
- Missing environment variables are handled with defaults
- Empty data scenarios are handled with appropriate messaging

## Logging

All functions use the established logging pattern:
```
ClassName::functionName:: Log message
```

This helps with debugging and monitoring in production environments.
