/**
 * Get current time in IST
 * @returns {string} - Formatted IST timestamp
 */
const getISTTimestamp = () => {
  const now = new Date();
  return now.toLocaleString("en-IN", {
    timeZone: "Asia/Kolkata",
    year: "numeric",
    month: "2-digit",
    day: "2-digit",
    hour: "2-digit",
    minute: "2-digit",
    second: "2-digit",
    hour12: true,
  });
};

/**
 * Generate English summary based on data analysis
 * @param {Array} data - Array of objects representing query results
 * @returns {string} - English summary text
 */
const generateDataSummary = (data) => {
  if (!data || !Array.isArray(data) || data.length === 0) {
    return "No data available for analysis.";
  }

  // Analyze the data
  const totalVerticals = data.length;
  const verticalsWithZeroHubs = data.filter(
    (row) => parseInt(row["Tot. service hubs"]) === 0
  ).length;
  const totalOrdersWithoutHub = data.reduce(
    (sum, row) => sum + parseInt(row["Orders without a service hub"] || 0),
    0
  );
  const totalServiceHubs = data.reduce(
    (sum, row) => sum + parseInt(row["Tot. service hubs"] || 0),
    0
  );
  const totalTechnicians = data.reduce(
    (sum, row) => sum + parseInt(row["Tot. Technicians"] || 0),
    0
  );
  const totalSkills = data.reduce(
    (sum, row) => sum + parseInt(row["Tot. Skills"] || 0),
    0
  );
  const newHubsCreated = data.reduce(
    (sum, row) => sum + parseInt(row["New hubs created"] || 0),
    0
  );

  // Generate summary based on analysis
  let summary = "";

  if (verticalsWithZeroHubs > 0) {
    if (verticalsWithZeroHubs === 1) {
      summary += `With one vertical having zero service hubs, `;
    } else {
      summary += `With ${verticalsWithZeroHubs} verticals having zero service hubs across multiple verticals, `;
    }

    if (totalOrdersWithoutHub > 0) {
      summary += `it's inevitable that ${totalOrdersWithoutHub} orders will remain unfulfilled and without proper allocation. This is a critical issue that needs urgent resolution.`;
    } else {
      summary += `there's a risk that future orders may remain unfulfilled without proper allocation. This requires immediate attention.`;
    }
  } else if (totalOrdersWithoutHub > 0) {
    summary += `Currently, ${totalOrdersWithoutHub} orders are without service hub allocation across ${totalVerticals} verticals. `;
    summary += `Despite having ${totalServiceHubs} service hubs available, these unallocated orders indicate potential capacity or routing issues that need investigation.`;
  } else {
    summary += `All ${totalVerticals} verticals are operational with ${totalServiceHubs} service hubs and ${totalTechnicians} technicians. `;
    if (newHubsCreated > 0) {
      summary += `${newHubsCreated} new hubs were created today, showing positive growth. `;
    }
    summary += `The system appears to be functioning well with no unallocated orders currently pending.`;
  }

  // Add additional insights
  if (totalSkills > 0) {
    const avgSkillsPerVertical = Math.round(totalSkills / totalVerticals);
    summary += ` Platform has ${totalSkills} skills across all verticals (average ${avgSkillsPerVertical} skills per vertical).`;
  }

  return summary;
};

/**
 * Generate HTML table from query results
 * @param {Array} data - Array of objects representing query results
 * @param {Object} options - Template options
 * @param {string} [options.title] - Email title
 * @param {string} [options.description] - Email description
 * @returns {string} - HTML content
 */
const generateDataTableHTML = (data, options = {}) => {
  const {
    title = "TMS Data Report",
    description = "Query results from TMS database",
  } = options;

  if (!data || !Array.isArray(data) || data.length === 0) {
    return generateEmptyDataHTML(title, description);
  }

  // Generate summary
  const dataSummary = generateDataSummary(data);

  // Get column headers from the first row
  const headers = Object.keys(data[0]);

  // Generate table headers
  const headerRow = headers
    .map(
      (header) =>
        `<th style="padding: 12px; background-color: #f8f9fa; border: 1px solid #dee2e6; text-align: left; font-weight: bold;">${escapeHtml(
          header
        )}</th>`
    )
    .join("");

  // Function to generate the style for each cell based on column index and value
  const getCellStyle = (columnIndex, value) => {
    const isNumber = !isNaN(value) && value !== "" && value !== null;

    // Modify the logic based on columnIndex (for example, apply red color to the first column when value is 0)
    if (isNumber && value === "0" && columnIndex != 4) {
      return "padding: 12px; border: 1px solid #dee2e6; color: #dc3545; font-weight: bold;";
    } else if (isNumber && columnIndex === 4 && value > "0") {
      return "padding: 12px; border: 1px solid #dee2e6; color: #dc3545; font-weight: bold;";
    }

    // Default style for other cells
    return "padding: 12px; border: 1px solid #dee2e6;";
  };

  // Function to generate table rows
  const generateTableRows = (data, headers) => {
    return data
      .map((row) => {
        const cells = headers
          .map((header, columnIndex) => {
            const value = row[header];
            const displayValue =
              value !== null && value !== undefined ? value : "";
            const cellStyle = getCellStyle(columnIndex, displayValue);
            return `<td style="${cellStyle}">${escapeHtml(
              String(displayValue)
            )}</td>`;
          })
          .join("");
        return `<tr>${cells}</tr>`;
      })
      .join("");
  };

  const dataRows = generateTableRows(data, headers);

  return `
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>${escapeHtml(title)}</title>
        <style>
            body {
                font-family: Arial, sans-serif;
                line-height: 1.6;
                color: #333;
                max-width: 1200px;
                margin: 0 auto;
                padding: 20px;
                background-color: #f8f9fa;
            }
            .container {
                background-color: white;
                padding: 30px;
                border-radius: 8px;
                box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            }
            .header {
                text-align: center;
                margin-bottom: 30px;
                padding-bottom: 20px;
                border-bottom: 2px solid #007bff;
            }
            .header h1 {
                color: #007bff;
                margin: 0;
                font-size: 28px;
            }
            .description {
                color: #6c757d;
                margin: 10px 0 0 0;
                font-size: 16px;
            }
            .data-table {
                width: 100%;
                border-collapse: collapse;
                margin: 20px 0;
                font-size: 14px;
            }
            .data-table th {
                background-color: #007bff;
                color: black;
                font-weight: bold;
                text-transform: uppercase;
                font-size: 12px;
                letter-spacing: 0.5px;
            }
            .data-table tr:nth-child(even) {
                background-color: #f8f9fa;
            }
            .data-table tr:hover {
                background-color: #e9ecef;
            }
            .footer {
                margin-top: 30px;
                padding-top: 20px;
                border-top: 1px solid #dee2e6;
                text-align: center;
                color: #6c757d;
                font-size: 12px;
            }
            .stats {
                background-color: #e7f3ff;
                padding: 15px;
                border-radius: 5px;
                margin: 20px 0;
                text-align: center;
            }
            .summary {
                background-color: #f8f9fa;
                border-left: 4px solid #007bff;
                padding: 20px;
                margin: 20px 0;
                border-radius: 5px;
            }
            .summary h3 {
                color: #007bff;
                margin-top: 0;
                margin-bottom: 15px;
                font-size: 18px;
            }
            .summary p {
                margin: 0;
                line-height: 1.6;
                font-size: 15px;
                color: #495057;
            }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>${escapeHtml(title)}</h1>
                <p class="description">${escapeHtml(description)}</p>
            </div>
            
            <div class="stats">
                <strong>Total Records: ${data.length}</strong> |
                <strong>Generated: ${getISTTimestamp()} IST</strong>
            </div>

            <div class="summary">
                <h3>Executive Summary</h3>
                <p>${escapeHtml(dataSummary)}</p>
            </div>

            <table class="data-table">
                <thead>
                    <tr>${headerRow}</tr>
                </thead>
                <tbody>
                    ${dataRows}
                </tbody>
            </table>
            
            <div class="footer">
                <p>This report was automatically generated by TMS Wify Emailer</p>
                <p>Generated on: ${getISTTimestamp()} IST</p>
            </div>
        </div>
    </body>
    </html>
  `;
};

/**
 * Generate HTML for empty data scenario
 * @param {string} title - Email title
 * @param {string} description - Email description
 * @returns {string} - HTML content
 */
const generateEmptyDataHTML = (title, description) => {
  return `
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>${escapeHtml(title)}</title>
        <style>
            body {
                font-family: Arial, sans-serif;
                line-height: 1.6;
                color: #333;
                max-width: 800px;
                margin: 0 auto;
                padding: 20px;
                background-color: #f8f9fa;
            }
            .container {
                background-color: white;
                padding: 30px;
                border-radius: 8px;
                box-shadow: 0 2px 10px rgba(0,0,0,0.1);
                text-align: center;
            }
            .header h1 {
                color: #007bff;
                margin: 0 0 20px 0;
            }
            .no-data {
                background-color: #fff3cd;
                border: 1px solid #ffeaa7;
                color: #856404;
                padding: 20px;
                border-radius: 5px;
                margin: 20px 0;
            }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>${escapeHtml(title)}</h1>
                <p>${escapeHtml(description)}</p>
            </div>
            
            <div class="no-data">
                <h3>No Data Available</h3>
                <p>The query returned no results for the specified criteria.</p>
                <p>Generated on: ${getISTTimestamp()} IST</p>
            </div>
        </div>
    </body>
    </html>
  `;
};

/**
 * Escape HTML special characters to prevent XSS
 * @param {string} text - Text to escape
 * @returns {string} - Escaped text
 */
const escapeHtml = (text) => {
  const map = {
    "&": "&amp;",
    "<": "&lt;",
    ">": "&gt;",
    '"': "&quot;",
    "'": "&#039;",
  };
  return text.replace(/[&<>"']/g, (m) => map[m]);
};

module.exports = {
  generateDataTableHTML,
  generateEmptyDataHTML,
  generateDataSummary,
  escapeHtml,
  getISTTimestamp,
};
