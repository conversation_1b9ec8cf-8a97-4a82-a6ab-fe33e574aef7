#!/usr/bin/env node

require("dotenv").config();
const { execSync } = require("child_process");
const fs = require("fs");
const path = require("path");
const zl = require("zip-lib");
const {
  LambdaClient,
  UpdateFunctionCodeCommand,
  PublishLayerVersionCommand,
  GetFunctionCommand,
  UpdateFunctionConfigurationCommand,
} = require("@aws-sdk/client-lambda");

// Get lambda function name from command line arguments
const args = require("minimist")(process.argv.slice(2));
if (args.e == undefined && args.e != "prod" && args.e != "dev") {
  console.error("Please provide an environment");
  console.error("Usage: node deploy.js -e <environment prod or dev>");
  process.exit(1);
}
if (args.l == undefined) {
  console.error("Please provide a lambda function name");
  console.error("Usage: node deploy-dev -- -l <lambda-function-name>");
  process.exit(1);
}
console.log("Deploying to Environment:", args.e);
console.log("Lambda name:", args.l);

const lambdaName = args.l;
const functionsDir = path.join(__dirname, "functions");
const lambdaDir = path.join(functionsDir, lambdaName);

// Check if the lambda function directory exists
if (!fs.existsSync(lambdaDir)) {
  console.error(
    `Error: Lambda function '${lambdaName}' not found in the functions directory.`
  );
  console.error(`Please make sure the function exists in: ${functionsDir}`);
  process.exit(1);
}

// Initialize AWS Lambda client
const lambdaClient = new LambdaClient({
  credentials: {
    accessKeyId: process.env.ACCESS_KEY_ID || process.env.AWS_ACCESS_KEY_ID,
    secretAccessKey:
      process.env.SECRET_ACCESS_KEY || process.env.AWS_SECRET_ACCESS_KEY,
  },
  region: process.env.REGION || process.env.AWS_REGION || "ap-south-1",
});

// Helper function to log with timestamp and emoji
const logWithTimestamp = (message, emoji = "") => {
  const timestamp = new Date().toLocaleString();
  console.log(`${emoji} ${message} [${timestamp}]`);
};

// Sleep function for delays
const sleep = (ms) => new Promise((resolve) => setTimeout(resolve, ms));

// Main deployment function
const deployLambda = async (retryCount = 0, maxRetries = 3) => {
  try {
    // Create temporary directories outside of the lambda function directory
    const tempDir = path.join(__dirname, "temp", lambdaName);
    const layerDir = path.join(tempDir, "layer");
    const nodeModulesDir = path.join(layerDir, "nodejs");

    // Clean up any existing temporary directories
    if (fs.existsSync(tempDir)) {
      logWithTimestamp("Cleaning up existing temporary directories...", "🧹");
      execSync(`rm -rf ${tempDir}`);
    }

    // Create temporary directories
    fs.mkdirSync(tempDir, { recursive: true });
    fs.mkdirSync(layerDir, { recursive: true });
    fs.mkdirSync(nodeModulesDir, { recursive: true });

    // Create node_modules directory inside nodejs directory (required for Lambda layers)
    const nodeModulesLayerDir = path.join(nodeModulesDir, "node_modules");
    fs.mkdirSync(nodeModulesLayerDir, { recursive: true });

    // Copy package.json to layer directory for reference
    if (fs.existsSync(path.join(lambdaDir, "package.json"))) {
      fs.copyFileSync(
        path.join(lambdaDir, "package.json"),
        path.join(nodeModulesDir, "package.json")
      );
    }

    // Log the directory structure for debugging
    logWithTimestamp(
      `Created layer directory structure: ${nodeModulesLayerDir}`,
      "📂"
    );

    // Step 1: Copy package.json to the temp directory
    logWithTimestamp(
      "Setting up fresh npm install in the temp directory...",
      "📦"
    );

    // Copy package.json to the temp directory
    fs.copyFileSync(
      path.join(lambdaDir, "package.json"),
      path.join(nodeModulesDir, "package.json")
    );

    // Copy package-lock.json if it exists
    const packageLockPath = path.join(lambdaDir, "package-lock.json");
    if (fs.existsSync(packageLockPath)) {
      fs.copyFileSync(
        packageLockPath,
        path.join(nodeModulesDir, "package-lock.json")
      );
    }

    // Step 2: Run npm install --production in the temp directory
    logWithTimestamp(
      "Running npm install --production in the temp directory...",
      "🔧"
    );

    // Navigate to the temp directory and run npm install --production
    execSync(`cd ${nodeModulesDir} && npm install --production`, {
      stdio: "inherit",
    });

    // Step 3: Log the number of top-level production dependencies that were installed
    const topLevelPackages = fs.readdirSync(
      path.join(nodeModulesDir, "node_modules")
    );

    // Step 4: Calculate and log the size of the layer directory
    // Use du -sk for macOS compatibility (gives size in KB)
    const layerSizeInKB = execSync(`du -sk ${layerDir} | awk '{print $1}'`, {
      encoding: "utf8",
    }).trim();

    const layerSizeInMB = (parseInt(layerSizeInKB) / 1024).toFixed(2);
    logWithTimestamp(
      `Layer directory size before zipping: ${layerSizeInMB} MB`,
      "📊"
    );

    // Also calculate the size of the node_modules directory specifically
    const nodeModulesSizeInKB = execSync(
      `du -sk ${path.join(nodeModulesDir, "node_modules")} | awk '{print $1}'`,
      {
        encoding: "utf8",
      }
    ).trim();

    const nodeModulesSizeInMB = (parseInt(nodeModulesSizeInKB) / 1024).toFixed(
      2
    );
    logWithTimestamp(
      `node_modules directory size: ${nodeModulesSizeInMB} MB`,
      "📊"
    );

    logWithTimestamp(
      `Installed ${topLevelPackages.length} top-level production dependencies with their nested dependencies`,
      "✅"
    );

    // Create layer zip file
    logWithTimestamp("Creating layer zip file...", "🗜️");
    const layerZipPath = path.join(tempDir, `${lambdaName}-layer.zip`);
    await zl.archiveFolder(layerDir, layerZipPath);

    // Check the size of the zip file
    const zipStats = fs.statSync(layerZipPath);
    const zipSizeInMB = (zipStats.size / (1024 * 1024)).toFixed(2);
    logWithTimestamp(`Layer zip file size: ${zipSizeInMB} MB`, "📊");

    // AWS Lambda layer size limit is 250MB unzipped and 50MB zipped
    if (parseFloat(zipSizeInMB) > 50) {
      logWithTimestamp(
        `WARNING: Layer zip file size (${zipSizeInMB} MB) exceeds the recommended limit of 50 MB`,
        "⚠️"
      );

      // If the zip file is larger than 250MB, it will definitely fail
      if (parseFloat(zipSizeInMB) > 250) {
        throw new Error(
          "Layer zip file size exceeds AWS Lambda limit of 250 MB"
        );
      }
    }

    logWithTimestamp("Layer zip file created successfully.", "✅");

    // Skip installing dependencies and building - use existing files
    logWithTimestamp("Using existing files without rebuilding...", "⏩");

    // Create function zip file (excluding node_modules)
    logWithTimestamp("Creating function zip file...", "📁");
    const functionZipPath = path.join(tempDir, `${lambdaName}.zip`);

    // Use zip-lib to create the function zip file
    const zipInstance = new zl.Zip();

    // Add all files except node_modules, temp, src, and .ts files
    const files = fs.readdirSync(lambdaDir, { withFileTypes: true });
    for (const file of files) {
      const filePath = path.join(lambdaDir, file.name);
      if (
        file.name !== "node_modules" &&
        file.name !== "temp" &&
        file.name !== "src" &&
        !file.name.endsWith(".ts")
      ) {
        if (file.isDirectory()) {
          zipInstance.addFolder(filePath, file.name);
        } else {
          zipInstance.addFile(filePath, file.name);
        }
      }
    }

    await zipInstance.archive(functionZipPath);
    logWithTimestamp("Function zip file created successfully.", "✅");

    // Deploy layer to AWS
    logWithTimestamp("Deploying layer to AWS...", "☁️");
    const layerName = `${lambdaName}-layer`;
    const layerZipBuffer = fs.readFileSync(layerZipPath);

    const publishLayerParams = {
      LayerName: layerName,
      Content: {
        ZipFile: layerZipBuffer,
      },
      CompatibleRuntimes: ["nodejs18.x"],
    };

    const layerResponse = await lambdaClient.send(
      new PublishLayerVersionCommand(publishLayerParams)
    );
    const layerArn = layerResponse.LayerVersionArn;
    logWithTimestamp(`Layer deployed successfully. ARN: ${layerArn}`, "✅");

    const environmentBasedName = lambdaName + "-" + args.e;

    // Check if lambda function exists
    try {
      await lambdaClient.send(
        new GetFunctionCommand({ FunctionName: environmentBasedName })
      );
      // Function exists, continue with deployment
    } catch (error) {
      logWithTimestamp(
        `ERROR: Lambda function ${environmentBasedName} does not exist.`,
        "❌"
      );
      logWithTimestamp(
        `Please contact Chef Sagar Bansal ji to create the lambda function first.`
      );
      logWithTimestamp(
        `Slack: https://wifytech-crew.slack.com/team/U03CV7201AQ`
      );
      process.exit(1);
    }

    // Read the function zip file
    const functionZipBuffer = fs.readFileSync(functionZipPath);

    // Update existing function code
    logWithTimestamp("Updating function code...", "🔄");
    await lambdaClient.send(
      new UpdateFunctionCodeCommand({
        FunctionName: environmentBasedName,
        ZipFile: functionZipBuffer,
      })
    );
    logWithTimestamp("Function code updated successfully.", "✅");

    // Update function configuration to use the layer
    logWithTimestamp(
      "Updating function configuration to use the layer...",
      "⚙️"
    );

    // Try to update the function configuration with exponential backoff
    let attempt = 0;
    const maxAttempts = 5;
    let success = false;

    while (attempt < maxAttempts && !success) {
      try {
        await lambdaClient.send(
          new UpdateFunctionConfigurationCommand({
            FunctionName: environmentBasedName,
            Layers: [layerArn],
          })
        );
        logWithTimestamp("Function configuration updated successfully.", "✅");
        success = true;
      } catch (configError) {
        if (configError.name === "ResourceConflictException") {
          attempt++;
          if (attempt < maxAttempts) {
            // Exponential backoff: 2s, 4s, 8s, 16s
            const waitTime = Math.pow(2, attempt) * 1000;
            logWithTimestamp(
              `Function still updating. Waiting ${
                waitTime / 1000
              } seconds before retry ${attempt + 1}/${maxAttempts}...`,
              "⏳"
            );
            await sleep(waitTime);
          } else {
            logWithTimestamp(
              `Failed to update function configuration after ${maxAttempts} attempts.`,
              "❌"
            );
            throw configError;
          }
        } else {
          // Re-throw any other errors
          throw configError;
        }
      }
    }

    // Clean up temporary files
    logWithTimestamp("Cleaning up temporary files...", "🧹");
    execSync(`rm -rf ${tempDir}`);

    // Try to clean up the parent temp directory if it's empty
    const parentTempDir = path.join(__dirname, "temp");
    try {
      if (fs.existsSync(parentTempDir)) {
        const files = fs.readdirSync(parentTempDir);
        if (files.length === 0) {
          fs.rmdirSync(parentTempDir);
          logWithTimestamp("Removed empty temp directory", "🧹");
        }
      }
    } catch (cleanupError) {
      // Ignore errors when cleaning up the parent temp directory
      logWithTimestamp("Note: Could not clean up parent temp directory", "ℹ️");
    }

    // Get the lambda ARN
    try {
      const lambdaInfo = await lambdaClient.send(
        new GetFunctionCommand({ FunctionName: environmentBasedName })
      );
      const lambdaArn = lambdaInfo.Configuration.FunctionArn;

      logWithTimestamp(
        `Successfully deployed ${environmentBasedName} with its dependencies as a layer!`,
        "🚀"
      );

      console.log(`\n📋 Lambda ARN: \x1b[36m${lambdaArn}\x1b[0m\n`);
    } catch (error) {
      logWithTimestamp(
        `Successfully deployed ${environmentBasedName} with its dependencies as a layer!`,
        "🚀"
      );
      logWithTimestamp(`Could not retrieve Lambda ARN: ${error.message}`, "ℹ️");
    }
  } catch (error) {
    if (error.name === "RequestEntityTooLargeException") {
      logWithTimestamp(
        `ERROR: The lambda package is too large (exceeds AWS size limit).`,
        "❌"
      );
      logWithTimestamp(
        `Please contact Chef Sagar Bansal ji to help with optimizing the lambda package.`
      );
      console.log(
        `\nSlack: \x1b[34m\x1b[4mhttps://wifytech-crew.slack.com/team/U03CV7201AQ\x1b[0m\n`
      );
    } else if (error.name === "AccessDeniedException") {
      logWithTimestamp(
        `ERROR: You don't have permission to deploy this lambda.`,
        "🔒"
      );
      logWithTimestamp(`Error details: ${error.message}`);
      logWithTimestamp(
        `Please contact Chef Sagar Bansal ji to get the necessary permissions.`
      );
      console.log(
        `\nSlack: \x1b[34m\x1b[4mhttps://wifytech-crew.slack.com/team/U03CV7201AQ\x1b[0m\n`
      );
    } else if (error.name === "ResourceConflictException") {
      if (retryCount < maxRetries) {
        const waitTime = 10000 * (retryCount + 1); // Exponential backoff: 10s, 20s, 30s
        logWithTimestamp(
          `Lambda function is currently being updated. Retrying in ${
            waitTime / 1000
          } seconds... (Attempt ${retryCount + 1}/${maxRetries})`
        );
        await sleep(waitTime);
        return deployLambda(retryCount + 1, maxRetries);
      } else {
        logWithTimestamp(
          `ERROR: The lambda function is still being updated after ${maxRetries} retry attempts.`
        );
        logWithTimestamp(`Error details: ${error.message}`);
        logWithTimestamp(
          `Please wait a few minutes and try again, or contact Chef Sagar Bansal ji for assistance.`
        );
        console.log(
          `\nSlack: \x1b[34m\x1b[4mhttps://wifytech-crew.slack.com/team/U03CV7201AQ\x1b[0m\n`
        );
      }
    } else if (
      error.message &&
      error.message.includes("Resolved credential object is not valid")
    ) {
      logWithTimestamp(`ERROR: AWS credentials are missing or invalid.`);
      logWithTimestamp(
        `Please check your .env file and ensure AWS_ACCESS_KEY_ID and AWS_SECRET_ACCESS_KEY are set correctly.`
      );
    } else {
      console.error("Error deploying lambda:", error);
      logWithTimestamp(
        `Please contact Chef Sagar Bansal ji for assistance with this error.`
      );
      console.log(
        `\nSlack: \x1b[34m\x1b[4mhttps://wifytech-crew.slack.com/team/U03CV7201AQ\x1b[0m\n`
      );
    }
    process.exit(1);
  }
};

// Run the deployment
deployLambda();
