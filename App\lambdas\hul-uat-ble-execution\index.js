const { makeRequest } = require("./http_utils");
const { constants } = require("./JigHelper")
const JIG_PREFIX = '#PI#JIG#';


const listOfBLEStates = {
    BLE_INITIALIZATION : "BLE_INITIALIZATION", // icon & initital text required here 
    BLE_PERMISSION_REQUIRED : "BLE_PERMISSION_REQUIRED",
    BLE_PERMISSION_DENIED : "BLE_PERMISSION_DENIED",
    BLE_SEARCHING_FOR_MACHINE : "BLE_SEARCHING_FOR_MACHINE", // list of available devices
    BLE_DEVICE_SELECTION : "BLE_DEVICE_SELECTION", // list of filtered devices to connect
    BLE_CONNECTING_TO_DEVICE : "BLE_CONNECTING_TO_DEVICE", // device to be connect
    BLE_CONNECTED : "BLE_CONNECTED", // connected device info & next command to run
    BLE_DISCONNECTED : "BLE_DISCONNECTED", // disconnected device, last executed command
    BLE_EXECUTING_CMD : "BLE_EXECUTING_CMD", // device & command to run
    BLE_SPEAKING_TO_LAMBDA : "BLE_SPEAKING_TO_LAMBDA", // current state and it's state info & , response received from lambda
    BLE_ERROR_FOUND : "BLE_ERROR_FOUND", // if lambda has told to show a error
    BLE_FINAL_SUCCESS : "BLE_FINAL_SUCCESS", // success message & icon, last executed command, last connected device, final value
    BLE_NETWORK_FAILURE : "BLE_NETWORK_FAILURE", // params which were being sent to lambda
    BLUETOOTH_NOT_FOUND : "BLUETOOTH_NOT_FOUND", // nothing to do
    BLE_EXECUTING_CMD_ON_ACTION : "BLE_EXECUTING_CMD_ON_ACTION",
    BLE_CHOOSE_COMMAND : "BLE_CHOOSE_COMMAND",
    BLE_OPERATION_ABORT : "BLE_OPERATION_ABORT"
}

const BLE_CommandSet = {
  "GET_DEVICE_UNIQUE_ID":{
      "serviceId" : "0000a003-0000-1000-8000-00805f9b34fb",
      "characteristicId" : "0000b205-0000-1000-8000-00805f9b34fb",
      'mode' : "READ"
  },
  "SET_DEVICE_UNIQUE_ID":{
      "serviceId" : "0000a003-0000-1000-8000-00805f9b34fb",
      "characteristicId" : "0000b205-0000-1000-8000-00805f9b34fb",
      'mode' : "WRITE",
  },
  "GENERATE_RANDOM_CHAR":{
      "serviceId" : "0000a003-0000-1000-8000-00805f9b34fb",
      "characteristicId" : "0000b201-0000-1000-8000-00805f9b34fb",
      'mode' : "WRITE"
  },
  "GET_RANDOM_CHAR":{
      "serviceId" : "0000a003-0000-1000-8000-00805f9b34fb",
      "characteristicId" : "0000b202-0000-1000-8000-00805f9b34fb",
      'mode' : "READ"
  },
  "RESET_FILTER":{
      "serviceId" : "0000a003-0000-1000-8000-00805f9b34fb",
      "characteristicId" : "0000b203-0000-1000-8000-00805f9b34fb",
      'mode' : "WRITE"
  },
  "GET_RESET_RESULT":{
      "serviceId" : "0000a003-0000-1000-8000-00805f9b34fb",
      "characteristicId" : "0000b204-0000-1000-8000-00805f9b34fb",
      'mode' : "READ"
  }
}

const getCommandCode = (commandDetails)=>{
  let matchedCommand = Object.keys(BLE_CommandSet).filter(commandCode => {
    return commandDetails.serviceId == BLE_CommandSet[commandCode].serviceId &&
        commandDetails.characteristicId == BLE_CommandSet[commandCode].characteristicId &&
        commandDetails.mode == BLE_CommandSet[commandCode].mode ;
  })
  console.log('Matched command',matchedCommand[0]);
  return matchedCommand[0];
}

function containsOnlyZero(str) {
    for (let i = 0; i < str.length; i++) {
        if (str.charAt(i) !== '0') {
            console.log("containsOnlyZero 1")
            return false;
        }
    }
    console.log("containsOnlyZero 2")
    return true;
}

function removeLastChar(str) {
    if(str.length == 33){
        return str.slice(0, -1);
    }else{
        return str;
    }
}

const getUniqueId = async(data) => {
  try {
      console.log("Netsuite api response get unique id params", data);
      const response = await makeRequest(data);
      console.log("Netsuite api response get unique id", response.data); // Handle the API response data
      
      const netSuiteData = response.data;
      let uniqueId = netSuiteData["unique id"];
      return uniqueId;
      
  } catch (error) {
      throw error;
  }
}

const establishNewSession = async(data) => {
  try {
      console.log("Netsuite api response establishNewSession params", data);
      const response = await makeRequest(data);
      console.log("Netsuite api response establishNewSession", response.data); // Handle the API response data
      
      const netSuiteData = response.data;
      let session_id = netSuiteData["session id"];
      return session_id;
      
  } catch (error) {
      throw error;
  }
}

const cipherCodeGeneration = async(data) => {
  try {
      console.log("Netsuite api response cipherCodeGeneration params", data);
      const response = await makeRequest(data);
      
      const netSuiteData = response.data;
      let cipherCode = netSuiteData["Cipher code"];
      return cipherCode;
      
  } catch (error) {
      throw error;
  }
}

const resetAcknowledgement= async(data) => {
  try {
      console.log("Netsuite api response resetAcknowledgement params", data);
      const response = await makeRequest(data);
      console.log("Netsuite api response resetAcknowledgement", response.data); // Handle the API response data
      
      const netSuiteData = response.data;
      //have to see what do we return from this function
      return netSuiteData;
      
  } catch (error) {
      throw error;
  }
  
  
}

function generateRandomNumber(length = 1) {
  let result = '';
  
  for (let i = 0; i < length; i++) {
    const randomDigit = Math.floor(Math.random() * 10); // Generates a random digit (0-9)
    result += randomDigit;
  }
  
  return result;
}

function showErrorAndRestart(){
    //code to be done

    let nextStateCode = listOfBLEStates.BLE_ERROR_FOUND;
     
    let nextStateInfo = {
        'UiData' : {}
    }
    // nextStateInfo['UiData']['ShowProgress'] = true;
    nextStateInfo['UiData']['ShowButton'] = true;
    nextStateInfo['UiData']['Buttons'] = [
        {
            'ButtonTitle' : 'Retry',
            'Message' : `Error Found`,
            'IconLink' : '',
            'onClickCommand' : BLE_CommandSet.GET_DEVICE_UNIQUE_ID
        }
    ];
    
    return {
        statusCode: 200,
        status: true,
        body: JSON.stringify({
            nextStateCode,
            nextStateInfo
        }),
    }
}

const handler = async (event) => {
  
    console.log("hul-dev-ble-execution initial event details",event);

    let {currentStateCode, stateInfo, contextInfo} = event;
    let external_order_id = contextInfo.sbtskDetails?.collab_order_id;
    let nextStateCode = listOfBLEStates.BLE_INITIALIZATION;
    let nextStateInfo = {
        'UiData' : {
            
        }
    };
    let isAllPermissionEnabled = stateInfo?.isAllPermissionEnabled;
    let missingPermissions = stateInfo?.missingPermissions || [];
        
    
    if(currentStateCode == listOfBLEStates.BLE_OPERATION_ABORT) {
        return  {
            statusCode: 200,
            status: true,
            body: JSON.stringify({
                nextStateCode : undefined,
                nextStateInfo
            }),
        }
    }
    
    //first if condition to check if any permission change runtime
    if(currentStateCode != listOfBLEStates.BLE_INITIALIZATION 
        && currentStateCode != listOfBLEStates.BLE_PERMISSION_REQUIRED 
        && currentStateCode != listOfBLEStates.BLE_PERMISSION_DENIED) {
            
            if(isAllPermissionEnabled == false && missingPermissions.length > 0) {
                nextStateCode = listOfBLEStates.BLE_PERMISSION_DENIED;
            
                nextStateInfo['UiData']['ShowButton'] = true;
                nextStateInfo['UiData']['Buttons'] = [
                    {
                        'ButtonTitle' : 'Connect Again',
                        'Message' : 'Permission denied'
                    }
                ];
                
                return  {
                    statusCode: 200,
                    status: true,
                    body: JSON.stringify({
                        nextStateCode,
                        nextStateInfo
                    }),
                };
            }
            let bleOperationFailed = stateInfo?.bleOperationFailed;
            if(bleOperationFailed != undefined && bleOperationFailed['code'] && bleOperationFailed['message']) {
                let startOver = bleOperationFailed['code'] == 500;
                nextStateCode = listOfBLEStates.BLE_ERROR_FOUND;
                
                let errorCode = bleOperationFailed['code']

                if(errorCode == 505 || errorCode == 506){
                    let disableButton = true
                    nextStateInfo['UiData']['ShowButton'] = true;
                    nextStateInfo['UiData']['Buttons'] = [
                        {
                            'ButtonTitle' : 'Retry',
                            'Message' : errorCode == 505 ? 'Control circuit failure E1' : 'Control circuit failure E2',
                            'IconLink' : '',
                            // 'onClickCommand' : BLE_CommandSet.GET_DEVICE_UNIQUE_ID,
                            'disableButton': disableButton
                        }
                    ];
                }else{
                    // nextStateInfo['UiData']['ShowProgress'] = true;
                    nextStateInfo['UiData']['ShowButton'] = true;
                    nextStateInfo['UiData']['Buttons'] = [
                        {
                            'ButtonTitle' : 'Retry',
                            'Message' : `Error Found: ${bleOperationFailed.message}`,
                            'IconLink' : '',
                            'onClickCommand' : BLE_CommandSet.GET_DEVICE_UNIQUE_ID,
                            startOver
                        }
                    ];
                }
                
                
                return  {
                    statusCode: 200,
                    status: true,
                    body: JSON.stringify({
                        nextStateCode,
                        nextStateInfo
                    }),
                };
            }
        
    }
    
    if( currentStateCode == listOfBLEStates.BLE_INITIALIZATION) {
        
        
        if(isAllPermissionEnabled == true && missingPermissions?.length == 0) {
            nextStateCode = listOfBLEStates.BLE_SEARCHING_FOR_MACHINE;
            
            nextStateInfo['UiData']['ShowProgress'] = true;
            nextStateInfo['UiData']['ProgressMessage'] = 'Please connect jig to pureit machine';
            nextStateInfo['UiData']['ProgressIcon'] = 'https://static.wify.co.in/images/BLE/ble_initialization.gif'
            // nextStateInfo['UiData']['ShowProgress'] = true;
            // nextStateInfo['UiData']['ProgressMessage'] = 'Searching for machine';
            // nextStateInfo['UiData']['ProgressIcon'] = 'https://static.wify.co.in/images/BLE/ble_searching_for_machine.gif'

        }
        else if(isAllPermissionEnabled == false && missingPermissions.length > 0) {
            nextStateCode = listOfBLEStates.BLE_PERMISSION_REQUIRED;
            
            nextStateInfo['UiData']['ShowButton'] = true;
            nextStateInfo['UiData']['Buttons'] = [
                {
                    'ButtonTitle' : 'Allow',
                    'Message' : 'Click allow for permission'
                }
            ];
            // nextStateInfo['UiData']['IconLink'] = ''
            
            

        } else {
            
            nextStateInfo['UiData']['ShowProgress'] = true;
            nextStateInfo['UiData']['ProgressMessage'] = 'Please connect jig to pureit machine';
            nextStateInfo['UiData']['ProgressIcon'] = 'https://static.wify.co.in/images/BLE/ble_initialization.gif'

        }
        
        
        
        return  {
            statusCode: 200,
            status: true,
            body: JSON.stringify({
                nextStateCode,
                nextStateInfo
            }),
        };
    }
    
    if(currentStateCode == listOfBLEStates.BLE_PERMISSION_REQUIRED || currentStateCode == listOfBLEStates.BLE_PERMISSION_DENIED) {
        let isAllPermissionEnabled = stateInfo?.isAllPermissionEnabled;
        let missingPermissions = stateInfo?.missingPermissions || [];
        if(isAllPermissionEnabled && missingPermissions?.length == 0) {
            nextStateCode = listOfBLEStates.BLE_SEARCHING_FOR_MACHINE;

            nextStateInfo['UiData']['ShowProgress'] = true;
            nextStateInfo['UiData']['ProgressMessage'] = 'Please connect jig to pureit machine';
            nextStateInfo['UiData']['ProgressIcon'] = 'https://static.wify.co.in/images/BLE/ble_initialization.gif'
            
            // nextStateInfo['UiData']['ShowProgress'] = true;
            // nextStateInfo['UiData']['ProgressMessage'] = 'Searching for machine';
            // nextStateInfo['UiData']['ProgressIcon'] = 'https://static.wify.co.in/images/BLE/ble_searching_for_machine.gif'
            
        } else {
            nextStateCode = listOfBLEStates.BLE_PERMISSION_DENIED;
            
            nextStateInfo['UiData']['ShowButton'] = true;
            nextStateInfo['UiData']['Buttons'] = [
                {
                    'ButtonTitle' : 'Allow',
                    'Message' : 'Click allow for permission'
                }
            ];
        }
        return  {
            statusCode: 200,
            status: true,
            body: JSON.stringify({
                nextStateCode,
                nextStateInfo,
                
            }),
        };
    }
    
    
    if(currentStateCode == listOfBLEStates.BLE_SEARCHING_FOR_MACHINE) {
        let deviceList = stateInfo?.DeviceList || [];
        console.log("deviceList",deviceList);
        let filteredDeviceList = [];
        if(deviceList.length > 0) {
            // filteredDeviceList = deviceList;
            filteredDeviceList = deviceList.filter(
                device => {
                    // return true;
                    return device.name?.includes(JIG_PREFIX)
                }
            )
            
            // if(filteredDeviceList.length == 1) {
            //     let deviceDet = {...filteredDeviceList[0]};
            //     deviceDet.name = deviceDet.name + " 2";
            //     filteredDeviceList.push(deviceDet);
            // }
            
            
        }
        
        nextStateCode = listOfBLEStates.BLE_SEARCHING_FOR_MACHINE;
        if(filteredDeviceList.length >= 1){
            nextStateCode = listOfBLEStates.BLE_DEVICE_SELECTION;

            nextStateInfo['UiData']['PopupTitle'] = 'Select a machine';
            nextStateInfo['UiData']['FilteredDeviceList'] = filteredDeviceList;
            
            // nextStateInfo['UiData']['ShowProgress'] = true;
            // nextStateInfo['UiData']['ProgressMessage'] = 'Searching for machine';
            // nextStateInfo['UiData']['ProgressIcon'] = 'https://static.wify.co.in/images/BLE/ble_searching_for_machine.gif'


        } else if(filteredDeviceList.length == 1){
            nextStateCode = listOfBLEStates.BLE_CONNECTING_TO_DEVICE;

            nextStateInfo['UiData']['ShowProgress'] = true;
            nextStateInfo['UiData']['ProgressMessage'] = 'Connecting... Please wait'
            nextStateInfo['UiData']['ProgressIcon'] = 'https://static.wify.co.in/images/BLE/ble_connecting_to_device.gif';
            
            nextStateInfo['DeviceToConnect'] = filteredDeviceList[0];
            nextStateInfo['command'] = {
                serviceId : constants.SERVICE_FILTER_RESET_A003,
                characteristicId : constants.CHAR_DEVICE_UNIQUE_ID_B205_READ_WRITE_NOTIFY,
                mode : "READ"
            }
            
        } else {
            
          nextStateInfo['UiData']['ShowProgress'] = true;
          nextStateInfo['UiData']['ProgressMessage'] = 'Please connect jig to pureit machine';
          nextStateInfo['UiData']['ProgressIcon'] = 'https://static.wify.co.in/images/BLE/ble_initialization.gif'
            
        }
        
        console.log("next state info",nextStateInfo);
        return  {
            statusCode: 200,
            status: true,
            body: JSON.stringify({
                nextStateCode,
                nextStateInfo
            }),
        };
    }
    
    if(currentStateCode == listOfBLEStates.BLE_DEVICE_SELECTION) {
        
        let deviceList = stateInfo?.DeviceList || [];
        nextStateCode = listOfBLEStates.BLE_CONNECTING_TO_DEVICE;

        nextStateInfo['UiData']['ShowProgress'] = true;
        nextStateInfo['UiData']['ProgressMessage'] = 'Connecting... Please wait'
        nextStateInfo['UiData']['ProgressIcon'] = 'https://static.wify.co.in/images/BLE/ble_connecting_to_device.gif';
        
        nextStateInfo['DeviceToConnect'] = deviceList[0];
        nextStateInfo['command'] = {
            serviceId : constants.SERVICE_FILTER_RESET_A003,
            characteristicId : constants.CHAR_DEVICE_UNIQUE_ID_B205_READ_WRITE_NOTIFY,
            mode : "READ"
        }
        
        return  {
            statusCode: 200,
            status: true,
            body: JSON.stringify({
                nextStateCode,
                nextStateInfo,
                
            }),
        };
    }
    
    if(currentStateCode == listOfBLEStates.BLE_EXECUTING_CMD) {
        let { SelectedDevice, CommandDetails, ResponseDetails } = stateInfo;
        if(CommandDetails == undefined || ResponseDetails == undefined){
          return showErrorAndRestart();// TODO
        }
        switch (getCommandCode(CommandDetails)) {
          case 'GET_DEVICE_UNIQUE_ID':
            //  check if received device unique id is zero
            //    Yes 
            //      call NS API, to generate device unique id
            //      once received from NS next command would be SET DEVICE UNIQUE ID
            //    NO
            //      CAll NS API to gerenatea a session id
            //      once recieved from NS next state would be
            //        SHOW BUTTON as true
            //        BUTTON would be titled as "RESET"
            //        ON CLICK a GENERATE RANDOM CHAR command should run, in the commandDetails
            //          add another key called as  'sessionID'(Which you received from NS)
            //          add another key called as  'DeviceUniqID'(Which you received from BLE DEVICE)
            if(containsOnlyZero(ResponseDetails?.value) || !ResponseDetails?.value) { // keep an eye out for this as we are fetching unique id when value is undefined as it was in some cases
                //run unique id generation
                console.log("device id all 0 ->",ResponseDetails?.value);
                let value = '00000000000000000000000000000000';
                let data={
                    "rfs_id": external_order_id,
                    "unique_id":"",
                    "action_type": "UNIQUE_ID"
                }
                //callng netsuite api
                value = await getUniqueId(data);
                console.log(value);

                if(!value){
                    let nextStateInfo = {
                        'UiData' : {}
                    }
                    // nextStateInfo['UiData']['ShowProgress'] = true;
                    nextStateInfo['UiData']['ShowButton'] = true;
                    nextStateInfo['UiData']['Buttons'] = [
                        {
                            'ButtonTitle' : 'Retry',
                            'Message' : `Network Failure E1`,
                            'IconLink' : '',
                            'onClickCommand' : BLE_CommandSet.GET_DEVICE_UNIQUE_ID
                        }
                    ];
                    // nextStateInfo['command'] = {
                    //     ...BLE_CommandSet.GET_DEVICE_UNIQUE_ID
                    // }
                    return{
                        statusCode: 200,
                        status: true,
                        body: JSON.stringify({
                            nextStateCode : listOfBLEStates.BLE_ERROR_FOUND,
                            nextStateInfo
                        }),
                    }
                }
                
                nextStateInfo['command'] = {
                    ...BLE_CommandSet.SET_DEVICE_UNIQUE_ID,
                    value : value
                }
                
                console.log("next state info for getuniqueid : ",nextStateInfo);
                return  {
                    statusCode: 200,
                    status: true,
                    body: JSON.stringify({
                        nextStateCode : listOfBLEStates.BLE_EXECUTING_CMD,
                        nextStateInfo
                    }),
                };
            }else{
                console.log("device id is valid ->",ResponseDetails.value);
                let value = removeLastChar(ResponseDetails.value);
                
                let unique_id_ack={
                    "rfs_id": external_order_id,
                    "unique_id":value,
                    "action_type": "UNIQUE_ID"
                }
                
                // call netsuit api with existing unique id
                let uniqueid_ack_resp = await getUniqueId(unique_id_ack);
                console.log(uniqueid_ack_resp);
                
                if(!uniqueid_ack_resp) {
                    let nextStateInfo = {
                        'UiData' : {}
                    }
                    // nextStateInfo['UiData']['ShowProgress'] = true;
                    nextStateInfo['UiData']['ShowButton'] = true;
                    nextStateInfo['UiData']['Buttons'] = [
                        {
                            'ButtonTitle' : 'Retry',
                            'Message' : `Network Failure E0`,
                            'IconLink' : '',
                            'onClickCommand' : BLE_CommandSet.GET_DEVICE_UNIQUE_ID
                        }
                    ];
                    // nextStateInfo['command'] = {
                    //     ...BLE_CommandSet.GET_DEVICE_UNIQUE_ID
                    // }
                    return{
                        statusCode: 200,
                        status: true,
                        body: JSON.stringify({
                            nextStateCode : listOfBLEStates.BLE_ERROR_FOUND,
                            nextStateInfo
                        }),
                    }
                }
                
                
                let data = {
                    "rfs_id": external_order_id,
                    "unique_id": value,
                    "action_type": "SESSION_ID",
                        "mac_address": SelectedDevice.macAddress
                }
                
                let sessionId = await establishNewSession(data);
                console.log('session id--',sessionId);

                if(!sessionId){
                    let nextStateInfo = {
                        'UiData' : {}
                    }
                    // nextStateInfo['UiData']['ShowProgress'] = true;
                    nextStateInfo['UiData']['ShowButton'] = true;
                    nextStateInfo['UiData']['Buttons'] = [
                        {
                            'ButtonTitle' : 'Retry',
                            'Message' : `Failed to generate a session`,
                            'IconLink' : '',
                            'onClickCommand' : BLE_CommandSet.GET_DEVICE_UNIQUE_ID
                        }
                    ];
                    return{
                        statusCode: 200,
                        status: true,
                        body: JSON.stringify({
                            nextStateCode : listOfBLEStates.BLE_ERROR_FOUND,
                            nextStateInfo
                        }),
                    }
                }

                nextStateInfo['UiData']['ShowButton'] = true;
                nextStateInfo['UiData']['Buttons'] = [
                    {
                        'ButtonTitle' : 'Reset',
                        'Message' : 'Reset the device',
                        'IconLink' : '',
                        'onClickCommand' : {
                            ...BLE_CommandSet.GENERATE_RANDOM_CHAR,
                            'value' : generateRandomNumber(),
                            'sessionId' : sessionId,
                            'deviceUniqueId' : value
                        }
                    }
                ];
                // nextStateInfo['command'] = {
                //     serviceId : BLE_CommandSet.SET_DEVICE_UNIQUE_ID.serviceId,
                //     characteristicId : BLE_CommandSet.SET_DEVICE_UNIQUE_ID.characteristicId,
                //     mode : "WRITE",
                //     value : value
                // have to figure out what to do with session id
                console.log("next state info for generate session id : ",nextStateInfo);
                return  {
                    statusCode: 200,
                    status: true,
                    body: JSON.stringify({
                        nextStateCode : listOfBLEStates.BLE_CHOOSE_COMMAND,
                        nextStateInfo,
                        
                    }),
                };
            }
            break;
          case 'SET_DEVICE_UNIQUE_ID':
            //  if repsonse is successful 
            //    Yes
            //      CAll NS API to gerenatea a session id (Get device unique id from commandDetails value key)
            //      once recieved from NS next state would be
            //        SHOW BUTTON as true
            //        BUTTON would be titled as "RESET"
            //        ON CLICK a GENERATE RANDOM CHAR command should run, in the commandDetails
            //          add another key called as  'sessionID'(Which you received from NS)
            //          add another key called as  'DeviceUniqID'(Which you received from commandDetails value key)
            //Generate session id
            let value = ResponseDetails.value;
            let data = {
                "rfs_id": external_order_id,
                "unique_id": CommandDetails.value,
                "action_type": "SESSION_ID",
                "mac_address": SelectedDevice.macAddress
            }
            
            let sessionId = await establishNewSession(data);
            console.log(sessionId);

            if(!sessionId){
                let nextStateInfo = {
                    'UiData' : {}
                }
                // nextStateInfo['UiData']['ShowProgress'] = true;
                nextStateInfo['UiData']['ShowButton'] = true;
                nextStateInfo['UiData']['Buttons'] = [
                    {
                        'ButtonTitle' : 'Retry',
                        'Message' : `Failed generate a session`,
                        'IconLink' : '',
                        'onClickCommand' : BLE_CommandSet.GET_DEVICE_UNIQUE_ID
                    }
                ];
                return{
                    statusCode: 200,
                    status: true,
                    body: JSON.stringify({
                        nextStateCode : listOfBLEStates.BLE_ERROR_FOUND,
                        nextStateInfo
                    }),
                }
            }
            
            nextStateInfo['UiData']['ShowButton'] = true;
            nextStateInfo['UiData']['Buttons'] = [
                {
                    'ButtonTitle' : 'Reset',
                    'Message' : 'Reset the device',
                    'IconLink' : '',
                    'onClickCommand' : {
                        ...BLE_CommandSet.GENERATE_RANDOM_CHAR,
                        'value' : generateRandomNumber(),
                        'sessionId' : sessionId,
                        'deviceUniqueId' : CommandDetails.value
                    },
                }
            ];
            // nextStateInfo['command'] = {
            //     serviceId : BLE_CommandSet.SET_DEVICE_UNIQUE_ID.serviceId,
            //     characteristicId : BLE_CommandSet.SET_DEVICE_UNIQUE_ID.characteristicId,
            //     mode : "WRITE",
            //     value : value
            // have to figure out what to do with session id
            console.log("next state info for generate session id 2: ",nextStateInfo);
            return  {
                statusCode: 200,
                status: true,
                body: JSON.stringify({
                    nextStateCode : listOfBLEStates.BLE_CHOOSE_COMMAND,
                    nextStateInfo,
                    
                }),
            };
            
            break;
          case 'GENERATE_RANDOM_CHAR':
            //  If response is successful
            //    Yes
            //      Next state should be to execute another command "GET_RANDOM_CHAR"
            //          add another key called as  'sessionID'(Which you received from commandDetails)
            //          add another key called as  'DeviceUniqID'(Which you received from commandDetails)
            console.log("case generate random character",);
            if(ResponseDetails.value){ //what we send when we click on reset
                nextStateInfo['command'] = {
                    ...BLE_CommandSet.GET_RANDOM_CHAR,
                    'sessionId' : CommandDetails.sessionId,
                    'deviceUniqueId' : CommandDetails.deviceUniqueId
                }
                console.log("next state info for generate random number ->",nextStateInfo);
                return  {
                    statusCode: 200,
                    status: true,
                    body: JSON.stringify({
                        nextStateCode : listOfBLEStates.BLE_EXECUTING_CMD,
                        nextStateInfo,
                        
                    }),
                };
            }else{
                console.log("Running else for GENERATE_RANDOM_CHAR");
            }
            break;
          case 'GET_RANDOM_CHAR':
            //  If response is successful
            //    Yes
            //      CALL NS API to generate cipher, get random char from response details and oherdetails from commandDetails
            //      once recieved from NS next ewxecuter another command RESET COMMAND
            //          add another key called as  'sessionID'(Which you received from commandDetails)
            //          add another key called as  'DeviceUniqID'(Which you received from commandDetails)
            //          add another key called as  'RandomChar' (From responseDetails)
            console.log("Running Get Random char case");
            if(ResponseDetails.value){
                let data = {
                    "rfs_id": external_order_id,
                    "unique_id": CommandDetails.deviceUniqueId,
                    "action_type":"CIPHER_CODE",
                    "session_id": CommandDetails.sessionId,
                    "random_number": ResponseDetails.value
                }
                let cipherCode = await cipherCodeGeneration(data);

                if(!cipherCode){
                    let nextStateInfo = {
                        'UiData' : {}
                    }
                    // nextStateInfo['UiData']['ShowProgress'] = true;
                    nextStateInfo['UiData']['ShowButton'] = true;
                    nextStateInfo['UiData']['Buttons'] = [
                        {
                            'ButtonTitle' : 'Retry',
                            'Message' : `Failed to get cipher command`,
                            'IconLink' : '',
                            'onClickCommand' : BLE_CommandSet.GET_RANDOM_CHAR
                        }
                    ];
                    return{
                        statusCode: 200,
                        status: true,
                        body: JSON.stringify({
                            nextStateCode : listOfBLEStates.BLE_ERROR_FOUND,
                            nextStateInfo
                        }),
                    }
                }



                nextStateInfo['randomChar'] = ResponseDetails.value || generateRandomNumber(8)
                nextStateInfo['command'] = {
                    ...BLE_CommandSet.RESET_FILTER,
                    value : cipherCode,
                    'sessionId' : CommandDetails.sessionId,
                    'deviceUniqueId' : CommandDetails.deviceUniqueId,
                    'randomNumber' : ResponseDetails.value
                }

                // console.log("next state info for GET_RANDOM_CHAR : ",nextStateInfo);
                return  {
                    statusCode: 200,
                    status: true,
                    body: JSON.stringify({
                        nextStateCode : listOfBLEStates.BLE_EXECUTING_CMD,
                        nextStateInfo,
                        
                    }),
                };
            }else{
                console.log("Running else for GET_RANDOM_CHAR");
            }
            break;
          case 'RESET_FILTER':
            // If response is successful
            //    Yes
            //       execute another command GET_RESET_RESULT
            //          add another key called as  'sessionID'(Which you received from commandDetails)
            //          add another key called as  'DeviceUniqID'(Which you received from commandDetails)
            //          add another key called as  'RandomChar' (From responseDetails)
            if(ResponseDetails.value){
                nextStateInfo['command'] = {
                    ...BLE_CommandSet.GET_RESET_RESULT,
                    'deviceUniqueId' : CommandDetails.deviceUniqueId,
                    'sessionId' : CommandDetails.sessionId,
                    'randomNumber' : CommandDetails.randomNumber,
                }

                console.log("next state info for reset filter : ",nextStateInfo);
                return  {
                    statusCode: 200,
                    status: true,
                    body: JSON.stringify({
                        nextStateCode : listOfBLEStates.BLE_EXECUTING_CMD,
                        nextStateInfo,
                        
                    }),
                };
            }else{
                console.log("Running else for RESET_FILTER");
            }
            break;
          case 'GET_RESET_RESULT':
            // If response is successful
            //    Yes
            //      Call NS API to update reset acknowledgement
            if(ResponseDetails.value >= 1){
                let data = {
                    "rfs_id": external_order_id,
                    "action_type": "RESET_ACKNOWLEDGMENT",
                    "acknowledgment":"Reset successfully Completed"
                }
                let resetAck = await resetAcknowledgement(data);
                if(resetAck.status == 'success'){
                    nextStateCode = listOfBLEStates.BLE_FINAL_SUCCESS;
                    nextStateInfo['UiData']['Message'] = 'Reset successful';
                    nextStateInfo['UiData']['Icon'] = 'https://static.wify.co.in/images/BLE/reset_successful.gif';
                    nextStateInfo['FormFieldValue'] = CommandDetails.sessionId;
                    
                    console.log("next state info for reset acknowledgement : ",nextStateInfo);
                    return  {
                        statusCode: 200,
                        status: true,
                        body: JSON.stringify({
                            nextStateCode,
                            nextStateInfo
                        }),
                    };
                }else{
                    let nextStateInfo = {
                        'UiData' : {}
                    }
                    // nextStateInfo['UiData']['ShowProgress'] = true;
                    nextStateInfo['UiData']['ShowButton'] = true;
                    nextStateInfo['UiData']['Buttons'] = [
                        {
                            'ButtonTitle' : 'Retry',
                            'Message' : `Failed reset acknowledgement`,
                            'IconLink' : '',
                            'onClickCommand' : BLE_CommandSet.GET_RESET_RESULT
                        }
                    ];
                    return{
                        statusCode: 200,
                        status: true,
                        body: JSON.stringify({
                            nextStateCode : listOfBLEStates.BLE_ERROR_FOUND,
                            nextStateInfo
                        }),
                    }
                
                }
            }else{
                //todo
                console.log("Running else for GET_RESET_RESULT");
                nextStateCode = listOfBLEStates.BLE_ERROR_FOUND;
                
                nextStateInfo['UiData']['ShowButton'] = true;
                nextStateInfo['UiData']['Buttons'] = [
                    {
                        'ButtonTitle' : 'Retry',
                        'Message' : `Reset Failed`,
                        'IconLink' : '',
                        // 'onClickCommand' : BLE_CommandSet.GET_DEVICE_UNIQUE_ID,
                        'startOver' : true
                    }
                ];
                return  {
                    statusCode: 200,
                    status: true,
                    body: JSON.stringify({
                        nextStateCode,
                        nextStateInfo
                    }),
                };
            }
            break;
          default:
            return showErrorAndRestart();
            break;
        }
        
        return  {
            statusCode: 200,
            status: true,
            body: JSON.stringify({
                nextStateCode,
                nextStateInfo
            }),
        };
    }
  
};

exports.handler = handler;
