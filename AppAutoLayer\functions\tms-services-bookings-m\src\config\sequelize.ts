import dotenv from "dotenv";
import { Sequelize } from "sequelize";

dotenv.config();

// Extract database info from DATABASE_URL or individual environment variables
const getDatabaseConfig = () => {
  const dbUrl = process.env.DATABASE_URL || "";
  const regex = /postgresql:\/\/([^:]+):([^@]+)@([^:]+):([^/]+)\/([^?]+)/;
  const match = dbUrl.match(regex);

  if (!match) {
    return {
      host: process.env.DB_HOST || "localhost",
      port: parseInt(process.env.DB_PORT || "5432"),
      username: process.env.DB_USERNAME || "postgres",
      password: process.env.DB_PASSWORD || "postgres",
      database: process.env.DB_NAME || "tms_bookings_dev",
    };
  }

  return {
    username: match[1],
    password: match[2],
    host: match[3],
    port: parseInt(match[4]),
    database: match[5],
  };
};

// Database configuration
const dbConfig = getDatabaseConfig();

// Create Sequelize instance
const sequelize = new Sequelize({
  dialect: "postgres",
  host: dbConfig.host,
  port: dbConfig.port,
  username: dbConfig.username,
  password: dbConfig.password,
  database: dbConfig.database,
  logging: process.env.NODE_ENV === "development" ? console.log : false,
  define: {
    timestamps: true,
    underscored: false,
  },
});

// Function to test database connection
export const testConnection = async (): Promise<void> => {
  try {
    await sequelize.authenticate();
    // Connection success is logged at a higher level with emojis
  } catch (error) {
    console.error("❌ Unable to connect to the database:", error);
    throw error;
  }
};

export default sequelize;
