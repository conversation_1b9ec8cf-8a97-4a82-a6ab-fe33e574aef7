class JigHelper {
    // J<PERSON> SERVICE
    static SERVICE_JIG_BASIC_INFO_A001 = '0000a001-0000-1000-8000-00805f9b34fb';
    // JIG SERVICE CHARACTERISTICS
    static CHAR_JIG_MAC_ADDRESS_B001_READ = '0000b001-0000-1000-8000-00805f9b34fb';
    static CHAR_FIRMWARE_VERSION_B002_READ = '0000b002-0000-1000-8000-00805f9b34fb';

    // FILTER RESET SERVICE
    static SERVICE_FILTER_RESET_A003 = '0000a003-0000-1000-8000-00805f9b34fb';
    // CHARACTERISTIC
    static CHAR_REFRESH_RANDOM_B201_WRITE = '0000b201-0000-1000-8000-00805f9b34fb';
    static CHAR_RANDOM_CHARACTERS_B202_READ_NOTIFY = '0000b202-0000-1000-8000-00805f9b34fb';
    static CHAR_RESET_COMMAND_B203_WRITE = '0000b203-0000-1000-8000-00805f9b34fb';
    static CHAR_FILTER_RESET_RESULT_B204_READ_NOTIFY = '0000b204-0000-1000-8000-00805f9b34fb';
    static CHAR_DEVICE_UNIQUE_ID_B205_READ_WRITE_NOTIFY = '0000b205-0000-1000-8000-00805f9b34fb';
    // DESCRIPTOR
    static DESC_DEVICE_UNIQUE_ID = '00002902-0000-1000-8000-00805f9b34fb';

    // DEVICE INFORMATION SERVICE
    static SERVICE_DEVICE_INFO_A002 = '0000a002-0000-1000-8000-00805f9b34fb';
    // CHARACTERISTIC
    static CHAR_NUMBER_OF_FILTERS_B104_READ = '0000b104-0000-1000-8000-00805f9b34fb';
    static CHAR_FILTERS_INFO_B105_READ = '0000b105-0000-1000-8000-00805f9b34fb';
}
exports.constants = JigHelper