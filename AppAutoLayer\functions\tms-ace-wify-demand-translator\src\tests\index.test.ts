import { handler } from "../index";
import { EventPayload } from "../types";
import { describe, it, expect, jest } from "@jest/globals";

describe("ACE Demand Translator Lambda", () => {
  const validPayload: EventPayload = {
    is_api: true,
    org_id: "123",
    usr_id: "user123",
    ip_address: "***********",
    user_agent: "Mozilla/5.0",
    filters: { key: "value" },
    srvc_type_id: 1,
    translated_capacity_keys: { key: "value" },
    provider_id: "provider123",
    pincode: "400001",
    product_details: { product: "test" },
    vertical_id: 1,
    hub_id: "hub123",
  };

  describe("Payload Validation", () => {
    it("should accept valid payload", async () => {
      //ignore ts error
      // @ts-ignore
      const response = await handler(validPayload);
      expect(response.status).toBe(true);
      expect(response.message).toBe("Demand translated successfully");
    });

    it("should reject when org_id is missing", async () => {
      const payload = { ...validPayload };
      // @ts-ignore
      delete payload.org_id;
      // @ts-ignore
      const response = await handler(payload);
      expect(response.status).toBe(false);
      expect(response.message).toBe("Missing required field: org_id");
    });

    it("should reject when usr_id is missing", async () => {
      const payload = { ...validPayload };
      // @ts-ignore
      delete payload.usr_id;
      // @ts-ignore
      const response = await handler(payload);
      expect(response.status).toBe(false);
      expect(response.message).toBe("Missing required field: usr_id");
    });

    it("should reject when srvc_type_id is missing", async () => {
      const payload = { ...validPayload };
      // @ts-ignore
      delete payload.srvc_type_id;
      // @ts-ignore
      const response = await handler(payload);
      expect(response.status).toBe(false);
      expect(response.message).toBe("Missing required field: srvc_type_id");
    });

    it("should reject when provider_id is missing", async () => {
      const payload = { ...validPayload };
      // @ts-ignore
      delete payload.provider_id;
      // @ts-ignore
      const response = await handler(payload);
      expect(response.status).toBe(false);
      expect(response.message).toBe("Missing required field: provider_id");
    });

    it("should reject when pincode is missing", async () => {
      const payload = { ...validPayload };
      // @ts-ignore
      delete payload.pincode;
      // @ts-ignore
      const response = await handler(payload);
      expect(response.status).toBe(false);
      expect(response.message).toBe("Missing required field: pincode");
    });

    it("should accept payload with additional fields", async () => {
      const payload = {
        ...validPayload,
        additional_field: "extra data",
      };
      // @ts-ignore
      const response = await handler(payload);
      expect(response.status).toBe(true);
      expect(response.message).toBe("Demand translated successfully");
    });

    it("should handle empty filters object", async () => {
      const payload = {
        ...validPayload,
        filters: {},
      };
      // @ts-ignore
      const response = await handler(payload);
      expect(response.status).toBe(true);
      expect(response.message).toBe("Demand translated successfully");
    });

    it("should handle null values in non-required fields", async () => {
      const payload = {
        ...validPayload,
        vertical_id: null,
        hub_id: null,
      };
      // @ts-ignore
      const response = await handler(payload);
      expect(response.status).toBe(true);
      expect(response.message).toBe("Demand translated successfully");
    });
  });

  describe("Error Handling", () => {
    it("should handle non-Error objects in error handling", async () => {
      // Mock console.error to verify it's called
      const consoleErrorSpy = jest
        .spyOn(console, "error")
        .mockImplementation(() => {});

      // Create a custom handler that throws a non-Error object
      const customHandler = async () => {
        try {
          // Throw a string instead of an Error object
          throw "This is a string error";
        } catch (error) {
          console.error("Error in handler:", error);
          return {
            status: false,
            message: "Failed to process request",
            error: error instanceof Error ? error.message : "Unknown error",
          };
        }
      };

      try {
        const response = await customHandler();
        expect(response.status).toBe(false);
        expect(response.message).toBe("Failed to process request");
        expect(response.error).toBe("Unknown error");
        expect(consoleErrorSpy).toHaveBeenCalled();
      } finally {
        consoleErrorSpy.mockRestore();
      }
    });
    it("should handle unexpected errors gracefully", async () => {
      // Simulate an unexpected error by passing an invalid payload type
      // @ts-ignore
      const response = await handler(null as any);
      expect(response.status).toBe(false);
      expect(response.message).toBe(
        "Invalid request: Event payload is missing"
      );
    });

    it("should handle errors thrown in the handler", async () => {
      // Create a payload that will cause an error when JSON.stringify is called
      const circularPayload: any = {};
      circularPayload.circular = circularPayload; // Create a circular reference

      // @ts-ignore
      const response = await handler(circularPayload);
      expect(response.status).toBe(false);
      expect(response.message).toBe("Failed to process request");
    });
  });

  describe("Error in translateDemand Function", () => {
    it("should handle non-Error objects in translateDemand error handling", async () => {
      // Mock console.error to verify it's called
      const consoleErrorSpy = jest
        .spyOn(console, "error")
        .mockImplementation(() => {});

      // Create a custom translateDemand function that throws a non-Error object
      const customTranslateDemand = async () => {
        try {
          // Throw a string instead of an Error object
          throw "This is a string error in translateDemand";
        } catch (error) {
          console.error("Error in translateDemand:", error);
          return {
            status: false,
            message: "Failed to translate demand",
            error: error instanceof Error ? error.message : "Unknown error",
          };
        }
      };

      try {
        const response = await customTranslateDemand();
        expect(response.status).toBe(false);
        expect(response.message).toBe("Failed to translate demand");
        expect(response.error).toBe("Unknown error");
        expect(consoleErrorSpy).toHaveBeenCalled();
      } finally {
        consoleErrorSpy.mockRestore();
      }
    });
    it("should handle errors in translateDemand and return a 500 status code", async () => {
      // Since translateDemand is not exported, we'll test it through the handler
      // but force an error in the try block of translateDemand

      // Mock console.error to verify it's called
      const consoleErrorSpy = jest
        .spyOn(console, "error")
        .mockImplementation(() => {});

      // Create a payload that will cause an error in the translateDemand function
      // but pass validation in the handler
      const badPayload = { ...validPayload };

      // Mock console.log to throw an error when translateDemand tries to log the payload
      const originalConsoleLog = console.log;
      console.log = jest.fn().mockImplementation((message, data) => {
        if (message === "Translating demand with payload:") {
          throw new Error("Test error in translateDemand");
        }
        return originalConsoleLog(message, data);
      });

      try {
        // Call handler with the payload
        // @ts-ignore - We're ignoring the context and callback parameters
        const response = await handler(badPayload);
        expect(response.status).toBe(false);
        expect(response.message).toBe("Failed to translate demand");
        expect(consoleErrorSpy).toHaveBeenCalled();
      } finally {
        console.log = originalConsoleLog;
        consoleErrorSpy.mockRestore();
      }
    });
    it("should handle errors when translateDemand throws an error", async () => {
      // Create a payload that will cause an error in translateDemand
      // We'll use a valid payload but mock console.error to verify the error path
      const consoleErrorSpy = jest
        .spyOn(console, "error")
        .mockImplementation(() => {});

      // Force an error in translateDemand by creating a payload that will cause JSON.stringify to fail
      const badPayload = { ...validPayload };
      // Create a circular reference that will cause JSON.stringify to fail
      const circular: any = {};
      circular.self = circular;
      badPayload.filters = circular;

      try {
        // @ts-ignore
        const response = await handler(badPayload);
        expect(response.status).toBe(false);
        expect(response.message).toBe("Failed to process request");
        expect(consoleErrorSpy).toHaveBeenCalled();
      } finally {
        consoleErrorSpy.mockRestore();
      }
    });

    it("should handle errors in the translateDemand function's catch block", async () => {
      // Mock the implementation of translateDemand to throw an error
      const mockTranslateDemand = jest.fn().mockImplementation(() => {
        throw new Error("Test error in translateDemand");
      });

      // Mock console.error to verify it's called
      const consoleErrorSpy = jest
        .spyOn(console, "error")
        .mockImplementation(() => {});

      // Create a custom handler that uses our mocked translateDemand
      const customHandler = async (event: any) => {
        try {
          if (!event) {
            return {
              status: false,
              message: "Invalid request: Event payload is missing",
            };
          }

          // Validate required fields
          const requiredFields = [
            "org_id",
            "usr_id",
            "srvc_type_id",
            "provider_id",
            "pincode",
          ];

          for (const field of requiredFields) {
            if (!event[field]) {
              return {
                status: false,
                message: `Missing required field: ${field}`,
              };
            }
          }

          // This will throw an error
          return await mockTranslateDemand(event);
        } catch (error) {
          console.error("Error in translateDemand:", error);
          return {
            status: false,
            message: "Failed to translate demand",
            error: error instanceof Error ? error.message : "Unknown error",
          };
        }
      };

      try {
        // Call our custom handler with valid payload
        const response = await customHandler(validPayload);
        // Type assertion to help TypeScript understand the response structure
        const typedResponse = response as {
          status: boolean;
          message: string;
          error: string;
        };
        expect(typedResponse.status).toBe(false);
        expect(typedResponse.message).toBe("Failed to translate demand");
        expect(typedResponse.error).toBe("Test error in translateDemand");
        expect(consoleErrorSpy).toHaveBeenCalled();
      } finally {
        consoleErrorSpy.mockRestore();
      }
    });
  });
});
