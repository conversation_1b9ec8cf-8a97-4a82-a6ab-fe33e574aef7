import express, { Request, Response, NextFunction } from "express";
import cors from "cors";
import bodyParser from "body-parser";
import swaggerUi from "swagger-ui-express";
// Import modules
import swaggerSpec from "./config/swagger";
import routes from "./routes/index";
import { initBigIntSerializer } from "./utils/bigint-serializer";
import * as database from "./config/database";
import { sequelize } from "./models";

// Initialize BigInt serializer for JSON responses
initBigIntSerializer();

// Initialize Express app
export const app = express();

// Create a promise to track database connection status
let dbConnectionPromise: Promise<void>;

// Function to wait for database connection
export const waitForDatabaseConnection = async (): Promise<void> => {
  if (!dbConnectionPromise) {
    dbConnectionPromise = database.testConnection();
  }
  return dbConnectionPromise;
};

// Test database connection
dbConnectionPromise = database
  .testConnection()
  .then(() => {
    console.log("🚀 Application ready to serve requests");
  })
  .catch((err: Error) => {
    console.error("❌ Database initialization failed:", err);
    throw err; // Re-throw to ensure promise rejection
  });

// Ensure Sequelize disconnects on app termination
process.on("beforeExit", async () => {
  await sequelize.close();
});

// Middleware
app.use(cors());

// Increase body-parser limits to handle large payloads (10MB)
app.use(bodyParser.json({ limit: "10mb" }));
app.use(bodyParser.urlencoded({ extended: true, limit: "10mb" }));

// Swagger documentation
app.use("/api-docs", swaggerUi.serve, swaggerUi.setup(swaggerSpec));

// Register routes
app.use(routes);

// Default route
app.get("/", (_req: Request, res: Response) => {
  res.redirect("/api-docs");
});

// Error handling middleware
app.use((err: Error, _req: Request, res: Response, _next: NextFunction) => {
  console.error("Unhandled error:", err);
  res.status(500).json({
    status: false,
    message: "Internal server error",
    timestamp: new Date().toISOString(),
  });
});
