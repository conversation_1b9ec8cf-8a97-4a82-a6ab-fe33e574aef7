const axios = require("axios");

const API_URL = "https://script.google.com/macros/s/AKfycbx3O_znoIADsviuIx7v82D6Efgd3aU6XVA4tfXM9TMPx4caNsrr_MYQhAOotB6nbQtoCw/exec";

const handler = async (event) => {
  const { payload } = event;
  const tms_order_id = payload?.tms_order_id;
  const tms_order_status = payload?.tms_order_status?.label;

  const postData = {
    job_id: tms_order_id,
    status: tms_order_status,
  };

  try {
    const response = await axios.post(API_URL, postData, {
      headers: {
        "Content-Type": "application/json",
      },
    });
    console.log(
      "tms-track-job-status-updates :: response data :: tmsId :: status :: ", response.data, postData.job_id, postData.status
    );

    return {
      statusCode: response.status,
      body: response.data,
    };
  } catch (error) {
    console.log(
      "tms-track-job-status-updates :: error :: tmsId :: status :: ", error?.message, postData.job_id, postData.status
    );

    return {
      statusCode: error.response ? error.response.status : 500,
      body: {
        message: "Failed to send job status update.",
        error: error.message,
      },
    };
  }
};

exports.handler = handler;
