{"name": "tms-services-bookings-m-dev", "version": "1.0.0", "description": "Bookings service lambda function for TMS (Development)", "main": "dist/index.js", "scripts": {"start": "ts-node src/localRunner.ts", "dev": "nodemon --exec ts-node src/localRunner.ts", "test": "jest", "test:simple": "jest --no-coverage", "test:watch": "jest --watchAll --no-coverage", "test:api": "npm run build && node test-api.js", "build": "tsc", "build:watch": "tsc --watch", "clean": "rm -rf dist", "prebuild": "npm run clean"}, "keywords": ["lambda", "bookings", "tms"], "author": "Wify", "license": "ISC", "dependencies": {"aws-serverless-express": "^3.4.0", "body-parser": "^2.2.0", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^5.1.0", "pg": "^8.15.5", "sequelize": "^6.37.7", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.1"}, "devDependencies": {"@types/aws-lambda": "^8.10.149", "@types/aws-serverless-express": "^3.3.10", "@types/body-parser": "^1.19.5", "@types/cors": "^2.8.17", "@types/express": "^5.0.1", "@types/jest": "^29.5.14", "@types/pg": "^8.11.13", "@types/supertest": "^6.0.3", "@types/swagger-jsdoc": "^6.0.4", "@types/swagger-ui-express": "^4.1.8", "formidable": "^3.5.3", "jest": "^29.7.0", "nodemon": "^3.1.9", "supertest": "^7.1.0", "ts-jest": "^29.3.2", "ts-node": "^10.9.2", "typescript": "^5.8.3"}}