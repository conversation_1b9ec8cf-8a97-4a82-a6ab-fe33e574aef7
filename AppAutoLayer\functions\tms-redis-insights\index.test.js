const { 
  extractPattern, 
  determineAndCountPatterns, 
  getRedisMemoryInfo, 
  getRedisGeneralInfo,
  getSampleKeys,
  createRedisClient,
  handler
} = require("./index");

// Mock Redis for unit tests
const mockRedis = {
  scan: jest.fn(),
  info: jest.fn(),
  connect: jest.fn(),
  disconnect: jest.fn(),
  status: 'ready'
};

describe("TMS Redis Insights Lambda", () => {
  
  describe("Unit Tests", () => {
    
    describe("extractPattern", () => {
      it("should extract pattern from key with underscore", () => {
        expect(extractPattern("user_123")).toBe("user");
        expect(extractPattern("session_abc_def")).toBe("session");
        expect(extractPattern("cache_data_456")).toBe("cache");
      });

      it("should return original key if no underscore", () => {
        expect(extractPattern("simplekey")).toBe("simplekey");
        expect(extractPattern("123")).toBe("123");
      });

      it("should handle empty string", () => {
        expect(extractPattern("")).toBe("");
      });

      it("should handle keys starting with underscore", () => {
        expect(extractPattern("_private_key")).toBe("");
        expect(extractPattern("_")).toBe("");
      });
    });

    describe("determineAndCountPatterns", () => {
      beforeEach(() => {
        jest.clearAllMocks();
      });

      it("should count patterns correctly", async () => {
        // Mock Redis scan responses
        mockRedis.scan
          .mockResolvedValueOnce(['10', ['user_1', 'user_2', 'session_a']])
          .mockResolvedValueOnce(['0', ['cache_x', 'user_3']]);

        const result = await determineAndCountPatterns(mockRedis);

        expect(result.totalKeys).toBe(5);
        expect(result.uniquePatterns).toBe(3);
        expect(result.patterns.user).toBe(3);
        expect(result.patterns.session).toBe(1);
        expect(result.patterns.cache).toBe(1);
        expect(result.timestamp).toBeDefined();
        
        // Verify patterns are sorted by count (descending)
        const patternEntries = Object.entries(result.patterns);
        expect(patternEntries[0]).toEqual(['user', 3]);
        expect(patternEntries[1]).toEqual(['session', 1]);
        expect(patternEntries[2]).toEqual(['cache', 1]);
      });

      it("should handle empty Redis", async () => {
        mockRedis.scan.mockResolvedValueOnce(['0', []]);

        const result = await determineAndCountPatterns(mockRedis);

        expect(result.totalKeys).toBe(0);
        expect(result.uniquePatterns).toBe(0);
        expect(result.patterns).toEqual({});
      });

      it("should handle Redis scan error", async () => {
        mockRedis.scan.mockRejectedValueOnce(new Error("Redis connection failed"));

        await expect(determineAndCountPatterns(mockRedis)).rejects.toThrow("Redis connection failed");
      });

      it("should handle large datasets with progress logging", async () => {
        // Mock console.log to verify progress logging
        const consoleSpy = jest.spyOn(console, 'log').mockImplementation();
        
        // Create a large dataset
        const largeKeySet = Array.from({length: 15000}, (_, i) => `user_${i}`);
        
        mockRedis.scan
          .mockResolvedValueOnce(['10', largeKeySet.slice(0, 10000)])
          .mockResolvedValueOnce(['0', largeKeySet.slice(10000)]);

        const result = await determineAndCountPatterns(mockRedis);

        expect(result.totalKeys).toBe(15000);
        expect(result.patterns.user).toBe(15000);
        
        // Verify progress was logged
        expect(consoleSpy).toHaveBeenCalledWith('Processed 10000 keys so far...');
        
        consoleSpy.mockRestore();
      });
    });

    describe("getRedisMemoryInfo", () => {
      beforeEach(() => {
        jest.clearAllMocks();
      });

      it("should parse memory info correctly", async () => {
        const mockMemoryInfo = "used_memory:1024\r\nused_memory_human:1K\r\nused_memory_rss:2048\r\nother_stat:value\r\n";
        mockRedis.info.mockResolvedValueOnce(mockMemoryInfo);

        const result = await getRedisMemoryInfo(mockRedis);

        expect(result.used_memory).toBe("1024");
        expect(result.used_memory_human).toBe("1K");
        expect(result.used_memory_rss).toBe("2048");
        expect(result.other_stat).toBeUndefined();
      });

      it("should handle info error gracefully", async () => {
        mockRedis.info.mockRejectedValueOnce(new Error("Info command failed"));

        const result = await getRedisMemoryInfo(mockRedis);

        expect(result).toEqual({});
      });

      it("should handle malformed info response", async () => {
        mockRedis.info.mockResolvedValueOnce("malformed_response_without_colons");

        const result = await getRedisMemoryInfo(mockRedis);

        expect(result).toEqual({});
      });
    });

    describe("getRedisGeneralInfo", () => {
      beforeEach(() => {
        jest.clearAllMocks();
      });

      it("should parse server info correctly", async () => {
        const mockServerInfo = "redis_version:6.2.0\r\nuptime_in_seconds:3600\r\nuptime_in_days:1\r\nother_info:value\r\n";
        mockRedis.info.mockResolvedValueOnce(mockServerInfo);

        const result = await getRedisGeneralInfo(mockRedis);

        expect(result.redis_version).toBe("6.2.0");
        expect(result.uptime_in_seconds).toBe("3600");
        expect(result.uptime_in_days).toBe("1");
        expect(result.other_info).toBeUndefined();
      });

      it("should handle server info error gracefully", async () => {
        mockRedis.info.mockRejectedValueOnce(new Error("Server info failed"));

        const result = await getRedisGeneralInfo(mockRedis);

        expect(result).toEqual({});
      });
    });

    describe("getSampleKeys", () => {
      beforeEach(() => {
        jest.clearAllMocks();
      });

      it("should collect sample keys for each pattern", async () => {
        const patterns = { user: 5, session: 3, cache: 2 };
        
        mockRedis.scan
          .mockResolvedValueOnce(['10', ['user_1', 'user_2', 'session_a']])
          .mockResolvedValueOnce(['0', ['cache_x', 'user_3', 'session_b']]);

        const result = await getSampleKeys(mockRedis, patterns, 2);

        expect(result.user).toEqual(['user_1', 'user_2']);
        expect(result.session).toEqual(['session_a', 'session_b']);
        expect(result.cache).toEqual(['cache_x']);
      });

      it("should handle sample collection error gracefully", async () => {
        const patterns = { user: 5 };
        mockRedis.scan.mockRejectedValueOnce(new Error("Scan failed"));

        const result = await getSampleKeys(mockRedis, patterns);

        expect(result).toEqual({});
      });
    });
  });

  describe("Integration Tests", () => {
    
    describe("handler", () => {
      let mockCreateRedisClient;
      
      beforeEach(() => {
        jest.clearAllMocks();
        
        // Mock the createRedisClient function
        mockCreateRedisClient = jest.fn().mockReturnValue(mockRedis);
        
        // Replace the original function
        const originalModule = require('./index');
        originalModule.createRedisClient = mockCreateRedisClient;
      });

      it("should return successful response with complete data", async () => {
        // Setup mocks for successful execution
        mockRedis.connect.mockResolvedValueOnce();
        mockRedis.scan
          .mockResolvedValueOnce(['10', ['user_1', 'session_a']])
          .mockResolvedValueOnce(['0', ['cache_x']]);
        mockRedis.info
          .mockResolvedValueOnce("used_memory:1024\r\nused_memory_human:1K")
          .mockResolvedValueOnce("redis_version:6.2.0\r\nuptime_in_seconds:3600");
        mockRedis.disconnect.mockResolvedValueOnce();

        const event = { includeSamples: true };
        const result = await handler(event);

        expect(result.statusCode).toBe(200);
        expect(result.body.success).toBe(true);
        expect(result.body.data.patternAnalysis).toBeDefined();
        expect(result.body.data.memoryInfo).toBeDefined();
        expect(result.body.data.serverInfo).toBeDefined();
        expect(result.body.data.sampleKeys).toBeDefined();
        expect(result.body.data.analysisTimestamp).toBeDefined();
      });

      it("should handle Redis connection error", async () => {
        mockRedis.connect.mockRejectedValueOnce(new Error("Connection failed"));

        const result = await handler({});

        expect(result.statusCode).toBe(500);
        expect(result.body.success).toBe(false);
        expect(result.body.error).toBe("Connection failed");
      });

      it("should exclude samples when requested", async () => {
        mockRedis.connect.mockResolvedValueOnce();
        mockRedis.scan.mockResolvedValueOnce(['0', ['user_1']]);
        mockRedis.info
          .mockResolvedValueOnce("used_memory:1024")
          .mockResolvedValueOnce("redis_version:6.2.0");
        mockRedis.disconnect.mockResolvedValueOnce();

        const event = { includeSamples: false };
        const result = await handler(event);

        expect(result.statusCode).toBe(200);
        expect(result.body.data.sampleKeys).toBeUndefined();
      });

      it("should always disconnect from Redis", async () => {
        mockRedis.connect.mockResolvedValueOnce();
        mockRedis.scan.mockRejectedValueOnce(new Error("Scan failed"));
        mockRedis.disconnect.mockResolvedValueOnce();

        await handler({});

        expect(mockRedis.disconnect).toHaveBeenCalled();
      });
    });
  });
});
