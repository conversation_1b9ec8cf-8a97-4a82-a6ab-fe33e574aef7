/**
 * Utility to handle BigInt serialization in JSON responses
 */

// Add BigInt serialization support
export const initBigIntSerializer = (): void => {
  // Add toJSON method to BigInt prototype to make it serializable
  // This converts BigInt to a string when JSON.stringify is called
  (BigInt.prototype as any).toJSON = function() {
    return this.toString();
  };
};

/**
 * Converts BigInt values in an object to strings
 * @param obj Object that may contain BigInt values
 * @returns Object with BigInt values converted to strings
 */
export const serializeBigInt = <T>(obj: T): T => {
  if (obj === null || obj === undefined) {
    return obj;
  }

  if (typeof obj === 'bigint') {
    return obj.toString() as unknown as T;
  }

  if (Array.isArray(obj)) {
    return obj.map(serializeBigInt) as unknown as T;
  }

  if (typeof obj === 'object') {
    const result: Record<string, any> = {};
    for (const [key, value] of Object.entries(obj)) {
      result[key] = serializeBigInt(value);
    }
    return result as T;
  }

  return obj;
};
