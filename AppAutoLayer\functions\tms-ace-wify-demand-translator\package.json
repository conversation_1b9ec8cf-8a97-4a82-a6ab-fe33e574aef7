{"name": "tms-dev-wify-ace-demand-translator", "version": "1.0.0", "description": "Lambda function for translating ACE demand", "main": "dist/index.js", "scripts": {"build": "tsc --watch", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "watch": "tsc -w"}, "dependencies": {"aws-lambda": "^1.0.7"}, "devDependencies": {"@jest/globals": "^29.7.0", "@types/aws-lambda": "^8.10.92", "@types/jest": "^27.4.0", "@types/node": "^17.0.13", "jest": "^27.4.7", "ts-jest": "^27.1.3", "typescript": "^4.5.5"}}