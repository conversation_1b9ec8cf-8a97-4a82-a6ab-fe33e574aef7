const axios = require("axios");

const HOME_ESSENTIALS_URL =
  "https://homeessentials.app/restapi/wify/e8ad22410a0a11efb65e0236e86f1e4b";

exports.handler = async (event) => {
  const { tms_order_id, tms_order_status } = event.payload;

  try {
    const headers = {
      "Content-Type": "application/json",
    };

    const payload = {
      ticket_details: {
        ticket_id: tms_order_id,
        current_ticket_status: {
          label: tms_order_status?.label,
          status_type: tms_order_status?.status_type,
          transition_millis: tms_order_status?.transition_millis,
          transition_timestamp: tms_order_status?.transition_timestamp,
        },
      },
    };

    const response = await axios.post(
      HOME_ESSENTIALS_URL,
      JSON.stringify(payload),
      {
        headers: headers,
      }
    );
    const responseData = {
      ...response.data,
      status: true,
    };
    console.log("responseData :: ", responseData);
    return responseData;
  } catch (error) {
    console.log("handler :: error :: ", error);
    return {
      status: false,
      message: error?.message || "Error in calling Home Essentials api",
    };
  }
};
