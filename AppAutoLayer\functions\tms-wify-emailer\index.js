const { Client } = require("pg");
const { sendEmail } = require("./emailService");
const { generateDataTableHTML, getISTTimestamp } = require("./htmlTemplate");
require("dotenv").config(); // Load environment variables

async function getRawDataForACEImplementationProg() {
  // Return some hardcoded data
  const client = new Client({
    host: process.env.DB_HOST,
    database: process.env.DB_NAME,
    user: process.env.DB_USER,
    password: process.env.DB_PASSWORD,
    port: process.env.DB_PORT, // default PostgreSQL port
  });

  await client.connect();

  const res = await client.query(
    `
      select vertical.db_id as "Vertical ID",
             vertical.settings_data->'vertical_title' as "Title",
             count(distinct srvc_hub.id) as "Tot. service hubs",
             count(distinct srvc_hub.id) filter(
                where
                    (srvc_hub.c_meta).time::timestamp >= CURRENT_DATE
                and (srvc_hub.c_meta).time::timestamp < CURRENT_DATE + INTERVAL '1 day'
             ) as "New hubs created",
             (
                SELECT COUNT(DISTINCT srvc_req.db_id) 
                FROM cl_tx_srvc_req srvc_req 
                WHERE srvc_req.srvc_prvdr = 2 
                AND srvc_req.prvdr_vertical = vertical.db_id
                AND srvc_req.is_deleted IS NOT TRUE
                AND srvc_req.prvdr_srvc_hub IS null
             ) AS "Orders without a service hub",
             (
                SELECT COUNT(DISTINCT vert_skill.skill_id) 
                FROM cl_tx_skill_map vert_skill 
                WHERE vert_skill.vertical_id = vertical.db_id
             ) AS "Tot. Skills",
             (
                SELECT COUNT(DISTINCT cust_timeslot.db_id) 
                FROM cl_tx_vertical_time_slots cust_timeslot 
                WHERE cust_timeslot.vertical_id = vertical.db_id
             ) AS "Tot. Timeslots",
             (
                SELECT COUNT(DISTINCT tech.usr_id) 
                FROM cl_tx_users tech 
                WHERE tech.primary_vertical = vertical.db_id
                AND tech.primary_srvc_hub IS NOT NULL
                AND tech.primary_skill1 IS NOT null
             ) AS "Tot. Technicians"
        from cl_tx_orgs_settings as vertical
        left join cl_tx_vertical_srvc_hubs srvc_hub 
          on srvc_hub.vertical_id = vertical.db_id
       where vertical.settings_type = 'SP_CUSTOM_FIELDS'
         and vertical.org_id = 2
         and vertical.db_id <> 2
       group by vertical.db_id
    `
  ); // Replace with your actual query
  await client.end();
  const resp = res.rows;
  // console.log("sbtsks_data", resp);
  return resp;
}

exports.handler = async (event) => {
  try {
    console.log("TmsWifyEmailer::handler:: Starting email process");

    // Get data from database
    const rawDataACEImpProg = await getRawDataForACEImplementationProg();
    console.log(
      "TmsWifyEmailer::handler:: Retrieved data rows:",
      rawDataACEImpProg.length
    );

    // Generate HTML content from the data
    const htmlContent = generateDataTableHTML(rawDataACEImpProg, {
      title: "ACE Implementation Progress Report",
      description: "Vertical-wise implementation progress and statistics",
    });

    // Email configuration
    const emailConfig = {
      to: process.env.EMAIL_RECIPIENTS, // Set recipients in env
      subject: "ACE Implementation Progress Report - Day -" + getISTTimestamp(),
      htmlContent: htmlContent,
      cc: process.env.EMAIL_CC || "", // Optional CC recipients
      bcc: process.env.EMAIL_BCC || "", // Optional BCC recipients
    };

    // Send email
    console.log("TmsWifyEmailer::handler:: Sending email to:", emailConfig.to);
    const emailResult = await sendEmail(emailConfig);

    if (emailResult.status === "Success") {
      console.log("TmsWifyEmailer::handler:: Email sent successfully");
      return {
        statusCode: 200,
        body: JSON.stringify({
          message: "Email sent successfully",
          messageId: emailResult.messageId,
          recordsCount: rawDataACEImpProg.length,
        }),
      };
    } else {
      console.error(
        "TmsWifyEmailer::handler:: Email send failed:",
        emailResult.error
      );
      return {
        statusCode: 500,
        body: JSON.stringify({
          message: "Email send failed",
          error: emailResult.error,
        }),
      };
    }
  } catch (error) {
    console.error("TmsWifyEmailer::handler:: Error in handler:", error);
    return {
      statusCode: 500,
      body: JSON.stringify({
        message: "Internal server error",
        error: error.message,
      }),
    };
  }
};
