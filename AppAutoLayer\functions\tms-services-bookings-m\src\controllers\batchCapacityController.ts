import { Request, Response } from "express";
import { Capacity, sequelize } from "../models";

/**
 * Interface for capacity record in batch request
 */
interface CapacityRecord {
  resourceId: string;
  startTime: string;
  endTime: string;
  totalCapacity: number;
  availableCapacity?: number;
  bookedCapacity?: number;
  organizationId?: string;
  organizationName?: string;
  metadata?: {
    verticalId?: string;
    skillId?: string;
    hubId?: string;
    providerId?: string;
    [key: string]: any;
  };
}

/**
 * Interface for batch request body
 */
interface BatchCapacityRequest {
  capacityRecords: CapacityRecord[];
  organizationId?: string;
  organizationName?: string;
}

/**
 * Interface for failed record in response
 */
interface FailedRecord {
  resourceId: string;
  startTime: string;
  endTime: string;
  message: string;
  statusCode: number;
  errorDetails?: {
    error: string;
    code?: string;
  };
}

/**
 * Interface for validated capacity record
 */
interface ValidatedCapacityRecord {
  resourceId: string;
  startTime: Date;
  endTime: Date;
  day?: string; // YYYY-MM-DD format (optional, will be set by hooks)
  totalCapacity: number;
  availableCapacity: number;
}

/**
 * Interface for validation results
 */
interface ValidationResults {
  successCount: number;
  failureCount: number;
  failedRecords: FailedRecord[];
}

/**
 * Validates a single capacity record
 * @param record The capacity record to validate
 * @param results The results object to update with validation errors
 * @returns The validated record if valid, null if invalid
 */
const validateCapacityRecord = (
  record: CapacityRecord,
  results: ValidationResults
): ValidatedCapacityRecord | null => {
  try {
    // Validate required fields
    if (
      !record.resourceId ||
      !record.startTime ||
      !record.endTime ||
      record.totalCapacity === undefined
    ) {
      results.failureCount++;
      results.failedRecords.push({
        resourceId: record.resourceId || "unknown",
        startTime: record.startTime || "unknown",
        endTime: record.endTime || "unknown",
        message:
          "Missing required fields: resourceId, startTime, endTime, totalCapacity",
        statusCode: 400,
        errorDetails: {
          error: "Missing required fields",
          code: "MISSING_FIELDS",
        },
      });
      return null;
    }

    // Validate capacity value
    if (record.totalCapacity < 0) {
      results.failureCount++;
      results.failedRecords.push({
        resourceId: record.resourceId,
        startTime: record.startTime,
        endTime: record.endTime,
        message: "Total capacity must be a non-negative integer",
        statusCode: 400,
        errorDetails: {
          error: "Invalid capacity value",
          code: "INVALID_CAPACITY",
        },
      });
      return null;
    }

    // Validate time range
    const startDate = new Date(record.startTime);
    const endDate = new Date(record.endTime);

    if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
      results.failureCount++;
      results.failedRecords.push({
        resourceId: record.resourceId,
        startTime: record.startTime,
        endTime: record.endTime,
        message: "Invalid date format for startTime or endTime",
        statusCode: 400,
        errorDetails: {
          error: "Invalid date format",
          code: "INVALID_DATE",
        },
      });
      return null;
    }

    if (startDate >= endDate) {
      results.failureCount++;
      results.failedRecords.push({
        resourceId: record.resourceId,
        startTime: record.startTime,
        endTime: record.endTime,
        message: "startTime must be before endTime",
        statusCode: 400,
        errorDetails: {
          error: "Invalid time range",
          code: "INVALID_TIME_RANGE",
        },
      });
      return null;
    }

    // Record is valid, return the validated record
    return {
      resourceId: record.resourceId,
      startTime: startDate,
      endTime: endDate,
      // No need to set day field, the beforeCreate/beforeBulkCreate hook will handle it
      totalCapacity: record.totalCapacity,
      availableCapacity: record.availableCapacity || record.totalCapacity,
    };
  } catch (validationError: any) {
    results.failureCount++;
    results.failedRecords.push({
      resourceId: record.resourceId || "unknown",
      startTime: record.startTime || "unknown",
      endTime: record.endTime || "unknown",
      message: `Validation error: ${
        validationError.message || "Unknown error"
      }`,
      statusCode: 400,
      errorDetails: {
        error: validationError.message || "Unknown error",
        code: "VALIDATION_ERROR",
      },
    });
    return null;
  }
};

/**
 * Validates all capacity records in a batch
 * @param capacityRecords The array of capacity records to validate
 * @returns An object containing validation results and valid records
 */
const validateCapacityBatch = (capacityRecords: CapacityRecord[]) => {
  const results: ValidationResults = {
    successCount: 0,
    failureCount: 0,
    failedRecords: [],
  };

  const validRecords: ValidatedCapacityRecord[] = [];

  // Validate each record
  for (const record of capacityRecords) {
    const validatedRecord = validateCapacityRecord(record, results);
    if (validatedRecord) {
      validRecords.push(validatedRecord);
    }
  }

  return { results, validRecords };
};

/**
 * Processes capacity records in bulk using a single optimized database operation
 * @param validRecords Array of validated capacity records
 * @param transaction The Sequelize transaction
 * @returns The number of successfully processed records
 */
const processBulkCapacityRecords = async (
  validRecords: ValidatedCapacityRecord[],
  transaction: any
): Promise<number> => {
  try {
    console.log(
      `[DB DEBUG] processBulkCapacityRecords called with ${validRecords.length} records`
    );
    console.log(`[DB DEBUG] Transaction object type: ${typeof transaction}`);

    // Prepare all records for a single bulk operation
    const records = validRecords.map((record) => ({
      resourceId: record.resourceId,
      startTime: record.startTime,
      endTime: record.endTime,
      // No need to include day field, the beforeBulkCreate hook will handle it
      totalCapacity: record.totalCapacity,
      availableCapacity: record.availableCapacity,
    }));

    console.log(
      `[DB DEBUG] Prepared ${records.length} records for bulk operation`
    );
    if (records.length > 0) {
      console.log(
        `[DB DEBUG] First record sample: ${JSON.stringify(records[0])}`
      );
    }

    // Perform a single bulkCreate operation with updateOnDuplicate option
    // This will insert new records and update existing ones in a single operation
    console.log(`[DB DEBUG] Executing bulkCreate with updateOnDuplicate`);
    const result = await Capacity.bulkCreate(records, {
      transaction,
      updateOnDuplicate: ["totalCapacity", "availableCapacity", "updatedAt"],
    });

    console.log(
      `[DB DEBUG] bulkCreate operation completed with ${result.length} records processed`
    );
    console.log(`[DB DEBUG] All database operations completed successfully`);
    return validRecords.length;
  } catch (error) {
    console.error(`[DB DEBUG] Error in processBulkCapacityRecords:`, error);
    throw error;
  }
};

/**
 * Process batch capacity updates in a single transaction
 * @param req Request
 * @param res Response
 */
export const batchAddCapacity = async (
  req: Request,
  res: Response
): Promise<void> => {
  try {
    const { capacityRecords, organizationId } =
      req.body as BatchCapacityRequest;

    // Validate request
    if (
      !capacityRecords ||
      !Array.isArray(capacityRecords) ||
      capacityRecords.length === 0
    ) {
      res.status(400).json({
        status: false,
        message: "capacityRecords must be a non-empty array",
        timestamp: new Date().toISOString(),
      });
      return;
    }

    // Minimal logging for better performance
    if (capacityRecords.length > 1000) {
      console.log(
        `Processing large batch: ${capacityRecords.length} records for org ${
          organizationId || "unknown"
        }`
      );
    }

    // Step 1: Validate all records first before touching the database
    const { results, validRecords } = validateCapacityBatch(capacityRecords);

    // If any records failed validation, return immediately without touching the database
    if (results.failureCount > 0) {
      res.status(400).json({
        status: false,
        message: `Batch validation failed with ${results.failureCount} errors. No records were updated.`,
        data: results,
        timestamp: new Date().toISOString(),
      });
      return;
    }

    try {
      console.log(
        `[DB DEBUG] Starting transaction for ${validRecords.length} records`
      );

      // Log the first record for debugging (if available)
      if (validRecords.length > 0) {
        console.log(
          `[DB DEBUG] First record sample: ${JSON.stringify(validRecords[0])}`
        );
      }

      // Process valid records in bulk using a single Sequelize transaction
      let successCount = 0;
      await sequelize.transaction(async (transaction: any) => {
        const result = await processBulkCapacityRecords(
          validRecords,
          transaction
        );
        successCount = result;
      });

      console.log(
        `[DB DEBUG] Transaction completed with success count: ${successCount}`
      );

      // Update success count
      results.successCount = successCount;

      // Return success response
      res.status(200).json({
        status: true,
        message: `Successfully processed ${results.successCount} capacity records`,
        data: {
          totalRecords: capacityRecords.length,
          successCount: results.successCount,
          failureCount: results.failureCount,
        },
        timestamp: new Date().toISOString(),
      });
    } catch (dbError: any) {
      // Handle database operation error
      console.error("Database operation error:", dbError);
      res.status(500).json({
        status: false,
        message: `Database operation failed: ${
          dbError.message || "Unknown error"
        }`,
        error: dbError.message,
        timestamp: new Date().toISOString(),
      });
    }
  } catch (error: any) {
    // Handle general errors
    console.error("Error in batch capacity processing:", error);
    res.status(500).json({
      status: false,
      message: "Internal server error during batch processing",
      error: error.message || "Unknown error",
      timestamp: new Date().toISOString(),
    });
  }
};
