{"compilerOptions": {"target": "ES2018", "module": "commonjs", "lib": ["es2018", "dom"], "declaration": true, "strict": true, "noImplicitAny": true, "strictNullChecks": true, "noImplicitThis": true, "alwaysStrict": true, "noUnusedLocals": false, "noUnusedParameters": false, "noImplicitReturns": true, "noFallthroughCasesInSwitch": false, "inlineSourceMap": true, "inlineSources": true, "experimentalDecorators": true, "strictPropertyInitialization": false, "typeRoots": ["./node_modules/@types"], "outDir": "./dist", "esModuleInterop": true, "resolveJsonModule": true}, "exclude": ["node_modules", "dist"], "include": ["src/**/*"]}