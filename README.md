# :india: AWS Lambda Deployer with <PERSON><PERSON> and <PERSON><PERSON><PERSON>

![Lambda](https://img.shields.io/badge/AWS-Lambda-orange) ![Node](https://img.shields.io/badge/Node.js-16.x-blue)

## Thank You

Special thanks to **Gaurav Pangam** and **CHAT GPT 4** for their invaluable support and assistance in the development of this project.

## Introduction

This project automates the process of zipping and deploying AWS Lambda functions using an Express server. It leverages the AWS SDK and `adm-zip` library to handle the deployment process smoothly.

## Features

- **Automatic Zipping**: Automatically zip Lambda functions including the `node_modules` directory.
- **Express Integration**: Uses an Express server to trigger the deployment process.
- **AWS SDK v3**: Utilizes the latest AWS SDK v3 for Lambda deployments.
- **Environment Configuration**: Configured with `.env` for secure and flexible deployment.

## Prerequisites

Before you begin, ensure you have the following installed:

- Node.js (16.x or later)
- Yarn package manager
- AWS CLI configured with your credentials

## Installation

1. **Clone the repository**:

    ```sh
    git clone https://github.com/yourusername/aws-lambda-deployer.git
    cd aws-lambda-deployer
    ```

2. **Install dependencies**:

    ```sh
    yarn install
    ```

3. **Create and configure `.env` file**:

    ```sh
    touch .env
    ```

    Add your AWS credentials and configuration to the `.env` file:

    ```env
    ACCESS_KEY_ID=your-access-key-id
    SECRET_ACCESS_KEY=your-secret-access-key
    REGION=your-region
    SIGNATUREVERSION=v4
    ```

## Usage

1. **Organize your Lambda functions**:

    Place your Lambda functions inside the `lambdas` directory. Each Lambda function should be in its own subdirectory with a `node_modules` folder if necessary.

    ```
    .
    ├── lambdas
    │   ├── lambda1
    │   │   ├── index.js
    │   │   ├── node_modules
    │   │   └── package.json
    │   └── lambda2
    │       ├── index.js
    │       ├── node_modules
    │       └── package.json
    ├── .env
    ├── app.js
    ├── package.json
    └── README.md
    ```

2. **Start the server for development**:

    ```sh
    yarn develop -your_custom_argument
    ```

    This will start the server with `nodemon` for automatic restarts during development. Ensure you pass the required custom argument.

3. **Deploy the application**:

    ```sh
    yarn deploy -your_custom_argument
    ```

    This will start the application without `nodemon` for deployment purposes. Ensure you pass the required custom argument.

## Code Overview

### app.js

This file contains the core logic for the project. It initializes the Express server, reads the Lambda functions from the `lambdas` directory, zips them using `adm-zip`, and uploads them to AWS Lambda using the AWS SDK.

If the required custom argument is not provided, the application will exit with an error message and a non-zero status code to prevent automatic restarts during development.

---

This README now includes instructions for using the `develop` and `deploy` scripts with the necessary custom arguments.