import { Model, DataTypes, Optional } from "sequelize";
import sequelize from "../config/sequelize";

// Define the attributes of the Capacity model
interface CapacityAttributes {
  id: bigint;
  resourceId: string;
  startTime: Date;
  endTime: Date;
  day: string; // YYYY-MM-DD format
  totalCapacity: number;
  availableCapacity: number;
  createdAt: Date;
  updatedAt: Date;
}

// Define the attributes that can be null during creation
interface CapacityCreationAttributes
  extends Optional<
    CapacityAttributes,
    "id" | "createdAt" | "updatedAt" | "day"
  > {}

// Define the Capacity model class
class Capacity
  extends Model<CapacityAttributes, CapacityCreationAttributes>
  implements CapacityAttributes
{
  public id!: bigint;
  public resourceId!: string;
  public startTime!: Date;
  public endTime!: Date;
  public day!: string;
  public totalCapacity!: number;
  public availableCapacity!: number;
  public createdAt!: Date;
  public updatedAt!: Date;
}

// Initialize the Capacity model
Capacity.init(
  {
    id: {
      type: DataTypes.BIGINT,
      primaryKey: true,
      autoIncrement: true,
    },
    resourceId: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    startTime: {
      type: "timestamp",
      allowNull: false,
    },
    endTime: {
      type: "timestamp",
      allowNull: false,
    },
    day: {
      type: DataTypes.DATEONLY,
      allowNull: true,
      // This will be overridden by the PostgreSQL GENERATED column
      // but we need to define it for Sequelize model validation
    },
    totalCapacity: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    availableCapacity: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
    },
  },
  {
    sequelize,
    modelName: "Capacity",
    tableName: "Capacities",
    timestamps: true,
    indexes: [
      {
        unique: true,
        fields: ["resourceId", "startTime", "endTime"],
      },
      {
        // Index for resourceId and day combination for faster queries
        fields: ["resourceId", "day"],
      },
    ],
    hooks: {
      beforeCreate: (capacity) => {
        // Set day value from startTime before creating a new record
        if (capacity.startTime) {
          capacity.day = capacity.startTime.toISOString().split("T")[0];
        }
      },
      beforeUpdate: (capacity) => {
        // Set day value from startTime before updating an existing record
        if (capacity.startTime) {
          capacity.day = capacity.startTime.toISOString().split("T")[0];
        }
      },
      beforeBulkCreate: (instances) => {
        // Set day value for each instance in bulk create
        instances.forEach((capacity) => {
          if (capacity.startTime) {
            capacity.day = capacity.startTime.toISOString().split("T")[0];
          }
        });
      },
      beforeBulkUpdate: () => {
        // For bulk updates, we can't easily set the day value
        // We'll rely on the individual beforeUpdate hooks that will run for each record
        // or manual setting in the controller
      },
    },
  }
);

export default Capacity;
