/**
 * Comprehensive API Test Script for Capacity Service
 *
 * This script tests all the capacity service API endpoints using supertest.
 * Run with: node test-api.js
 */
const { app, waitForDatabaseConnection } = require("./dist/app");
const request = require("supertest");

// API key for authentication
const API_KEY = "tms-bookings-service-dev-key-123456";

// Helper function to print test results
const printResult = (testName, response, expectedStatus) => {
  const status = response.status;
  const isSuccess = status === expectedStatus;

  console.log(isSuccess ? `✅ PASS: ${testName}` : `❌ FAIL: ${testName}`);

  // Only show detailed response for failures
  if (!isSuccess) {
    console.log(`  Status: ${status} (Expected: ${expectedStatus})`);
    console.log(`  Response:`, response.body);
  }

  return isSuccess;
};

// Run tests
async function runTests() {
  // Wait for database connection to be established before running tests
  console.log("🔄 Initializing test environment...");
  await waitForDatabaseConnection();
  console.log("🚀 Starting API tests...");
  console.log("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");

  let capacityId;
  let testsPassed = 0;
  let testsFailed = 0;

  try {
    // Test 1: Health endpoint
    console.log("🔍 Test 1: Health endpoint");
    const healthResponse = await request(app)
      .get("/health")
      .set("x-api-key", API_KEY);

    if (printResult("Health endpoint", healthResponse, 200)) {
      testsPassed++;
    } else {
      testsFailed++;
    }

    // Test 2: Auth endpoint
    console.log("🔍 Test 2: Auth endpoint");
    const authResponse = await request(app)
      .get("/test-credentials")
      .set("x-api-key", API_KEY);

    if (printResult("Auth endpoint", authResponse, 200)) {
      testsPassed++;
    } else {
      testsFailed++;
    }

    // Test 3: Add a new capacity
    console.log("🔍 Test 3: Add a new capacity");
    // Use a unique resource ID with timestamp to ensure it's always new
    const uniqueResourceId = `test-resource-${Date.now()}`;
    const capacityData = {
      resourceId: uniqueResourceId,
      startTime: "2023-08-01T09:00:00.000Z",
      endTime: "2023-08-01T17:00:00.000Z",
      totalCapacity: 10,
    };

    const capacityResponse = await request(app)
      .post("/capacity")
      .set("x-api-key", API_KEY)
      .send(capacityData);

    // Accept either 201 (created) or 200 (updated) as success
    const expectedStatus = 201; // We expect 201 since we're using a unique resource ID
    if (printResult("Add a new capacity", capacityResponse, expectedStatus)) {
      testsPassed++;
    } else {
      testsFailed++;
    }

    // Save the ID for later tests
    if (capacityResponse.body.data && capacityResponse.body.data.id) {
      capacityId = capacityResponse.body.data.id;
    }

    // Test 4: Update an existing capacity
    console.log("🔍 Test 4: Update an existing capacity");
    const updateData = {
      resourceId: uniqueResourceId, // Use the same resource ID from the previous test
      startTime: "2023-08-01T09:00:00.000Z",
      endTime: "2023-08-01T17:00:00.000Z",
      totalCapacity: 15, // Increased capacity
    };

    const updateResponse = await request(app)
      .post("/capacity")
      .set("x-api-key", API_KEY)
      .send(updateData);

    if (printResult("Update an existing capacity", updateResponse, 200)) {
      testsPassed++;
    } else {
      testsFailed++;
    }

    // Test 5: Get capacity by ID
    if (capacityId) {
      console.log(`🔍 Test 5: Get capacity by ID (${capacityId})`);
      const getCapacityResponse = await request(app)
        .get(`/capacity/byId/${capacityId}`)
        .set("x-api-key", API_KEY);

      if (printResult("Get capacity by ID", getCapacityResponse, 200)) {
        testsPassed++;
      } else {
        testsFailed++;
      }
    }

    // Test 6: Test validation (invalid date range)
    console.log("🔍 Test 6: Test validation (invalid date range)");
    const invalidDateData = {
      resourceId: "test-resource-invalid",
      startTime: "2023-08-01T17:00:00.000Z",
      endTime: "2023-08-01T09:00:00.000Z", // End time before start time
      totalCapacity: 10,
    };

    const invalidDateResponse = await request(app)
      .post("/capacity")
      .set("x-api-key", API_KEY)
      .send(invalidDateData);

    if (
      printResult(
        "Test validation (invalid date range)",
        invalidDateResponse,
        400
      )
    ) {
      testsPassed++;
    } else {
      testsFailed++;
    }

    // Test 7: Batch add capacities
    console.log("🔍 Test 7: Batch add capacities");
    const batchAddData = {
      capacityRecords: [
        {
          resourceId: "batch-resource-1",
          startTime: "2023-08-01T09:00:00.000Z",
          endTime: "2023-08-01T17:00:00.000Z",
          totalCapacity: 20,
          availableCapacity: 20,
        },
        {
          resourceId: "batch-resource-2",
          startTime: "2023-08-01T09:00:00.000Z",
          endTime: "2023-08-01T17:00:00.000Z",
          totalCapacity: 30,
          availableCapacity: 30,
        },
      ],
      organizationId: "test-org-1",
    };

    const batchAddResponse = await request(app)
      .post("/capacity/batch")
      .set("x-api-key", API_KEY)
      .send(batchAddData);

    if (printResult("Batch add capacities", batchAddResponse, 200)) {
      testsPassed++;
    } else {
      testsFailed++;
    }

    // Test 8: Batch update capacities
    console.log("🔍 Test 8: Batch update capacities");
    const batchUpdateData = {
      capacityRecords: [
        {
          resourceId: "batch-resource-1",
          startTime: "2023-08-01T09:00:00.000Z",
          endTime: "2023-08-01T17:00:00.000Z",
          totalCapacity: 25,
          availableCapacity: 25,
        },
        {
          resourceId: "batch-resource-2",
          startTime: "2023-08-01T09:00:00.000Z",
          endTime: "2023-08-01T17:00:00.000Z",
          totalCapacity: 35,
          availableCapacity: 35,
        },
      ],
      organizationId: "test-org-1",
    };

    const batchUpdateResponse = await request(app)
      .post("/capacity/batch")
      .set("x-api-key", API_KEY)
      .send(batchUpdateData);

    if (printResult("Batch update capacities", batchUpdateResponse, 200)) {
      testsPassed++;
    } else {
      testsFailed++;
    }

    // Test 9: Batch validation (invalid date)
    console.log("🔍 Test 9: Batch validation (invalid date)");
    const batchInvalidData = {
      capacityRecords: [
        {
          resourceId: "batch-resource-invalid",
          startTime: "invalid-date",
          endTime: "2023-08-01T17:00:00.000Z",
          totalCapacity: 20,
          availableCapacity: 20,
        },
      ],
      organizationId: "test-org-1",
    };

    const batchInvalidResponse = await request(app)
      .post("/capacity/batch")
      .set("x-api-key", API_KEY)
      .send(batchInvalidData);

    if (
      printResult("Batch validation (invalid date)", batchInvalidResponse, 400)
    ) {
      testsPassed++;
    } else {
      testsFailed++;
    }

    // Test 10: Get slot-wise capacity
    console.log("🔍 Test 10: Get slot-wise capacity");
    // Use the resource ID from the batch test
    const resourceId = "batch-resource-1";
    // Use tomorrow's date in YYYY-MM-DD format
    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);
    const dateStr = tomorrow.toISOString().split("T")[0]; // YYYY-MM-DD format

    const slotWiseResponse = await request(app)
      .get(`/capacity/slot-wise?resourceId=${resourceId}&date=${dateStr}`)
      .set("x-api-key", API_KEY);

    // We should get a 200 response even if no slots are found
    if (printResult("Get slot-wise capacity", slotWiseResponse, 200)) {
      testsPassed++;
    } else {
      testsFailed++;
    }

    // Test 11: Get slot-wise capacity with invalid date format
    console.log("🔍 Test 11: Get slot-wise capacity with invalid date format");
    const invalidSlotWiseResponse = await request(app)
      .get(`/capacity/slot-wise?resourceId=${resourceId}&date=invalid-date`)
      .set("x-api-key", API_KEY);

    if (
      printResult(
        "Get slot-wise capacity with invalid date",
        invalidSlotWiseResponse,
        400
      )
    ) {
      testsPassed++;
    } else {
      testsFailed++;
    }

    // Test 12: Get slot-wise capacity for multiple days
    console.log("🔍 Test 12: Get slot-wise capacity for multiple days");
    // Use the resource ID from the batch test
    // Create dates for today, tomorrow, and day after tomorrow
    const today = new Date();
    // Reuse the tomorrow variable from above
    // Create day after tomorrow
    const dayAfterTomorrow = new Date();
    dayAfterTomorrow.setDate(dayAfterTomorrow.getDate() + 2);

    const todayStr = today.toISOString().split("T")[0]; // YYYY-MM-DD format
    // Reuse the dateStr variable from above for tomorrow
    const dayAfterTomorrowStr = dayAfterTomorrow.toISOString().split("T")[0]; // YYYY-MM-DD format

    const dates = `${todayStr},${dateStr},${dayAfterTomorrowStr}`;

    const slotWiseMultipleDaysResponse = await request(app)
      .get(
        `/capacity/slot-wise-multiple-days?resourceId=${resourceId}&dates=${dates}`
      )
      .set("x-api-key", API_KEY);

    // We should get a 200 response even if no slots are found
    if (
      printResult(
        "Get slot-wise capacity for multiple days",
        slotWiseMultipleDaysResponse,
        200
      )
    ) {
      testsPassed++;
    } else {
      testsFailed++;
    }

    // Test 13: Get slot-wise capacity for multiple days with invalid date format
    console.log(
      "🔍 Test 13: Get slot-wise capacity for multiple days with invalid date format"
    );
    const invalidDates = `${todayStr},invalid-date,${dateStr}`;

    const invalidSlotWiseMultipleDaysResponse = await request(app)
      .get(
        `/capacity/slot-wise-multiple-days?resourceId=${resourceId}&dates=${invalidDates}`
      )
      .set("x-api-key", API_KEY);

    if (
      printResult(
        "Get slot-wise capacity for multiple days with invalid date",
        invalidSlotWiseMultipleDaysResponse,
        400
      )
    ) {
      testsPassed++;
    } else {
      testsFailed++;
    }

    // Summary
    console.log("\n📊 TEST SUMMARY");
    console.log("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");
    console.log(`🧪 Total Tests: ${testsPassed + testsFailed} (13 tests)`);
    console.log(`✅ Passed: ${testsPassed}`);
    console.log(`❌ Failed: ${testsFailed}`);
    console.log("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");

    if (testsFailed === 0) {
      console.log("🎉 All tests completed successfully!");
    } else {
      console.log("⚠️ Some tests failed. Please check the output above.");
    }
  } catch (error) {
    console.error("Error during tests:", error);
  }
}

// Run the tests
runTests();
