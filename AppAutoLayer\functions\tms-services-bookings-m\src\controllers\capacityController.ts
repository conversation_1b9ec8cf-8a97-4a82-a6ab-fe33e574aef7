import { Request, Response } from "express";
import { Capacity } from "../models";
import { serializeBigInt } from "../utils/bigint-serializer";
import { Op } from "sequelize";

/**
 * Interface for slot-wise capacity response
 */
interface SlotCapacity {
  start: string;
  end: string;
  label: string;
  available_qty: number;
  total_qty: number;
}

/**
 * Add new capacity
 * @param req Request
 * @param res Response
 */
export const addCapacity = async (
  req: Request,
  res: Response
): Promise<void> => {
  try {
    const { resourceId, startTime, endTime, totalCapacity } = req.body;

    // Validate required fields
    if (!resourceId || !startTime || !endTime || totalCapacity === undefined) {
      res.status(400).json({
        status: false,
        message:
          "Missing required fields: resourceId, startTime, endTime, totalCapacity",
        timestamp: new Date().toISOString(),
      });
      return;
    }

    // Validate capacity value
    if (totalCapacity < 0) {
      res.status(400).json({
        status: false,
        message: "Total capacity must be a non-negative integer",
        timestamp: new Date().toISOString(),
      });
      return;
    }

    // Validate time range
    const startDate = new Date(startTime);
    const endDate = new Date(endTime);

    if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
      res.status(400).json({
        status: false,
        message: "Invalid date format for startTime or endTime",
        timestamp: new Date().toISOString(),
      });
      return;
    }

    if (startDate >= endDate) {
      res.status(400).json({
        status: false,
        message: "startTime must be before endTime",
        timestamp: new Date().toISOString(),
      });
      return;
    }

    // Extract day from startTime for the day field
    const day = startDate.toISOString().split("T")[0];

    // Use bulkCreate with updateOnDuplicate option to handle both insert and update in a single operation
    // This is more efficient than checking if the record exists first
    const [capacity] = await Capacity.bulkCreate(
      [
        {
          resourceId,
          startTime: startDate,
          endTime: endDate,
          day,
          totalCapacity,
          availableCapacity: totalCapacity,
        },
      ],
      {
        updateOnDuplicate: ["totalCapacity", "availableCapacity", "updatedAt"],
        returning: true,
      }
    );

    // Check if this was an insert or update by looking at the createdAt and updatedAt timestamps
    const isUpdate =
      capacity.createdAt.getTime() !== capacity.updatedAt.getTime();

    // Send the response
    res.status(isUpdate ? 200 : 201).json({
      status: true,
      message: isUpdate
        ? "Capacity updated successfully"
        : "Capacity added successfully",
      data: serializeBigInt(capacity.toJSON()),
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error("Error adding capacity:", error);
    res.status(500).json({
      status: false,
      message: "Internal server error",
      timestamp: new Date().toISOString(),
    });
  }
};

/**
 * Get capacity by ID
 * @param req Request
 * @param res Response
 */
export const getCapacityById = async (
  req: Request,
  res: Response
): Promise<void> => {
  try {
    const { id } = req.params;

    const capacity = await Capacity.findByPk(BigInt(id));

    if (!capacity) {
      res.status(404).json({
        status: false,
        message: "Capacity not found",
        timestamp: new Date().toISOString(),
      });
      return;
    }

    res.status(200).json({
      status: true,
      data: serializeBigInt(capacity.toJSON()),
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error("Error getting capacity:", error);
    res.status(500).json({
      status: false,
      message: "Internal server error",
      timestamp: new Date().toISOString(),
    });
  }
};

/**
 * Get slot-wise capacity for a resource on a specific day
 * @param req Request
 * @param res Response
 */
export const getSlotWiseCapacity = async (
  req: Request,
  res: Response
): Promise<void> => {
  try {
    const { resourceId, date } = req.query;

    // Validate required parameters
    if (!resourceId || !date) {
      res.status(400).json({
        status: false,
        message: "Missing required parameters: resourceId, date (YYYY-MM-DD)",
        timestamp: new Date().toISOString(),
      });
      return;
    }

    // Validate date format (YYYY-MM-DD)
    const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
    if (!dateRegex.test(date as string)) {
      res.status(400).json({
        status: false,
        message: "Invalid date format. Expected format: YYYY-MM-DD",
        timestamp: new Date().toISOString(),
      });
      return;
    }

    // Validate the date
    const dateObj = new Date(`${date}T00:00:00.000Z`);
    if (isNaN(dateObj.getTime())) {
      res.status(400).json({
        status: false,
        message: "Invalid date",
        timestamp: new Date().toISOString(),
      });
      return;
    }

    // Query capacity records for the resource and date using the day column
    // This will use the new index on resourceId and day for better performance
    const capacities = await Capacity.findAll({
      where: {
        resourceId: resourceId as string,
        day: date as string, // Use the day column directly
      },
      order: [["startTime", "ASC"]],
    });

    if (capacities.length === 0) {
      res.status(200).json({
        status: true,
        message: "No capacity data found for the specified resource and date",
        data: [],
        timestamp: new Date().toISOString(),
      });
      return;
    }

    // Format the response to match the expected slot-wise structure
    const slots: SlotCapacity[] = capacities.map((capacity) => {
      const startTime = new Date(capacity.startTime);
      const endTime = new Date(capacity.endTime);

      // Format times for display
      const startHour = startTime.getUTCHours();
      const startMinute = startTime.getUTCMinutes();
      const endHour = endTime.getUTCHours();
      const endMinute = endTime.getUTCMinutes();

      const startFormatted = `${startHour}:${startMinute
        .toString()
        .padStart(2, "0")}`;
      const endFormatted = `${endHour}:${endMinute
        .toString()
        .padStart(2, "0")}`;

      return {
        start: capacity.startTime.toISOString(),
        end: capacity.endTime.toISOString(),
        label: `${startFormatted} - ${endFormatted}`,
        available_qty: capacity.availableCapacity,
        total_qty: capacity.totalCapacity,
      };
    });

    // Format the response to match the expected structure
    res.status(200).json({
      status: true,
      message: "Slot-wise capacity retrieved successfully",
      data: [
        {
          resource_id: resourceId,
          slots: slots,
        },
      ],
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error("Error getting slot-wise capacity:", error);
    res.status(500).json({
      status: false,
      message: "Internal server error",
      timestamp: new Date().toISOString(),
    });
  }
};

/**
 * Get slot-wise capacity for a resource on multiple days
 * @param req Request
 * @param res Response
 */
export const getSlotWiseCapacityForMultipleDays = async (
  req: Request,
  res: Response
): Promise<void> => {
  try {
    const { resourceId, dates } = req.query;

    // Validate required parameters
    if (!resourceId || !dates) {
      res.status(400).json({
        status: false,
        message:
          "Missing required parameters: resourceId, dates (comma-separated YYYY-MM-DD)",
        timestamp: new Date().toISOString(),
      });
      return;
    }

    // Parse dates from comma-separated string
    const dateArray = (dates as string).split(",");

    if (dateArray.length === 0) {
      res.status(400).json({
        status: false,
        message: "No valid dates provided",
        timestamp: new Date().toISOString(),
      });
      return;
    }

    // Validate date format for each date (YYYY-MM-DD)
    const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
    const invalidFormatDates = dateArray.filter(
      (date) => !dateRegex.test(date)
    );

    if (invalidFormatDates.length > 0) {
      res.status(400).json({
        status: false,
        message: `Invalid date format for: ${invalidFormatDates.join(
          ", "
        )}. Expected format: YYYY-MM-DD`,
        timestamp: new Date().toISOString(),
      });
      return;
    }

    // Check for invalid dates
    const invalidDates = dateArray.filter((date) => {
      const dateObj = new Date(`${date}T00:00:00.000Z`);
      return isNaN(dateObj.getTime());
    });

    if (invalidDates.length > 0) {
      res.status(400).json({
        status: false,
        message: `Invalid dates: ${invalidDates.join(", ")}`,
        timestamp: new Date().toISOString(),
      });
      return;
    }

    // Create a query to get capacity for all dates in one go using the day column
    // This will use the new index on resourceId and day for better performance
    const capacities = await Capacity.findAll({
      where: {
        resourceId: resourceId as string,
        day: {
          [Op.in]: dateArray, // Use the day column with IN operator
        },
      },
      order: [["startTime", "ASC"]],
    });

    if (capacities.length === 0) {
      res.status(200).json({
        status: true,
        message: "No capacity data found for the specified resource and dates",
        data: [],
        timestamp: new Date().toISOString(),
      });
      return;
    }

    // Group capacities by date
    const capacitiesByDate = new Map<string, any[]>();

    // Initialize the map with empty arrays for each date
    dateArray.forEach((date) => {
      capacitiesByDate.set(date, []);
    });

    // Assign capacities to their respective dates
    capacities.forEach((capacity) => {
      // Use the day column directly to determine which date this capacity belongs to
      const capacityDay = capacity.day;
      if (dateArray.includes(capacityDay)) {
        const dateCapacities = capacitiesByDate.get(capacityDay) || [];
        dateCapacities.push(capacity);
        capacitiesByDate.set(capacityDay, dateCapacities);
      }
    });

    // Format the response to match the expected slot-wise structure for each date
    const result = Array.from(capacitiesByDate.entries()).map(
      ([date, dateCapacities]) => {
        // Format slots for this date
        const slots: SlotCapacity[] = dateCapacities.map((capacity) => {
          const startTime = new Date(capacity.startTime);
          const endTime = new Date(capacity.endTime);

          // Format times for display
          const startHour = startTime.getUTCHours();
          const startMinute = startTime.getUTCMinutes();
          const endHour = endTime.getUTCHours();
          const endMinute = endTime.getUTCMinutes();

          const startFormatted = `${startHour}:${startMinute
            .toString()
            .padStart(2, "0")}`;
          const endFormatted = `${endHour}:${endMinute
            .toString()
            .padStart(2, "0")}`;

          return {
            start: capacity.startTime.toISOString(),
            end: capacity.endTime.toISOString(),
            label: `${startFormatted} - ${endFormatted}`,
            available_qty: capacity.availableCapacity,
            total_qty: capacity.totalCapacity,
          };
        });

        return {
          date,
          resource_id: resourceId,
          slots: slots,
        };
      }
    );

    // Format the response to match the expected structure
    res.status(200).json({
      status: true,
      message: "Slot-wise capacity for multiple days retrieved successfully",
      data: result,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error("Error getting slot-wise capacity for multiple days:", error);
    res.status(500).json({
      status: false,
      message: "Internal server error",
      timestamp: new Date().toISOString(),
    });
  }
};
