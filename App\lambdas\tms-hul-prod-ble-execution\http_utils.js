const axios = require("axios");
const oauth = require("oauth-1.0a");
const crypto = require("crypto");


// Configuration for OAuth 1.0
const oauthConfig = {
  consumerKey:
    "9b15181920ab82f6be051f607eeb28d8b45f84840ea72626df882ca7fcde9f31",
  consumerSecret:
    "f503cb3e9d626d2b6611e6ce743f23a1f9719a8fe69ca37ee678df4432a28c87",
  accessToken:
    "cb535ffcc2589496acb19f2eec0cc630378d6828590d8e4f0161fc4b7f8e598c",
  accessTokenSecret:
    "c9ab57532db14ac4ac11531f4cd878e77f697d8465a145856fddd29721df7763",
  realm: "3667364",
};

const logAxiosError = (error) => {
  if (error.response) {
    // The request was made and the server responded with a status code
    // that falls out of the range of 2xx
    console.log(error.response.data);
    console.log(error.response.status);
    console.log(error.response.headers);
  } else if (error.request) {
    // The request was made but no response was received
    // `error.request` is an instance of XMLHttpRequest in the browser and an instance of
    // http.ClientRequest in node.js
    console.log(error.request);
  } else {
    // Something happened in setting up the request that triggered an Error
    console.log("Error", error.message);
  }
  // console.log(error.config);
};

// console.log('request_data["headers"] ', request_data);
exports.makeRequest = async (data) => {
  // Create an OAuth 1.0 instance
  const oauthInstance = oauth({
    consumer: {
      key: oauthConfig.consumerKey,
      secret: oauthConfig.consumerSecret,
      realm: oauthConfig.realm,
    },
    signature_method: "HMAC-SHA256",
    hash_function(baseString, key) {
      return crypto
        .createHmac("SHA256", key)
        .update(baseString)
        .digest("base64");
    },
    realm: oauthConfig.realm,
  });

  const token = {
    key: oauthConfig.accessToken,
    secret: oauthConfig.accessTokenSecret,
  };
  const request_data = {
    url: "https://3667364.restlets.api.netsuite.com/app/site/hosting/restlet.nl?script=2485&deploy=1",
    method: "POST",
    maxBodyLength: Infinity,
    body: JSON.stringify(data),
  };
  // Generate OAuth 1.0 headers
  const oauthHeaders = oauthInstance.toHeader(
    oauthInstance.authorize(request_data, token)
  );
  const headers = {
    "Content-Type": "application/json",
    ...oauthHeaders,
  };
  return await axios.post(request_data.url, JSON.stringify(data), {
    headers,
  });
};
