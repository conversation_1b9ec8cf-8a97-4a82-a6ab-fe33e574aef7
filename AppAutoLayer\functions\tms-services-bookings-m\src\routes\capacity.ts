import express from "express";
import {
  addCapacity,
  getCapacityById,
  getSlotWiseCapacity,
  getSlotWiseCapacityForMultipleDays,
} from "../controllers/capacityController";
import { batchAddCapacity } from "../controllers/batchCapacityController";
import { authenticate<PERSON><PERSON><PERSON><PERSON> } from "../middleware/auth";

const router = express.Router();

/**
 * @swagger
 * /capacity:
 *   post:
 *     summary: Add or update capacity
 *     description: Creates a new capacity entry for a resource or updates it if it already exists
 *     security:
 *       - ApiKeyAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - resourceId
 *               - startTime
 *               - endTime
 *               - totalCapacity
 *             properties:
 *               resourceId:
 *                 type: string
 *                 description: ID of the resource
 *               startTime:
 *                 type: string
 *                 format: date-time
 *                 description: Start time of the capacity (ISO 8601 format)
 *               endTime:
 *                 type: string
 *                 format: date-time
 *                 description: End time of the capacity (ISO 8601 format)
 *               totalCapacity:
 *                 type: integer
 *                 minimum: 0
 *                 description: Total capacity available
 *     responses:
 *       200:
 *         description: Capacity updated successfully
 *       201:
 *         description: Capacity added successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: Capacity added successfully
 *                 data:
 *                   type: object
 *                   properties:
 *                     id:
 *                       type: integer
 *                       example: 1
 *                     resourceId:
 *                       type: string
 *                       example: resource-123
 *                     startTime:
 *                       type: string
 *                       format: date-time
 *                       example: 2023-07-20T09:00:00.000Z
 *                     endTime:
 *                       type: string
 *                       format: date-time
 *                       example: 2023-07-20T17:00:00.000Z
 *                     totalCapacity:
 *                       type: integer
 *                       example: 10
 *                     availableCapacity:
 *                       type: integer
 *                       example: 10
 *                     createdAt:
 *                       type: string
 *                       format: date-time
 *                       example: 2023-07-20T08:00:00.000Z
 *                     updatedAt:
 *                       type: string
 *                       format: date-time
 *                       example: 2023-07-20T08:00:00.000Z
 *                 timestamp:
 *                   type: string
 *                   format: date-time
 *                   example: 2023-07-20T08:00:00.000Z
 *       400:
 *         description: Bad request - missing or invalid parameters
 *       401:
 *         description: Unauthorized - invalid API key
 *       409:
 *         description: Conflict - deprecated, now updates existing capacity
 *       500:
 *         description: Internal server error
 */
router.post("/", authenticateApiKey, addCapacity);

/**
 * @swagger
 * /capacity/slot-wise:
 *   get:
 *     summary: Get slot-wise capacity for a resource on a specific day
 *     description: Retrieves all capacity slots for a resource on a given date
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - in: query
 *         name: resourceId
 *         required: true
 *         schema:
 *           type: string
 *         description: ID of the resource
 *       - in: query
 *         name: date
 *         required: true
 *         schema:
 *           type: string
 *           format: date
 *         description: Date in YYYY-MM-DD format
 *     responses:
 *       200:
 *         description: Slot-wise capacity retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: Slot-wise capacity retrieved successfully
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       resource_id:
 *                         type: string
 *                         example: resource-123
 *                       slots:
 *                         type: array
 *                         items:
 *                           type: object
 *                           properties:
 *                             start:
 *                               type: string
 *                               format: date-time
 *                               example: 2023-07-20T09:00:00.000Z
 *                             end:
 *                               type: string
 *                               format: date-time
 *                               example: 2023-07-20T10:00:00.000Z
 *                             label:
 *                               type: string
 *                               example: 9:00 - 10:00
 *                             available_qty:
 *                               type: integer
 *                               example: 8
 *                             total_qty:
 *                               type: integer
 *                               example: 10
 *                 timestamp:
 *                   type: string
 *                   format: date-time
 *                   example: 2023-07-20T08:00:00.000Z
 *       400:
 *         description: Bad request - missing or invalid parameters
 *       401:
 *         description: Unauthorized - invalid API key
 *       500:
 *         description: Internal server error
 */
router.get("/slot-wise", authenticateApiKey, getSlotWiseCapacity);

/**
 * @swagger
 * /capacity/slot-wise-multiple-days:
 *   get:
 *     summary: Get slot-wise capacity for a resource on multiple days
 *     description: Retrieves all capacity slots for a resource on multiple given dates
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - in: query
 *         name: resourceId
 *         required: true
 *         schema:
 *           type: string
 *         description: ID of the resource
 *       - in: query
 *         name: dates
 *         required: true
 *         schema:
 *           type: string
 *         description: Comma-separated dates in YYYY-MM-DD format (e.g., 2023-07-20,2023-07-21,2023-07-22)
 *     responses:
 *       200:
 *         description: Slot-wise capacity for multiple days retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: Slot-wise capacity for multiple days retrieved successfully
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       date:
 *                         type: string
 *                         format: date
 *                         example: 2023-07-20
 *                       resource_id:
 *                         type: string
 *                         example: resource-123
 *                       slots:
 *                         type: array
 *                         items:
 *                           type: object
 *                           properties:
 *                             start:
 *                               type: string
 *                               format: date-time
 *                               example: 2023-07-20T09:00:00.000Z
 *                             end:
 *                               type: string
 *                               format: date-time
 *                               example: 2023-07-20T10:00:00.000Z
 *                             label:
 *                               type: string
 *                               example: 9:00 - 10:00
 *                             available_qty:
 *                               type: integer
 *                               example: 8
 *                             total_qty:
 *                               type: integer
 *                               example: 10
 *                 timestamp:
 *                   type: string
 *                   format: date-time
 *                   example: 2023-07-20T08:00:00.000Z
 *       400:
 *         description: Bad request - missing or invalid parameters
 *       401:
 *         description: Unauthorized - invalid API key
 *       500:
 *         description: Internal server error
 */
router.get(
  "/slot-wise-multiple-days",
  authenticateApiKey,
  getSlotWiseCapacityForMultipleDays
);

/**
 * @swagger
 * /capacity/byId/{id}:
 *   get:
 *     summary: Get capacity by ID
 *     description: Retrieves capacity information by ID
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: Capacity ID
 *     responses:
 *       200:
 *         description: Capacity retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   type: object
 *                   properties:
 *                     id:
 *                       type: integer
 *                       example: 1
 *                     resourceId:
 *                       type: string
 *                       example: resource-123
 *                     startTime:
 *                       type: string
 *                       format: date-time
 *                       example: 2023-07-20T09:00:00.000Z
 *                     endTime:
 *                       type: string
 *                       format: date-time
 *                       example: 2023-07-20T17:00:00.000Z
 *                     totalCapacity:
 *                       type: integer
 *                       example: 10
 *                     availableCapacity:
 *                       type: integer
 *                       example: 10
 *                     createdAt:
 *                       type: string
 *                       format: date-time
 *                       example: 2023-07-20T08:00:00.000Z
 *                     updatedAt:
 *                       type: string
 *                       format: date-time
 *                       example: 2023-07-20T08:00:00.000Z
 *                 timestamp:
 *                   type: string
 *                   format: date-time
 *                   example: 2023-07-20T08:00:00.000Z
 *       401:
 *         description: Unauthorized - invalid API key
 *       404:
 *         description: Capacity not found
 *       500:
 *         description: Internal server error
 */
router.get("/byId/:id", authenticateApiKey, getCapacityById);

/**
 * @swagger
 * /capacity/batch:
 *   post:
 *     summary: Process batch capacity updates
 *     description: Creates or updates multiple capacity entries in a single transaction (all or nothing)
 *     security:
 *       - ApiKeyAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - capacityRecords
 *             properties:
 *               capacityRecords:
 *                 type: array
 *                 description: Array of capacity records to process
 *                 items:
 *                   type: object
 *                   required:
 *                     - resourceId
 *                     - startTime
 *                     - endTime
 *                     - totalCapacity
 *                   properties:
 *                     resourceId:
 *                       type: string
 *                       description: ID of the resource
 *                     startTime:
 *                       type: string
 *                       format: date-time
 *                       description: Start time of the capacity (ISO 8601 format)
 *                     endTime:
 *                       type: string
 *                       format: date-time
 *                       description: End time of the capacity (ISO 8601 format)
 *                     totalCapacity:
 *                       type: integer
 *                       minimum: 0
 *                       description: Total capacity available
 *                     availableCapacity:
 *                       type: integer
 *                       minimum: 0
 *                       description: Available capacity (defaults to totalCapacity if not provided)
 *                     bookedCapacity:
 *                       type: integer
 *                       minimum: 0
 *                       description: Booked capacity (informational only)
 *                     metadata:
 *                       type: object
 *                       description: Additional metadata for the capacity record
 *               organizationId:
 *                 type: string
 *                 description: Organization ID (informational only)
 *               organizationName:
 *                 type: string
 *                 description: Organization name (informational only)
 *     responses:
 *       200:
 *         description: Batch processed successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: Successfully processed 5000 capacity records
 *                 data:
 *                   type: object
 *                   properties:
 *                     successCount:
 *                       type: integer
 *                       example: 5000
 *                     failureCount:
 *                       type: integer
 *                       example: 0
 *                     failedRecords:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           resourceId:
 *                             type: string
 *                             example: resource-123
 *                           startTime:
 *                             type: string
 *                             format: date-time
 *                             example: 2023-07-20T09:00:00.000Z
 *                           endTime:
 *                             type: string
 *                             format: date-time
 *                             example: 2023-07-20T17:00:00.000Z
 *                           message:
 *                             type: string
 *                             example: Error processing record
 *                           statusCode:
 *                             type: integer
 *                             example: 400
 *                           errorDetails:
 *                             type: object
 *                             properties:
 *                               error:
 *                                 type: string
 *                                 example: Invalid date format
 *                               code:
 *                                 type: string
 *                                 example: INVALID_DATE
 *                 timestamp:
 *                   type: string
 *                   format: date-time
 *                   example: 2023-07-20T08:00:00.000Z
 *       400:
 *         description: Bad request - validation errors in batch
 *       401:
 *         description: Unauthorized - invalid API key
 *       500:
 *         description: Internal server error
 */
router.post("/batch", authenticateApiKey, batchAddCapacity);

export default router;
