import swaggerJsDoc from "swagger-jsdoc";

// Swagger configuration
const swaggerOptions = {
  definition: {
    openapi: "3.0.0",
    info: {
      title: "TMS Bookings Service API (Dev)",
      version: "1.0.0",
      description: "API documentation for TMS Bookings Service (Development)",
      contact: {
        name: "Wify Support",
        email: "<EMAIL>",
      },
    },
    servers: [
      {
        url: process.env.API_URL || "http://localhost:3001",
        description: "Development Server",
      },
    ],
    components: {
      securitySchemes: {
        ApiKeyAuth: {
          type: "apiKey",
          in: "header",
          name: "x-api-key",
        },
      },
    },
    security: [
      {
        ApiKeyAuth: [],
      },
    ],
  },
  apis: ["./src/**/*.ts"],
};

// Generate Swagger specification
const swaggerSpec = swaggerJsDoc(swaggerOptions);

export default swaggerSpec;
