const { sendEmail } = require("./emailService");
const { generateDataTableHTML, getISTTimestamp } = require("./htmlTemplate");
const { getOrdersForVertical } = require("./dataService");
const { convertToCSV } = require("./csvUtils");

/**
 * Process and send emails to all category managers
 * @param {Array} verticalKPIData - All vertical KPI data
 * @param {Object} categoryManagers - Category managers configuration
 * @returns {Promise<Object>} Results of email processing
 */
async function processManagerEmails(verticalKPIData, categoryManagers) {
  // Calculate dates for display
  const today = new Date();
  const yesterday = new Date(today);
  yesterday.setDate(today.getDate() - 1);
  const yesterdayStr = yesterday.toISOString().split("T")[0];

  const emailResults = [];
  const totalAttachments = [];

  // Process each category manager
  for (const [managerEmail, managerData] of Object.entries(categoryManagers)) {
    console.log(
      `ManagerEmailProcessor::processManagerEmails:: Processing category manager: ${managerData.user_name} (${managerEmail})`
    );

    // Filter vertical data for this manager's verticals
    const managerVerticalIds = managerData.verticals.map((v) => v.vertical_id);
    const managerVerticalData = verticalKPIData.filter((vertical) =>
      managerVerticalIds.includes(parseInt(vertical["Vertical ID"]))
    );

    if (managerVerticalData.length === 0) {
      console.log(
        `ManagerEmailProcessor::processManagerEmails:: No data found for manager ${managerData.user_name}, skipping`
      );
      continue;
    }

    console.log(
      `ManagerEmailProcessor::processManagerEmails:: Found ${managerVerticalData.length} verticals for ${managerData.user_name}`
    );

    // Process attachments for this manager
    const managerAttachments = await processManagerAttachments(
      managerVerticalData,
      managerData,
      yesterdayStr
    );
    totalAttachments.push(...managerAttachments);

    // Generate and send email for this manager
    const emailResult = await sendManagerEmail(
      managerEmail,
      managerData,
      managerVerticalData,
      managerAttachments,
      yesterdayStr
    );

    emailResults.push(emailResult);
  }

  // Calculate summary
  const successCount = emailResults.filter(
    (r) => r.status === "Success"
  ).length;
  const failureCount = emailResults.filter((r) => r.status === "Failed").length;

  return {
    emailResults,
    successCount,
    failureCount,
    totalAttachments,
  };
}

/**
 * Process attachments for a specific manager
 * @param {Array} managerVerticalData - Vertical data for the manager
 * @param {Object} managerData - Manager configuration
 * @param {string} yesterdayStr - Yesterday's date string
 * @returns {Promise<Array>} Array of attachment objects
 */
async function processManagerAttachments(
  managerVerticalData,
  managerData,
  yesterdayStr
) {
  const managerAttachments = [];

  for (const vertical of managerVerticalData) {
    const openOrders = parseInt(vertical["Open orders"]) || 0;
    if (openOrders > 0) {
      console.log(
        `ManagerEmailProcessor::processManagerAttachments:: Vertical "${vertical.Title}" has ${openOrders} open orders (>0), fetching order details for ${managerData.user_name}`
      );

      try {
        const orderData = await getOrdersForVertical(
          vertical["Vertical ID"],
          vertical.Title
        );

        if (orderData && orderData.length > 0) {
          const csvContent = convertToCSV(orderData);
          const sanitizedTitle = String(vertical.Title).replace(
            /[^a-zA-Z0-9]/g,
            "_"
          );

          const attachment = {
            filename: `${sanitizedTitle}_Open_Orders_${yesterdayStr}.csv`,
            content: csvContent,
            contentType: "text/csv",
          };

          managerAttachments.push(attachment);

          console.log(
            `ManagerEmailProcessor::processManagerAttachments:: Created CSV attachment for ${vertical.Title} with ${orderData.length} orders for ${managerData.user_name}`
          );
        }
      } catch (error) {
        console.error(
          `ManagerEmailProcessor::processManagerAttachments:: Error fetching orders for vertical ${vertical.Title} for manager ${managerData.user_name}:`,
          error
        );
      }
    }
  }

  return managerAttachments;
}

/**
 * Send email to a specific manager
 * @param {string} managerEmail - Manager's email address
 * @param {Object} managerData - Manager configuration
 * @param {Array} managerVerticalData - Vertical data for the manager
 * @param {Array} managerAttachments - Attachments for the manager
 * @param {string} yesterdayStr - Yesterday's date string
 * @returns {Promise<Object>} Email result object
 */
async function sendManagerEmail(
  managerEmail,
  managerData,
  managerVerticalData,
  managerAttachments,
  yesterdayStr
) {
  // Generate HTML content for this manager's data
  const verticalNames = managerData.verticals.map((v) => v.vertical).join(", ");
  let htmlDescription = `Daily KPI metrics for ${yesterdayStr} for your managed verticals:<br><strong>${verticalNames}</strong>`;
  if (managerAttachments.length > 0) {
    htmlDescription += `<br><br><em>Note: ${managerAttachments.length} vertical(s) with open orders have detailed order lists attached as CSV files.</em>`;
  }

  const htmlContent = generateDataTableHTML(managerVerticalData, {
    title: `Fulfillment KPI Report - ${managerData.user_name}`,
    description: htmlDescription,
  });

  // Email configuration for this manager
  const emailConfig = {
    to: process.env.EMAIL_RECIPIENTS, //|| managerEmail,
    subject: `Daily Fulfillment KPI Report - ${
      managerData.user_name
    } - ${getISTTimestamp()}`,
    htmlContent: htmlContent,
    cc: process.env.EMAIL_CC || "", // Optional CC recipients
    bcc: process.env.EMAIL_BCC || "", // Optional BCC recipients
    attachments: managerAttachments,
  };

  // Send email to this manager
  console.log(
    `ManagerEmailProcessor::sendManagerEmail:: Sending email to: ${managerEmail} (${managerData.user_name})`
  );
  console.log(
    `ManagerEmailProcessor::sendManagerEmail:: Email includes ${managerAttachments.length} attachments for ${managerData.user_name}`
  );

  try {
    const emailResult = await sendEmail(emailConfig);
    const result = {
      manager: managerData.user_name,
      email: managerEmail,
      status: emailResult.status,
      messageId: emailResult.messageId,
      verticalCount: managerVerticalData.length,
      attachmentCount: managerAttachments.length,
      error: emailResult.error || null,
    };

    if (emailResult.status === "Success") {
      console.log(
        `ManagerEmailProcessor::sendManagerEmail:: Email sent successfully to ${managerData.user_name}`
      );
    } else {
      console.error(
        `ManagerEmailProcessor::sendManagerEmail:: Email send failed for ${managerData.user_name}:`,
        emailResult.error
      );
    }

    return result;
  } catch (error) {
    console.error(
      `ManagerEmailProcessor::sendManagerEmail:: Error sending email to ${managerData.user_name}:`,
      error
    );
    return {
      manager: managerData.user_name,
      email: managerEmail,
      status: "Failed",
      messageId: null,
      verticalCount: managerVerticalData.length,
      attachmentCount: managerAttachments.length,
      error: error.message,
    };
  }
}

module.exports = {
  processManagerEmails,
};
