const HttpStatus = require("http-status-codes");
const http_utils = require("./http_utils.js");
const sampleOperationResp = require("./operationResp.js");
const S3_REGION = "ap-south-1";

function getFinalLink(path, bucketName, getLink = false) {
  try {
    if (path.startsWith(`https://${bucketName}.s3`)) {
      const s3Key = path.replace(
        `https://${bucketName}.s3.${S3_REGION}.amazonaws.com/`,
        ""
      );

      if (getLink) {
        const fileUrl = `https://static.wify.co.in/${s3Key}`;
        return fileUrl;
      }
    } else {
      console.log("getFinalLink :: Invalid S3 URL ::", path);
    }

    return;
  } catch (error) {
    console.log("getFinalLink :: Error ::", error);
    return;
  }
}

function getOptionLabelByKey(fields, selectFieldKey, optionKey) {
  const selectField = fields.find((field) => field.key === selectFieldKey);
  if (!selectField || !Array.isArray(selectField.options)) return null;

  const option = selectField.options.find((opt) => opt.value === optionKey);
  return option ? option.label : "";
}

const generateHTML = (data) => `
  <!DOCTYPE html>
  <html lang="en">
  <head>
    <meta charset="UTF-8">
    <title>Warranty Certificate</title>
    <style>
      .wy-wc-container {
        width: 650px;
        max-width: 100%;
        margin: 40px auto;
        padding: 24px;
        border: 1px solid #F3F3F3;
        font-family: 'Arial', sans-serif;
        background-color: #fff;
        color: #000;
        border-radius: 10px;
      }
      .wy-wc-title {
          text-align: center;
          font-size: 20px;
          font-weight: bold;
          margin: 30px 0;
      }
      .wy-wc-section {
          margin-bottom: 40px;
      }
      .wy-wc-subtitle {
          font-size: 20px;
          font-weight: bold;
          margin-top: 30px;
          margin-bottom: 10px;
          text-decoration: underline;
      }
      .wy-wc-note {
          margin-top: 30px;
          line-height: 1.5;
      }
      .wy-wc-note a,
      .wy-wc-contact a {
          color: #007BFF;
          text-decoration: none;
      }
      .wy-wc-note a:hover,
      .wy-wc-contact a:hover {
          text-decoration: underline;
      }
      .wy-wc-contact {
          margin-top: 30px;
      }
      .wy-wc-footer {
          text-align: left;
          font-weight: bold;
          margin-top: 40px;
      }
      .wy-wc-table {
          width: 100%;
          border-collapse: collapse;
          margin: 50px 0;
          font-size: 14px;
          text-align: left;
      }
      .wy-wc-table td,
      .wy-wc-table th {
          border: 1px solid #ccc;
          padding: 8px 12px;
      }
      .wy-wc-table tr:nth-child(even) {
          background-color: #F9F9F9;
      }
      .wy-wc-table tr:hover {
          background-color: #FCFCFC;
      }
      .wy-wc-table th {
          background-color: #F0F0F0;
          font-weight: normal;
      }
      .wy-wc-footer-label {
        font-weight: normal;
        margin-bottom: 0;
      }
      .wy-wc-footer-brand {
        margin: 0;
      }
    </style>
  </head>
  <body>
    <div class="wy-wc-container">
      <img src="https://static.wify.co.in/images/welsupun_living_logo_fr_certificate.svg" alt="Welspun Logo" style="max-width: 150px;" />
      <h3 class="wy-wc-title">Warranty Certificate</h3>
  
      <div class="wy-wc-section">
        <p><strong>Case ID:</strong> ${data.caseId}</p>
        <p><strong>Customer Name:</strong> ${data.customerName}</p>
        <p><strong>Mobile No:</strong> ${data.mobileNo}</p>
        <p><strong>Site Address:</strong> ${data.siteAddress}</p>
      </div>
  
      <p>Dear Customer,</p>
      <p>We would like to confirm that installation of Welspun Flooring Limited products at your premises was successfully completed as per the details below.</p>
  
      <table class="wy-wc-table">
        <thead>
          <tr>
            <th>Product Details</th>
            <th>Article Code</th>
            <th>Area</th>
            <th>Warranty Start Date</th>
            <th>Warranty End Date</th>
          </tr>
        </thead>
        <tbody>
          ${generateTableRows(data.productTable)}
        </tbody>
      </table>
  
      <div class="wy-wc-note">
        <p>We request you to show this document along with the original purchase invoice for claiming the warranty of the product.</p>
        <p>
          The warranty of the product will be applicable as per the standard warranty terms and conditions of Welspun Flooring Limited. To know further detailed warranty terms and conditions, please visit
          <a href="${data.website}" target="_blank" rel="noopener noreferrer">${
  data.website
}</a>.
        </p>
      </div>
  
      <div class="wy-wc-contact">
        <p>For any future assistance you can reach us on ${
          data.supportContact
        } or <a href="mailto:${data.supportEmail}">${
  data.supportEmail
}</a>. We thank you for using our product and services.</p>
      </div>
  
      <div class="wy-wc-footer">
        <p class="wy-wc-footer-label">Thanking You.</p>
        <p class="wy-wc-footer-brand">Welspun Flooring Ltd.</p>
      </div>
    </div>
  </body>
  </html>
  `;

const generateTableRows = (rows = []) => {
  return rows
    .map(
      (row) => `
      <tr>
        <td>${row?.productCode}</td>
        <td>${row?.articleCode}</td>
        <td>${row?.area}</td>
        <td>${row?.warrantyStart}</td>
        <td>${row?.warrantyEnd}</td>
      </tr>
    `
    )
    .join("");
};

const copyToS3 = ({ sourceBucket, destinationBucket }) => {
  return new Promise((resolve, reject) => {
    const copyUrl = `https://intcertgen.wify.co.in/aws/copy-from-s3`;

    const params = {
      s3SourceInfo: sourceBucket,
      s3DestInfo: destinationBucket,
    };

    const onComplete = (resp) => {
      console.log("Copy to S3 successful", resp);
      resolve(new sampleOperationResp(true, resp, HttpStatus.StatusCodes.OK));
    };

    const onError = (resp) => {
      console.log("Copy to S3 error", resp);
      resolve(
        new sampleOperationResp(false, resp, HttpStatus.StatusCodes.BAD_REQUEST)
      );
    };
    http_utils.performPostCall(copyUrl, params, onComplete, onError);
  });
};

const generatePDF = (modifiedWarrantyData) => {
  return new Promise((resolve, reject) => {
    // let submitUrl = `https://qa02-tms-app.wify.co.in/v1/lambda/pdf/generate`;
    // let submitUrl = `https://pie-hawk-itunes-begin.trycloudflare.com/v1/lambda/pdf/generate`;
    // let submitUrl = `https://api-tms.wify.co.in/v1/lambda/pdf/generate`;
    let submitUrl = `https://intcertgen.wify.co.in/pdf/generate`;
    const htmlContent = generateHTML(modifiedWarrantyData);
    const d = new Date();
    const today = d.toISOString().slice(0, 10);
    const fileName = `WarrantyCertificate_${today}_${d.getTime()}.pdf`;
    const filePath = `Warranty`;
    const bucketName = "wifystaticbucket";

    const s3Info = {
      bucketName,
      filePath,
      fileName,
    };
    const params = { htmlContent, uploadToS3: true, s3Info };
    const onComplete = (resp) => {
      console.log("sms send successfully ", resp);
      resolve(new sampleOperationResp(true, resp, HttpStatus.StatusCodes.OK));
    };
    const onError = (resp) => {
      console.log(`error resp`, resp);
      const errorResponseData = resp?.response?.data;

      console.log(
        `errorResponseData ${errorResponseData.status} `,
        errorResponseData
      );
      resolve(
        new sampleOperationResp(false, resp, HttpStatus.StatusCodes.GONE)
      );
    };
    http_utils.performPostCall(submitUrl, params, onComplete, onError);
  });
};

function getOptionsKeyMap(meta, fieldKey) {
  // Find the field with the given key and map its options
  return meta
    .find((item) => item.key === fieldKey)
    .options.reduce((acc, { label, value }) => {
      acc[label] = value;
      return acc;
    }, {});
}

function tmsExtractAddress(formData, prefix) {
  const keys = [
    `${prefix}line_0`,
    `${prefix}line_1`,
    `${prefix}line_2`,
    `${prefix}line_3`,
    `${prefix}city`,
    `${prefix}state`,
    `${prefix}pincode`,
  ];

  let returnText = "";
  let first = true;

  for (const key of keys) {
    const value = formData[key];
    if (value !== undefined && value !== null && value !== "") {
      if (!first) {
        returnText += ", ";
      }
      returnText += value;
      first = false;
    }
  }

  return returnText;
}

const handler = async (event) => {
  console.log("event", event);
  let responseStatus = false;
  let responseMessage = "Unknown error";
  let createWarrantyCert = false;
  let hasClosedSubtsksFrWarrantyCert = false;

  let finalGeneratePDFResp;
  let modifiedSrvcReqFormData = {};
  try {
    const { srvcReqDetails, srvc_config_data, srvcReqClosedSubtasksDetails } =
      event;
    let modifiedWarrantyData = {};
    const srvcConfigData = event?.srvc_config_data;
    const srvcCustFields = JSON.parse(
      srvcConfigData?.srvc_cust_fields_json || "{}"
    );
    const translatedFields = srvcCustFields?.translatedFields || [];
    const srvcCustLabelToKeyMap = {};
    const formData = srvcReqDetails?.form_data;

    let productsTablearray = [];
    let warrantyEnd;
    // const warrantyStartUTC = null;
    const x = 5; // number of years to add

    warrantyEnd = null;
    // if (warrantyStartUTC) {
    //   warrantyEnd = new Date(warrantyStartUTC);
    //   warrantyEnd.setFullYear(warrantyEnd.getFullYear() + x);
    // }

    // const formattedWarrantyEnd = warrantyEnd?.toISOString().split("T")[0];
    let formattedWarrantyStart = null;
    let formattedWarrantyEnd = null;

    const warrantyStart = srvcReqDetails?.closed_date;
    console.log(
      "modifiedWarrantyData without productTable :: formattedWarrantyEnd",
      formattedWarrantyEnd
    );

    modifiedWarrantyData["customerName"] = formData?.cust_full_name || "";
    modifiedWarrantyData["mobileNo"] = formData?.cust_mobile || "";
    modifiedWarrantyData["siteAddress"] = tmsExtractAddress(formData, "cust_");

    modifiedWarrantyData["supportContact"] = "18001201161";
    modifiedWarrantyData["supportEmail"] = "<EMAIL>";
    modifiedWarrantyData["website"] = "http://www.welspunflooring.com/";

    console.log(
      "modifiedWarrantyData without productTable :: modifiedWarrantyData",
      modifiedWarrantyData
    );

    if (srvcReqClosedSubtasksDetails?.length > 0) {
      srvcReqClosedSubtasksDetails.forEach(
        (singleSrvcReqClosedSubtasksDetails) => {
          const sbtskClosedFormData =
            singleSrvcReqClosedSubtasksDetails?.sbtsk_closed_form_data || {};
          const sbtskClosedCustFields = JSON.parse(
            singleSrvcReqClosedSubtasksDetails?.sbtsk_closed_cust_fields || "{}"
          );
          const sbtskTranslatedFields =
            sbtskClosedCustFields?.translatedFields || [];
          const sbtskCustLabelToKeyMap = {};

          sbtskTranslatedFields.map((singleField) => {
            sbtskCustLabelToKeyMap[singleField.label] = singleField.key;
          });
          console.log("sbtskCustLabelToKeyMap", sbtskCustLabelToKeyMap);
          for (let index = 1; index < 4; index++) {
            const productCode =
              getOptionLabelByKey(
                sbtskTranslatedFields,
                sbtskCustLabelToKeyMap[`Product Type (${index})`],
                sbtskClosedFormData[
                  sbtskCustLabelToKeyMap[`Product Type (${index})`]
                ]
              ) || "";
            const articleCode =
              sbtskClosedFormData[
                sbtskCustLabelToKeyMap[`Article Code (${index}) install`]
              ] || "";
            const rawArea =
              sbtskClosedFormData[
                sbtskCustLabelToKeyMap[`Area install (${index}) (sqft.)`]
              ];
            const area =
              rawArea !== undefined && rawArea !== null
                ? Number(parseFloat(rawArea).toFixed(2))
                : "";

            const invoiceDate =
              sbtskClosedFormData[sbtskCustLabelToKeyMap[`Invoice Date`]];

            const invoiceDateUTC = new Date(invoiceDate);

            let warrantyEndDate = null;
            if (invoiceDate) {
              warrantyEndDate = new Date(invoiceDateUTC);
              warrantyEndDate.setFullYear(warrantyEndDate.getFullYear() + x);
              console.log("yeti warrantyEndDate inside if", warrantyEndDate);
            }

            formattedWarrantyStart = invoiceDate
              ? invoiceDateUTC.toLocaleDateString("en-GB")
              : formattedWarrantyStart;

            formattedWarrantyEnd = warrantyEndDate
              ? warrantyEndDate.toLocaleDateString("en-GB")
              : formattedWarrantyEnd;

            console.log(
              "formattedWarrantyStart ",
              formattedWarrantyStart,
              "formattedWarrantyEnd",
              formattedWarrantyEnd
            );

            // const area = sbtskClosedFormData[sbtskCustLabelToKeyMap[`Area install (${index}) (sqft.)`]];
            if (productCode) {
              productsTablearray.push({
                productCode,
                articleCode,
                area,
                warrantyStart: formattedWarrantyStart,
                warrantyEnd: formattedWarrantyEnd,
              });
            }
          }
        }
      );
      modifiedWarrantyData["productTable"] = productsTablearray;
    }

    console.log(
      "modifiedWarrantyData with productTable :: modifiedWarrantyData",
      modifiedWarrantyData
    );
    console.log(
      "modifiedWarrantyData with productTable :: modifiedWarrantyData",
      JSON.stringify(modifiedWarrantyData)
    );
    console.log("create Warranty Certificate srvcConfigData", srvcConfigData);
    console.log("create Warranty Certificate srvcCustFields", srvcCustFields);
    console.log(
      "create Warranty Certificate translatedFields",
      translatedFields
    );
    console.log(
      "create Warranty Certificate srvcReqClosedSubtasksDetails",
      srvcReqClosedSubtasksDetails
    );

    translatedFields.map((singleField) => {
      srvcCustLabelToKeyMap[singleField.label] = singleField.key;
    });
    console.log(
      "create Warranty Certificate srvcCustLabelToKeyMap",
      srvcCustLabelToKeyMap
    );
    console.log("yeti translatedFields", translatedFields);
    const shareWarrantyCertOptionsMap = getOptionsKeyMap(
      translatedFields,
      srvcCustLabelToKeyMap["Share Warranty Certificate"]
    );
    console.log(
      "create Warranty Certificate shareWarrantyCertOptionsMap",
      shareWarrantyCertOptionsMap
    );

    modifiedWarrantyData["caseId"] = srvcReqDetails?.title || "";
    let shareWarrantyCertValue =
      formData[srvcCustLabelToKeyMap["Share Warranty Certificate"]];
    console.log(
      "create Warranty Certificate shareWarrantyCertValue",
      shareWarrantyCertValue
    );
    createWarrantyCert =
      shareWarrantyCertValue == shareWarrantyCertOptionsMap["Yes"];
    hasClosedSubtsksFrWarrantyCert = productsTablearray.length > 0;

    if (createWarrantyCert && hasClosedSubtsksFrWarrantyCert) {
      console.log("create Warranty Certificate");
      // createWarrantyCert = true;

      const generatePDFResp = await generatePDF(modifiedWarrantyData);
      console.log("generatePDFResp", generatePDFResp);
      console.log("generatePDFResp", generatePDFResp?.resp);
      console.log("generatePDFResp", generatePDFResp?.resp?.data);
      const sourceBucket = {
        bucketName: "wifystaticbucket",
        filePath: "Warranty",
        fileName: generatePDFResp?.resp?.data?.fileName,
      };
      const destinationBucket = {
        bucketName: "tms-prod-media",
        filePath: "Warranty",
        fileName: generatePDFResp?.resp?.data?.fileName,
      };
      const copyToS3Resp = await copyToS3({ sourceBucket, destinationBucket });
      console.log("copyToS3Resp", copyToS3Resp?.resp?.data);

      const pdfUrl = getFinalLink(
        generatePDFResp?.resp?.data?.pdfUrl,
        "wifystaticbucket",
        true
      );
      console.log("pdfUrl", pdfUrl);

      finalGeneratePDFResp = {
        ...generatePDFResp?.resp,
        s3Key: copyToS3Resp?.resp?.data?.s3key,
        pdfUrl,
      };
      if (
        formData?.attachments?.[srvcCustLabelToKeyMap["Warranty Certificate"]]
      ) {
        console.log("formData?.attachments", formData?.attachments);
        modifiedSrvcReqFormData["attachments"] = formData?.attachments;
        console.log("modifiedSrvcReqFormData", modifiedSrvcReqFormData);
        modifiedSrvcReqFormData.attachments[
          srvcCustLabelToKeyMap["Warranty Certificate"]
        ] = [
          ...formData?.attachments?.[
            srvcCustLabelToKeyMap["Warranty Certificate"]
          ],
          finalGeneratePDFResp?.s3Key,
        ];
      } else {
        modifiedSrvcReqFormData.attachments = {
          [srvcCustLabelToKeyMap["Warranty Certificate"]]: [
            finalGeneratePDFResp?.s3Key,
          ],
        };
      }
      console.log("modifiedSrvcReqFormData", modifiedSrvcReqFormData);
    }
    console.log("finalGeneratePDFResp", finalGeneratePDFResp);

    responseStatus = true;
    responseMessage = "success";
  } catch (error) {
    console.error("Error generating PDF:", error);
    responseMessage = error.message;
  }

  return {
    status: responseStatus,
    message: responseMessage,
    data: {
      createWarrantyCert,
      hasClosedSubtsksFrWarrantyCert,
      generatePDFResp: finalGeneratePDFResp,
      modifiedSrvcReqFormData,
    },
  };
};
exports.handler = handler;
