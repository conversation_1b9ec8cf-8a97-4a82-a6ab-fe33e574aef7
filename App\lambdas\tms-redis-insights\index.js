const Redis = require('ioredis');
require('dotenv').config();

// Initialize Redis connection
const createRedisClient = () => {
  return new Redis({
    host: process.env.REDIS_HOST || 'your-redis-endpoint.amazonaws.com',
    port: process.env.REDIS_PORT || 6379,
    password: process.env.REDIS_PASSWORD || undefined,
    retryDelayOnFailover: 100,
    maxRetriesPerRequest: 3,
    lazyConnect: true,
  });
};

/**
 * Extract pattern from Redis key
 * @param {string} key - Redis key
 * @returns {string} - Extracted pattern
 */
function extractPattern(key) {
  // Split the key at the first underscore and take the prefix part
  const pattern = key.split('_')[0];
  return pattern || key; // Return original key if no underscore found
}

/**
 * Determine and count patterns in Redis keys
 * @param {Redis} redis - Redis client instance
 * @returns {Object} - Object containing pattern counts and statistics
 */
async function determineAndCountPatterns(redis) {
  let cursor = '0';
  let patternsCount = {};
  let totalKeys = 0;
  let processedKeys = 0;
  const batchSize = 1000; // Process keys in batches

  console.log('Starting Redis key pattern analysis...');

  try {
    do {
      const [newCursor, foundKeys] = await redis.scan(cursor, 'COUNT', batchSize);
      cursor = newCursor;
      totalKeys += foundKeys.length;

      // Process each key and extract its pattern
      foundKeys.forEach((key) => {
        const pattern = extractPattern(key);
        patternsCount[pattern] = patternsCount[pattern] ? patternsCount[pattern] + 1 : 1;
        processedKeys++;
      });

      // Log progress for large datasets
      if (processedKeys % 10000 === 0) {
        console.log(`Processed ${processedKeys} keys so far...`);
      }

    } while (cursor !== '0');

    console.log(`Analysis complete. Processed ${totalKeys} total keys.`);
    
    // Sort patterns by count (descending)
    const sortedPatterns = Object.entries(patternsCount)
      .sort(([,a], [,b]) => b - a)
      .reduce((r, [k, v]) => ({ ...r, [k]: v }), {});

    return {
      totalKeys,
      uniquePatterns: Object.keys(patternsCount).length,
      patterns: sortedPatterns,
      timestamp: new Date().toISOString()
    };

  } catch (error) {
    console.error('Error during pattern analysis:', error);
    throw error;
  }
}

/**
 * Get Redis memory usage information
 * @param {Redis} redis - Redis client instance
 * @returns {Object} - Memory usage statistics
 */
async function getRedisMemoryInfo(redis) {
  try {
    const info = await redis.info('memory');
    const memoryStats = {};
    
    info.split('\r\n').forEach(line => {
      if (line.includes(':')) {
        const [key, value] = line.split(':');
        if (key.startsWith('used_memory')) {
          memoryStats[key] = value;
        }
      }
    });

    return memoryStats;
  } catch (error) {
    console.error('Error getting memory info:', error);
    return {};
  }
}

/**
 * Get Redis general information
 * @param {Redis} redis - Redis client instance
 * @returns {Object} - General Redis statistics
 */
async function getRedisGeneralInfo(redis) {
  try {
    const info = await redis.info('server');
    const serverStats = {};
    
    info.split('\r\n').forEach(line => {
      if (line.includes(':')) {
        const [key, value] = line.split(':');
        if (['redis_version', 'uptime_in_seconds', 'uptime_in_days'].includes(key)) {
          serverStats[key] = value;
        }
      }
    });

    return serverStats;
  } catch (error) {
    console.error('Error getting server info:', error);
    return {};
  }
}

/**
 * Main Lambda handler function
 * @param {Object} event - Lambda event object
 * @returns {Object} - Response object with Redis insights
 */
exports.handler = async (event) => {
  const redis = createRedisClient();
  
  try {
    console.log('Connecting to Redis...');
    await redis.connect();
    console.log('Successfully connected to Redis');

    // Get pattern analysis
    const patternAnalysis = await determineAndCountPatterns(redis);
    
    // Get memory information
    const memoryInfo = await getRedisMemoryInfo(redis);
    
    // Get general server information
    const serverInfo = await getRedisGeneralInfo(redis);

    const response = {
      statusCode: 200,
      body: {
        success: true,
        data: {
          patternAnalysis,
          memoryInfo,
          serverInfo,
          analysisTimestamp: new Date().toISOString()
        }
      }
    };

    console.log('Redis insights analysis completed successfully');
    console.log('Pattern summary:', JSON.stringify(patternAnalysis, null, 2));
    
    return response;

  } catch (error) {
    console.error('Error in Redis insights analysis:', error);
    
    return {
      statusCode: 500,
      body: {
        success: false,
        error: error.message,
        timestamp: new Date().toISOString()
      }
    };
  } finally {
    // Always disconnect from Redis
    if (redis.status === 'ready') {
      await redis.disconnect();
      console.log('Disconnected from Redis');
    }
  }
};

// Export functions for testing
module.exports = {
  handler: exports.handler,
  extractPattern,
  determineAndCountPatterns,
  getRedisMemoryInfo,
  getRedisGeneralInfo,
  createRedisClient
};
