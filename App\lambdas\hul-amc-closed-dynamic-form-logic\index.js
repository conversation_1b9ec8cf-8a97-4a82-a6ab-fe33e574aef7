const modifyFieldInMeta = (meta,fieldKey,newKeyValueObj) => {
            
    meta.map((singleField,index) => {
        if(singleField.key == fieldKey){
            singleField = {
                ...singleField,
                ...newKeyValueObj
            }
            meta[index] = singleField
        }
    })
}

const handler = async(event) => {
    
    let responseStatus = false
    let responseMessage = "No response received from Netsuite 1";
    
    const {meta,allValues,changedValues,request_data,currentMeta} = event;
    
    console.log("event",event);
    
    const labelToKeyMap = {};
    
    meta.map(
        singleField => {
            labelToKeyMap[singleField.label || singleField.cust_component_value] = singleField.key
        }
    )

    const manipulatedFieldValues = {};

    let disableFormSubmissionButton = true;   

    console.log("meta-->>",JSON.stringify(meta))
    responseStatus = true;
    responseMessage = "success";
    const response = {
        status: responseStatus,
        message : responseMessage,
        data : {meta,allValues,changedValues,manipulatedFieldValues, disableFormSubmissionButton}
    };
    return response;
};

// handler({});

exports.handler = handler;