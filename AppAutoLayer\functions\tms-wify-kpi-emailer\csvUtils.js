/**
 * Convert array of objects to CSV format
 * @param {Array} data - Array of objects to convert
 * @returns {string} CSV formatted string
 */
function convertToCSV(data) {
  if (!data || data.length === 0) {
    return "";
  }

  // Get headers from the first row
  const headers = Object.keys(data[0]);

  // Create CSV header row
  const csvHeaders = headers.join(",");

  // Create CSV data rows
  const csvRows = data.map((row) => {
    return headers
      .map((header) => {
        const value = row[header];
        // Handle null/undefined values and escape quotes
        if (value === null || value === undefined) {
          return "";
        }
        // Convert to string and escape quotes
        const stringValue = String(value).replace(/"/g, '""');
        // Wrap in quotes if contains comma, quote, or newline
        if (
          stringValue.includes(",") ||
          stringValue.includes('"') ||
          stringValue.includes("\n")
        ) {
          return `"${stringValue}"`;
        }
        return stringValue;
      })
      .join(",");
  });

  // Combine headers and rows
  return [csvHeaders, ...csvRows].join("\n");
}

module.exports = {
  convertToCSV,
};
