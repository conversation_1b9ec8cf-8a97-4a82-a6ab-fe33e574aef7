import { Model, DataTypes, Optional } from 'sequelize';
import sequelize from '../config/sequelize';
import Capacity from './capacity';

// Define the attributes of the Booking model
interface BookingAttributes {
  bookingId: string;
  capacityId: bigint;
  requiredCapacity: number;
  orderId: string | null;
  createdAt: Date;
  updatedAt: Date;
}

// Define the attributes that can be null during creation
interface BookingCreationAttributes extends Optional<BookingAttributes, 'bookingId' | 'createdAt' | 'updatedAt' | 'orderId'> {}

// Define the Booking model class
class Booking extends Model<BookingAttributes, BookingCreationAttributes> implements BookingAttributes {
  public bookingId!: string;
  public capacityId!: bigint;
  public requiredCapacity!: number;
  public orderId!: string | null;
  public createdAt!: Date;
  public updatedAt!: Date;
}

// Initialize the Booking model
Booking.init(
  {
    bookingId: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
    },
    capacityId: {
      type: DataTypes.BIGINT,
      allowNull: false,
      references: {
        model: Capacity,
        key: 'id',
      },
    },
    requiredCapacity: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    orderId: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
    },
  },
  {
    sequelize,
    modelName: 'Booking',
    tableName: 'Bookings',
    timestamps: true,
    indexes: [
      {
        fields: ['capacityId'],
      },
      {
        fields: ['orderId'],
      },
    ],
  }
);

// Define associations
Booking.belongsTo(Capacity, { foreignKey: 'capacityId', as: 'capacity' });
Capacity.hasMany(Booking, { foreignKey: 'capacityId', as: 'bookings' });

export default Booking;
