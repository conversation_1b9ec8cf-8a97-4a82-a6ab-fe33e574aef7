// Mock the database connection for all tests
jest.mock("./src/config/database", () => ({
  __esModule: true,
  default: {
    transaction: jest.fn((callback) =>
      callback({ transaction: "mock-transaction" })
    ),
    query: jest.fn().mockResolvedValue([{ result: 1 }]),
  },
  testConnection: jest.fn().mockResolvedValue(true),
}));

// Suppress console errors during tests
console.error = jest.fn();
