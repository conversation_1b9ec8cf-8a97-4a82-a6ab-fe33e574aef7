import { Request, Response, NextFunction } from 'express';

/**
 * Authentication middleware to validate API key
 * @param req - Express request object
 * @param res - Express response object
 * @param next - Express next function
 */
export const authenticateApiKey = (req: Request, res: Response, next: NextFunction): void => {
  const apiKey = req.headers['x-api-key'];
  
  if (!apiKey || apiKey !== process.env.API_ACCESS_KEY) {
    res.status(401).json({
      status: false,
      message: 'Unauthorized: Invalid API key',
      timestamp: new Date().toISOString(),
    });
    return;
  }
  
  next();
};
