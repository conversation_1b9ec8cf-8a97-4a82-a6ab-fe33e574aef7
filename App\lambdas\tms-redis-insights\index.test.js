const AWS = require("aws-sdk");
const { 
  extractPattern, 
  determineAndCountPatterns, 
  getRedisMemoryInfo, 
  getRedisGeneralInfo,
  createRedisClient 
} = require("./index");
require("dotenv").config();

// Mock Redis for unit tests
const mockRedis = {
  scan: jest.fn(),
  info: jest.fn(),
  connect: jest.fn(),
  disconnect: jest.fn(),
  status: 'ready'
};

describe("TMS Redis Insights Lambda", () => {
  
  describe("Unit Tests", () => {
    
    describe("extractPattern", () => {
      it("should extract pattern from key with underscore", () => {
        expect(extractPattern("user_123")).toBe("user");
        expect(extractPattern("session_abc_def")).toBe("session");
        expect(extractPattern("cache_data_456")).toBe("cache");
      });

      it("should return original key if no underscore", () => {
        expect(extractPattern("simplekey")).toBe("simplekey");
        expect(extractPattern("123")).toBe("123");
      });

      it("should handle empty string", () => {
        expect(extractPattern("")).toBe("");
      });
    });

    describe("determineAndCountPatterns", () => {
      beforeEach(() => {
        jest.clearAllMocks();
      });

      it("should count patterns correctly", async () => {
        // Mock Redis scan responses
        mockRedis.scan
          .mockResolvedValueOnce(['10', ['user_1', 'user_2', 'session_a']])
          .mockResolvedValueOnce(['0', ['cache_x', 'user_3']]);

        const result = await determineAndCountPatterns(mockRedis);

        expect(result.totalKeys).toBe(5);
        expect(result.uniquePatterns).toBe(3);
        expect(result.patterns.user).toBe(3);
        expect(result.patterns.session).toBe(1);
        expect(result.patterns.cache).toBe(1);
        expect(result.timestamp).toBeDefined();
      });

      it("should handle empty Redis", async () => {
        mockRedis.scan.mockResolvedValueOnce(['0', []]);

        const result = await determineAndCountPatterns(mockRedis);

        expect(result.totalKeys).toBe(0);
        expect(result.uniquePatterns).toBe(0);
        expect(result.patterns).toEqual({});
      });

      it("should handle Redis scan error", async () => {
        mockRedis.scan.mockRejectedValueOnce(new Error("Redis connection failed"));

        await expect(determineAndCountPatterns(mockRedis)).rejects.toThrow("Redis connection failed");
      });
    });

    describe("getRedisMemoryInfo", () => {
      it("should parse memory info correctly", async () => {
        const mockMemoryInfo = "used_memory:1024\r\nused_memory_human:1K\r\nused_memory_rss:2048\r\nother_stat:value\r\n";
        mockRedis.info.mockResolvedValueOnce(mockMemoryInfo);

        const result = await getRedisMemoryInfo(mockRedis);

        expect(result.used_memory).toBe("1024");
        expect(result.used_memory_human).toBe("1K");
        expect(result.used_memory_rss).toBe("2048");
        expect(result.other_stat).toBeUndefined();
      });

      it("should handle info error gracefully", async () => {
        mockRedis.info.mockRejectedValueOnce(new Error("Info command failed"));

        const result = await getRedisMemoryInfo(mockRedis);

        expect(result).toEqual({});
      });
    });

    describe("getRedisGeneralInfo", () => {
      it("should parse server info correctly", async () => {
        const mockServerInfo = "redis_version:6.2.0\r\nuptime_in_seconds:3600\r\nuptime_in_days:1\r\nother_info:value\r\n";
        mockRedis.info.mockResolvedValueOnce(mockServerInfo);

        const result = await getRedisGeneralInfo(mockRedis);

        expect(result.redis_version).toBe("6.2.0");
        expect(result.uptime_in_seconds).toBe("3600");
        expect(result.uptime_in_days).toBe("1");
        expect(result.other_info).toBeUndefined();
      });
    });
  });

  describe("Integration Test", () => {
    // Configure AWS for actual Lambda invocation
    AWS.config.update({
      accessKeyId: process.env.AWS_ACCESS_KEY_ID || "",
      secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY || "",
      region: process.env.AWS_REGION || "ap-south-1",
    });

    const lambda = new AWS.Lambda();

    it("should invoke the actual Lambda function", async () => {
      const params = {
        FunctionName: "tms-redis-insights",
        InvocationType: "RequestResponse",
        Payload: JSON.stringify({}),
      };

      try {
        const response = await lambda.invoke(params).promise();
        expect(response.StatusCode).toBe(200);
        
        const payload = JSON.parse(response.Payload);
        expect(payload).toBeDefined();
        expect(payload.body).toBeDefined();
        
        if (payload.body.success) {
          expect(payload.body.data).toBeDefined();
          expect(payload.body.data.patternAnalysis).toBeDefined();
          expect(payload.body.data.patternAnalysis.totalKeys).toBeGreaterThanOrEqual(0);
        }
      } catch (error) {
        // If Lambda doesn't exist yet or Redis is not configured, that's expected
        console.log("Integration test skipped - Lambda may not be deployed yet:", error.message);
      }
    }, 60000); // 60 second timeout for Redis operations
  });
});
