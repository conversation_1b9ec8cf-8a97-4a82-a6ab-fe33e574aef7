import request from 'supertest';
import { app } from '../app';
import dotenv from 'dotenv';

// Load environment variables for testing
dotenv.config({ path: '.env.development' });

describe('API Endpoints', () => {
  // Health check endpoint
  it('GET /health should return 200 and healthy status', async () => {
    const res = await request(app).get('/health');
    expect(res.statusCode).toEqual(200);
    expect(res.body.status).toBeTruthy();
    expect(res.body.message).toEqual('Service is healthy');
  });

  // Test credentials endpoint - without API key
  it('GET /test-credentials without API key should return 401', async () => {
    const res = await request(app).get('/test-credentials');
    expect(res.statusCode).toEqual(401);
    expect(res.body.status).toBeFalsy();
  });

  // Test credentials endpoint - with valid API key
  it('GET /test-credentials with valid API key should return 200', async () => {
    const res = await request(app)
      .get('/test-credentials')
      .set('x-api-key', process.env.API_ACCESS_KEY || '');
    
    expect(res.statusCode).toEqual(200);
    expect(res.body.status).toBeTruthy();
    expect(res.body.message).toEqual('API credentials are valid');
  });
});
