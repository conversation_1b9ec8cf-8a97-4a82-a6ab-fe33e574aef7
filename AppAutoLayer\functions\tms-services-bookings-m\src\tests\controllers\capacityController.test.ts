// Mock Sequelize models before importing controllers
const mockCapacity = {
  findOne: jest.fn(),
  findByPk: jest.fn(),
  create: jest.fn(),
  update: jest.fn(),
  findAll: jest.fn(),
  bulkCreate: jest.fn(),
  count: jest.fn(),
  toJSON: jest.fn().mockReturnThis(),
};

// Mock the database connection
jest.mock("../../config/database", () => ({
  __esModule: true,
  default: {
    transaction: jest.fn((callback) =>
      callback({ transaction: "mock-transaction" })
    ),
    query: jest.fn().mockResolvedValue([{ result: 1 }]),
  },
  testConnection: jest.fn().mockResolvedValue(true),
}));

// Mock the sequelize import in models
jest.mock("../../models", () => {
  const mockSequelize = {
    transaction: jest.fn((callback) =>
      callback({ transaction: "mock-transaction" })
    ),
    query: jest.fn().mockResolvedValue([{ result: 1 }]),
  };

  return {
    __esModule: true,
    sequelize: mockSequelize,
    Capacity: mockCapacity,
  };
});

// Now import controllers after mocking
import {
  addCapacity,
  getCapacityById,
} from "../../controllers/capacityController";

// Mock the models
jest.mock("../../models/Capacity", () => ({
  __esModule: true,
  default: mockCapacity,
}));

jest.mock("../../models/Booking", () => ({
  __esModule: true,
  default: {
    findAll: jest.fn(),
    create: jest.fn(),
  },
}));

describe("Capacity Controller", () => {
  let req: any;
  let res: any;
  let jsonMock: jest.Mock;
  let statusMock: jest.Mock;

  beforeEach(() => {
    // Reset mocks
    jest.clearAllMocks();

    // Setup request and response mocks
    jsonMock = jest.fn().mockReturnThis();
    statusMock = jest.fn().mockReturnValue({ json: jsonMock });

    req = {
      body: {},
      params: {},
    };

    res = {
      status: statusMock,
      json: jsonMock,
    };
  });

  describe("addCapacity", () => {
    test("should return 400 if required fields are missing", async () => {
      // Arrange
      req.body = { resourceId: "resource-123" }; // Missing other required fields

      // Act
      await addCapacity(req, res);

      // Assert
      expect(statusMock).toHaveBeenCalledWith(400);
      expect(jsonMock).toHaveBeenCalledWith(
        expect.objectContaining({
          status: false,
          message: expect.stringContaining("Missing required fields"),
        })
      );
    });

    test("should return 400 if totalCapacity is negative", async () => {
      // Arrange
      req.body = {
        resourceId: "resource-123",
        startTime: "2023-07-20T09:00:00.000Z",
        endTime: "2023-07-20T17:00:00.000Z",
        totalCapacity: -1,
      };

      // Act
      await addCapacity(req, res);

      // Assert
      expect(statusMock).toHaveBeenCalledWith(400);
      expect(jsonMock).toHaveBeenCalledWith(
        expect.objectContaining({
          status: false,
          message: expect.stringContaining(
            "Total capacity must be a non-negative integer"
          ),
        })
      );
    });

    test("should return 400 if date format is invalid", async () => {
      // Arrange
      req.body = {
        resourceId: "resource-123",
        startTime: "invalid-date",
        endTime: "2023-07-20T17:00:00.000Z",
        totalCapacity: 10,
      };

      // Act
      await addCapacity(req, res);

      // Assert
      expect(statusMock).toHaveBeenCalledWith(400);
      expect(jsonMock).toHaveBeenCalledWith(
        expect.objectContaining({
          status: false,
          message: expect.stringContaining("Invalid date format"),
        })
      );
    });

    test("should return 400 if startTime is after endTime", async () => {
      // Arrange
      req.body = {
        resourceId: "resource-123",
        startTime: "2023-07-20T18:00:00.000Z", // After endTime
        endTime: "2023-07-20T17:00:00.000Z",
        totalCapacity: 10,
      };

      // Act
      await addCapacity(req, res);

      // Assert
      expect(statusMock).toHaveBeenCalledWith(400);
      expect(jsonMock).toHaveBeenCalledWith(
        expect.objectContaining({
          status: false,
          message: expect.stringContaining("startTime must be before endTime"),
        })
      );
    });

    test("should update capacity and return 200 if capacity already exists", async () => {
      // Arrange
      req.body = {
        resourceId: "resource-123",
        startTime: "2023-07-20T09:00:00.000Z",
        endTime: "2023-07-20T17:00:00.000Z",
        totalCapacity: 15, // New capacity value
      };

      // Mock existing capacity
      const existingCapacity = {
        id: BigInt(1),
        resourceId: "resource-123",
        startTime: new Date("2023-07-20T09:00:00.000Z"),
        endTime: new Date("2023-07-20T17:00:00.000Z"),
        totalCapacity: 10,
        availableCapacity: 10,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      // Mock updated capacity
      const updatedCapacity = {
        ...existingCapacity,
        totalCapacity: 15,
        availableCapacity: 15,
        updatedAt: new Date(),
      };

      // Create a save mock function that we can track
      const saveMock = jest.fn().mockResolvedValue(updatedCapacity);

      // Mock Sequelize findOne to return an existing capacity
      mockCapacity.findOne.mockResolvedValue({
        ...existingCapacity,
        toJSON: () => existingCapacity,
        save: saveMock,
      });

      // Mock Sequelize update to return the updated capacity
      mockCapacity.update.mockResolvedValue({
        ...updatedCapacity,
        toJSON: () => updatedCapacity,
      });

      // Act
      await addCapacity(req, res);

      // Assert
      expect(mockCapacity.findOne).toHaveBeenCalledWith({
        where: {
          resourceId: "resource-123",
          startTime: expect.any(Date),
          endTime: expect.any(Date),
        },
      });

      // Verify that save was called on the existingCapacity object
      expect(saveMock).toHaveBeenCalled();

      expect(statusMock).toHaveBeenCalledWith(200);
      expect(jsonMock).toHaveBeenCalledWith(
        expect.objectContaining({
          status: true,
          message: expect.stringContaining("Capacity updated successfully"),
          data: expect.any(Object),
          timestamp: expect.any(String),
        })
      );
    });

    test("should create capacity and return 201 if all validations pass", async () => {
      // Arrange
      const capacityData = {
        resourceId: "resource-123",
        startTime: "2023-07-20T09:00:00.000Z",
        endTime: "2023-07-20T17:00:00.000Z",
        totalCapacity: 10,
      };

      req.body = capacityData;

      // Mock Sequelize findOne to return null (no existing capacity)
      mockCapacity.findOne.mockResolvedValue(null);

      // Mock Sequelize create to return the created capacity
      const createdCapacity = {
        id: BigInt(1),
        resourceId: capacityData.resourceId,
        startTime: new Date(capacityData.startTime),
        endTime: new Date(capacityData.endTime),
        totalCapacity: capacityData.totalCapacity,
        availableCapacity: capacityData.totalCapacity,
        createdAt: new Date(),
        updatedAt: new Date(),
      };
      mockCapacity.create.mockResolvedValue({
        ...createdCapacity,
        toJSON: () => createdCapacity,
      });

      // Act
      await addCapacity(req, res);

      // Assert
      expect(mockCapacity.findOne).toHaveBeenCalledWith({
        where: {
          resourceId: capacityData.resourceId,
          startTime: expect.any(Date),
          endTime: expect.any(Date),
        },
      });

      expect(mockCapacity.create).toHaveBeenCalledWith({
        resourceId: capacityData.resourceId,
        startTime: expect.any(Date),
        endTime: expect.any(Date),
        totalCapacity: capacityData.totalCapacity,
        availableCapacity: capacityData.totalCapacity,
      });

      expect(statusMock).toHaveBeenCalledWith(201);
      expect(jsonMock).toHaveBeenCalledWith(
        expect.objectContaining({
          status: true,
          message: expect.stringContaining("Capacity added successfully"),
          data: expect.any(Object),
          timestamp: expect.any(String),
        })
      );
    });

    test("should return 500 if an error occurs", async () => {
      // Arrange
      req.body = {
        resourceId: "resource-123",
        startTime: "2023-07-20T09:00:00.000Z",
        endTime: "2023-07-20T17:00:00.000Z",
        totalCapacity: 10,
      };

      // Mock Sequelize findOne to throw an error
      const error = new Error("Database error");
      mockCapacity.findOne.mockRejectedValue(error);

      // Mock console.error to prevent error output in tests
      jest.spyOn(console, "error").mockImplementation(() => {});

      // Act
      await addCapacity(req, res);

      // Assert
      expect(statusMock).toHaveBeenCalledWith(500);
      expect(jsonMock).toHaveBeenCalledWith(
        expect.objectContaining({
          status: false,
          message: "Internal server error",
        })
      );
    });
  });

  describe("getCapacityById", () => {
    test("should return 404 if capacity is not found", async () => {
      // Arrange
      req.params = { id: "999" };

      // Mock Sequelize findByPk to return null
      mockCapacity.findByPk.mockResolvedValue(null);

      // Act
      await getCapacityById(req, res);

      // Assert
      expect(mockCapacity.findByPk).toHaveBeenCalledWith(BigInt(999));
      expect(statusMock).toHaveBeenCalledWith(404);
      expect(jsonMock).toHaveBeenCalledWith(
        expect.objectContaining({
          status: false,
          message: "Capacity not found",
        })
      );
    });

    test("should return capacity if found", async () => {
      // Arrange
      req.params = { id: "1" };

      // Mock Sequelize findByPk to return a capacity
      const capacity = {
        id: BigInt(1),
        resourceId: "resource-123",
        startTime: new Date("2023-07-20T09:00:00.000Z"),
        endTime: new Date("2023-07-20T17:00:00.000Z"),
        totalCapacity: 10,
        availableCapacity: 10,
        createdAt: new Date(),
        updatedAt: new Date(),
        toJSON: function () {
          return this;
        },
      };
      mockCapacity.findByPk.mockResolvedValue(capacity);

      // Act
      await getCapacityById(req, res);

      // Assert
      expect(mockCapacity.findByPk).toHaveBeenCalledWith(BigInt(1));
      expect(statusMock).toHaveBeenCalledWith(200);
      expect(jsonMock).toHaveBeenCalledWith(
        expect.objectContaining({
          status: true,
          data: expect.any(Object),
          timestamp: expect.any(String),
        })
      );
    });

    test("should return 500 if an error occurs", async () => {
      // Arrange
      req.params = { id: "1" };

      // Mock Sequelize findByPk to throw an error
      const error = new Error("Database error");
      mockCapacity.findByPk.mockRejectedValue(error);

      // Mock console.error to prevent error output in tests
      jest.spyOn(console, "error").mockImplementation(() => {});

      // Act
      await getCapacityById(req, res);

      // Assert
      expect(statusMock).toHaveBeenCalledWith(500);
      expect(jsonMock).toHaveBeenCalledWith(
        expect.objectContaining({
          status: false,
          message: "Internal server error",
        })
      );
    });
  });
});
