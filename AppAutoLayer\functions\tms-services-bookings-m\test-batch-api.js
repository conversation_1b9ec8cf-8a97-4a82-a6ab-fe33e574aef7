// Test script to test batch capacity API
const { app } = require('./dist/app');
const request = require('supertest');

// API key for authentication
const API_KEY = 'tms-bookings-service-dev-key-123456';

// Run tests
async function runTests() {
  console.log('=== Testing Batch Capacity API ===');
  
  try {
    // Test batch capacity endpoint
    console.log('\nTesting batch capacity endpoint...');
    
    // Create batch data with multiple capacity records
    const batchData = {
      capacityRecords: [
        {
          resourceId: 'batch-resource-1',
          startTime: new Date().toISOString(),
          endTime: new Date(Date.now() + 3600000).toISOString(), // 1 hour later
          totalCapacity: 5
        },
        {
          resourceId: 'batch-resource-2',
          startTime: new Date().toISOString(),
          endTime: new Date(Date.now() + 3600000).toISOString(), // 1 hour later
          totalCapacity: 10
        },
        {
          resourceId: 'batch-resource-3',
          startTime: new Date().toISOString(),
          endTime: new Date(Date.now() + 3600000).toISOString(), // 1 hour later
          totalCapacity: 15
        }
      ]
    };
    
    const batchResponse = await request(app)
      .post('/capacity/batch')
      .set('x-api-key', API_KEY)
      .send(batchData);
    
    console.log('Status:', batchResponse.status);
    console.log('Response:', batchResponse.body);
    
    console.log('\nBatch test completed successfully!');
  } catch (error) {
    console.error('Error during batch test:', error);
  }
}

// Run the tests
runTests();
