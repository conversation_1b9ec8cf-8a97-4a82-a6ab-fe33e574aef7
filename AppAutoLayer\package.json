{"name": "app-auto-layer", "version": "1.0.0", "description": "Lambda functions with automated layer management", "main": "deploy.js", "scripts": {"deploy-prod": "node deploy.js -e prod", "deploy-dev": "node deploy.js -e dev", "watch-dev": "NODE_ENV=dev nodemon --ignore temp/ --ignore \"**/node_modules/**\" --ext js,ts,json,prisma --exec \"node watch.js\"", "watch-prod": "NODE_ENV=prod nodemon --ignore temp/ --ignore \"**/node_modules/**\" --ext js,ts,json,prisma --exec \"node watch.js\""}, "keywords": ["lambda", "layers", "aws"], "author": "Wify", "license": "ISC", "dependencies": {"@aws-sdk/client-lambda": "^3.787.0", "aws-sdk": "^2.1687.0", "chalk": "^4.1.2", "commander": "^11.0.0", "dotenv": "^16.5.0", "minimist": "^1.2.8", "nodemon": "^3.1.10", "zip-lib": "^1.1.2"}}